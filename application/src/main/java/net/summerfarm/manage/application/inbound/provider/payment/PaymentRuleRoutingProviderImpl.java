
package net.summerfarm.manage.application.inbound.provider.payment;

import net.summerfarm.client.provider.payment.PaymentRuleRoutingProvider;
import net.summerfarm.client.req.payment.PaymentRoutingQueryReq;
import net.summerfarm.client.resp.payment.PaymentRoutingQueryResp;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO;
import net.summerfarm.payment.routing.service.PaymentRuleRoutingService;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Reference;

import javax.annotation.Resource;

/**
 * @description: 支付路由规则服务实现
 * @author: Gemini
 * @date: 2025-08-15
 */
@DubboService
public class PaymentRuleRoutingProviderImpl implements PaymentRuleRoutingProvider {

    @Resource
    private PaymentRuleRoutingService paymentRuleRoutingService;

    @Override
    public DubboResponse<PaymentRoutingQueryResp> getRoutingInfo(PaymentRoutingQueryReq req) {
        if (req == null) {
            throw new ParamsException("请求路由参数不能为空");
        }

        // 1. Convert client Req to SDK DTO
        PaymentRoutingQueryDTO sdkQueryDTO = convertReqToSdkDto(req);

        // 2. Call SDK service
        PaymentRoutingDTO sdkRoutingDTO = paymentRuleRoutingService.getRoutingInfo(sdkQueryDTO);

        // 3. Convert SDK DTO to client Resp
        PaymentRoutingQueryResp paymentRoutingQueryResp = convertSdkDtoToResp(sdkRoutingDTO);
        return DubboResponse.getOK(paymentRoutingQueryResp);
    }

    private PaymentRoutingQueryDTO convertReqToSdkDto(PaymentRoutingQueryReq req) {
        PaymentRoutingQueryDTO.PaymentRoutingQueryDTOBuilder builder = PaymentRoutingQueryDTO.builder();

        builder.tenantId(req.getTenantId())
                .businessLine(req.getBusinessLine())
                .platform(req.getPlatform())
                .routeKey(req.getRouteKey())
                .routeValue(req.getRouteValue())
                .paymentMethod(req.getPaymentMethod())
                .mId(req.getMId())
                .openId(req.getOpenId())
                .b2bSwitch(req.getB2bSwitch())
                .sellingEntityName(req.getSellingEntityName());

        return builder.build();
    }

    private PaymentRoutingQueryResp convertSdkDtoToResp(PaymentRoutingDTO sdkDto) {
        PaymentRoutingQueryResp resp = new PaymentRoutingQueryResp();
        if (sdkDto == null) {
            return resp;
        }

        // The SDK returns an Integer for channelCode, converting to String for safety.
        if (sdkDto.getChannelCode() != null) {
            resp.setChannelCode(String.valueOf(sdkDto.getChannelCode()));
        }

        resp.setChannelId(sdkDto.getChannelId());
        resp.setCompanyAccountId(sdkDto.getCompanyAccountId());
        resp.setSellingEntityName(sdkDto.getSellingEntityName());
        resp.setChannelName(sdkDto.getChannelName());
        resp.setMerchantNo(sdkDto.getMerchantNo());
        resp.setAppId(sdkDto.getAppId());
        resp.setAppSecret(sdkDto.getAppSecret());
        resp.setCertPath(sdkDto.getCertPath());
        resp.setUserId(sdkDto.getUserId());
        resp.setPublicKey(sdkDto.getPublicKey());
        resp.setPrivateKey(sdkDto.getPrivateKey());
        resp.setSecret(sdkDto.getSecret());
        resp.setAuthCode(sdkDto.getAuthCode());
        resp.setRefundPassword(sdkDto.getRefundPassword());
        resp.setOperatorAccount(sdkDto.getOperatorAccount());
        return resp;
    }
}
