package net.summerfarm.manage.application.util;

import com.google.common.collect.Sets;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.repository.ConfigQueryRepository;
import net.xianmu.authentication.common.utils.SpringContextUtil;

@Slf4j
public class UserRoleUtils {

    /**
     * 超管角色id
     */
    private static final Set<Integer> SA_ROLE_ID = Sets.newHashSet(1);
    /**
     * BD角色id
     */
    private static final Set<Integer> BD_ROLE_ID = Sets.newHashSet(5);
    /**
     * 采购角色id
     */
    private static final Set<Integer> PURCHASE_ROLE_ID = Sets.newHashSet(9, 70, 71, 72, 73);
    /**
     * 运营角色id
     */
    private static final Set<Integer> OPERATE_ROLE_ID = Sets.newHashSet(4, 54, 65, 66, 67, 68, 69, 80, 81, 82, 83);
    /**
     * 大客户角色id
     */
    private static final Set<Integer> MAJOR_ROLE_ID = Sets.newHashSet(14);
    /**
     * 区域超管角色id
     */
    private static final Set<Integer> AREA_SA_ROLE_ID = Sets.newHashSet(13, 89);
    /**
     * 城市合伙人角色id
     */
    private static final Set<Integer> PARTNER_SA_ROLE_ID = Sets.newHashSet(12);
    /**
     * 销售主管角色id
     */
    private static final Set<Integer> SALE_SA_ROLE_ID = Sets.newHashSet(20, 74, 92);

    /**
     * 返回当前登录用户是否是超级管理员
     *
     * @return
     */
    public static boolean isCurrentUserSA() {
        Set<Integer> roleIds = UserInfoHolder.getCurrentUserRoleIds();
        for (Integer roleId : roleIds) {
            if (SA_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回当前登录用户是否是销售管理员(M1/M2/M3等)
     *
     * @return
     */
    public static boolean isCurrentUserSalesSA() {
        Set<Integer> roleIds = UserInfoHolder.getCurrentUserRoleIds();
        for (Integer roleId : roleIds) {
            if (SALE_SA_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回当前登录用户是否是“大客户”
     *
     * @return
     */
    public static boolean isCurrentUserMajorAdmin() {
        Set<Integer> roleIds = UserInfoHolder.getCurrentUserRoleIds();
        for (Integer roleId : roleIds) {
            if (MAJOR_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回当前登录用户是否是BD(普通销售员)
     *
     * @return
     */
    public static boolean isCurrentUserBD() {
        Set<Integer> roleIds = UserInfoHolder.getCurrentUserRoleIds();
        for (Integer roleId : roleIds) {
            if (BD_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    private static final String R_D_KEY = "R_D_KEY";

    /**
     * @return 返回当前用户是否是研发(RD)
     */
    public static boolean isCurrentUserRD() {
        ConfigEntity config = SpringContextUtil.getBean("configQueryRepositoryImpl", ConfigQueryRepository.class).selectByKey(R_D_KEY);
        if (null == config) {
            log.error("未配置Key:{}", R_D_KEY);
            return false;
        }
        Set<Integer> rdRoleId = Arrays.stream(config.getValue().split(";")).map(Integer::valueOf).collect(Collectors.toSet());
        log.info("R_D_KEY:{}", rdRoleId);
        Set<Integer> adminRoleVOs = UserInfoHolder.getCurrentUserRoleIds();
        for (Integer adminRoleVO : adminRoleVOs) {
            if (rdRoleId.contains(adminRoleVO)) {
                return true;
            }
        }
        return false;
    }
}
