package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Data
public class ExternalProductMappingQueryInput extends BasePageInput implements Serializable{

	/**
	 * 三级类目id
	 */
	private Long categoryId;

	/**
	 * 商品名称
	 */
	private String title;

	/**
	 * 买手id
	 */
	private Long buyerId;

	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 映射类型 1-sku 2-类目
	 */
	private Integer type;

	/**
	 * 鲜沐内部值
	 */
	private String internalValue;

	/**
	 * 外部平台值
	 */
	private String externalValue;

	/**
	 * 鲜沐sku编码
	 */
	private String xmSkuCode;

}