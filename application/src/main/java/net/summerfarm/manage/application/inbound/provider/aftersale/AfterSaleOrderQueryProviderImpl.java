package net.summerfarm.manage.application.inbound.provider.aftersale;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.aftesale.AfterSaleOrderQueryProvider;
import net.summerfarm.client.req.aftersale.AfterSaleDeliveryPathDetailQueryReq;
import net.summerfarm.client.resp.aftersale.AfterSaleDeliveryPathInfoResp;
import net.summerfarm.manage.application.inbound.provider.aftersale.converter.AfterSaleOrderConverter;
import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;
import net.summerfarm.manage.domain.afterSale.repository.AfterSaleDeliveryPathQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 售后单查询<br/>
 * date: 2024/12/30 15:42<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
@Component
public class AfterSaleOrderQueryProviderImpl implements AfterSaleOrderQueryProvider {

    @Resource
    private AfterSaleDeliveryPathQueryRepository afterSaleDeliveryPathQueryRepository;

    @Override
    public DubboResponse<List<AfterSaleDeliveryPathInfoResp>> queryValidAfterSaleDeliveryPathDetail(@Valid AfterSaleDeliveryPathDetailQueryReq req) {
        List<String> afterSaleNoList = req.getAfterSaleNoList();
        List<AfterSaleDeliveryPathFlatObject> flatObjectList = afterSaleDeliveryPathQueryRepository.queryValidAfterSaleDeliveryPathDetail(afterSaleNoList);
        return DubboResponse.getOK(flatObjectList.stream().map(AfterSaleOrderConverter::afterSaleDeliveryPathFlatObjectToResp).collect(Collectors.toList()));
    }
}
