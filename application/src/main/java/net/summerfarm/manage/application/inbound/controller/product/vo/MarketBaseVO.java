package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商品spu 基础信息vo
 * 禁止字段扩展
 */
@Data
public class MarketBaseVO implements Serializable {
    private static final long serialVersionUID = -71520281877228448L;
    /**
     * primary key
     */
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
}
