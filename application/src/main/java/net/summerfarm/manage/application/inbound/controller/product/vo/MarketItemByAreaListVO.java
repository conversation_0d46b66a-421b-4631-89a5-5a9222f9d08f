package net.summerfarm.manage.application.inbound.controller.product.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class MarketItemByAreaListVO extends MarketItemBaseVO implements Serializable {
    /**
     * 运营大区
     */
    private Integer largeAreaNo;
    /**
     * 0下架 1上架
     */
    private Integer onSale;
    /**
     * 城市
     */
    private Integer areaNo;
    /**
     * 城市名称
     */
    private Integer areaName;

    /**
     * 采购成本
     */
    private BigDecimal cost;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 商城售价
     */
    private BigDecimal mallPrice;
    /**
     * 可用库存
     */
    private Integer availableStock;
}
