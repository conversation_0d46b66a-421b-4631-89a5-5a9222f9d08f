package net.summerfarm.manage.application.inbound.provider.product.mall;


import net.summerfarm.client.provider.product.mall.MajorPriceCommandProvider;
import net.summerfarm.manage.application.service.product.mall.MajorPriceCommandService;

import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * @date 2024-04-09 18:04:40
 * @version 1.0
 *
 */
@DubboService
public class MajorPriceCommandProviderImpl implements MajorPriceCommandProvider {

    @Autowired
    private MajorPriceCommandService majorPriceCommandService;


    @Override
    public DubboResponse<Void> newLowPriceRemainder(Integer adminId, Integer areaNo, String sku) {
        majorPriceCommandService.newLowPriceRemainder(adminId, areaNo, sku);
        return DubboResponse.getOK(null);
    }
}
