package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class SkuAreaWarehouseNoMapVO {
    /**
     * <sku,areanos>
     */
    Map<String, Set<Integer>> skuAreaNoMap;
    /**
     * <sku_areano,warehouseNo>
     */
    Map<String, Integer> sku_AreaWarehouseNoMap;

    Map<Integer, Set<Integer>> largeAreaAreaNoMap;

    Map<Integer, AreaSimpleEntity> areaMap;

    Map<String, List<AreaSkuEntity>> skuAreaSkuMap;
}
