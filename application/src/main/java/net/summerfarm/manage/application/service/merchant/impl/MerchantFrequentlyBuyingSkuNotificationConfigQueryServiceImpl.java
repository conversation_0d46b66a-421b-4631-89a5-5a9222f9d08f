package net.summerfarm.manage.application.service.merchant.impl;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.enums.InventoryAdjustPriceTypeEnum;
import net.summerfarm.manage.application.inbound.controller.merchant.assembler.MerchantFrequentlyBuyingSkuNotificationConfigAssembler;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryInput;
import net.summerfarm.manage.application.service.merchant.MerchantFrequentlyBuyingSkuNotificationConfigQueryService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.constants.Conf;
import net.summerfarm.manage.common.constants.RedisKeyConstants;
import net.summerfarm.manage.common.enums.CommonStatus;
import net.summerfarm.manage.common.enums.activity.ScopeTypeEnum;
import net.summerfarm.manage.common.input.wx.ActivityNotice;
import net.summerfarm.manage.common.msg.ActivityNoticeMsg;
import net.summerfarm.manage.common.util.WeChatUtils;
import net.summerfarm.manage.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.summerfarm.manage.domain.activity.valueObject.ActivityLadderPriceValueObject;
import net.summerfarm.manage.domain.activity.valueObject.ActivitySkuDetailValueObject;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam;
import net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuQueryParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuNotificationConfigQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolDetailEntity;
import net.summerfarm.manage.domain.merchantpool.repository.MerchantPoolDetailRepository;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.fence.FenceQueryFacade;
import net.summerfarm.manage.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.manage.facade.message.WxSendMessageFacade;
import net.summerfarm.manage.facade.message.input.SendMessageInput;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
@Service
@Slf4j
public class MerchantFrequentlyBuyingSkuNotificationConfigQueryServiceImpl implements MerchantFrequentlyBuyingSkuNotificationConfigQueryService {

    @Autowired
    private MerchantFrequentlyBuyingSkuNotificationConfigQueryRepository merchantFrequentlyBuyingSkuNotificationConfigQueryRepository;

    @Resource
    private ActivitySkuDetailQueryRepository activitySkuDetailQueryRepository;

    @Resource
    private MerchantPoolDetailRepository merchantPoolDetailRepository;

    @Resource
    private MerchantFrequentlyBuyingSkuQueryRepository merchantFrequentlyBuyingSkuQueryRepository;

    @Resource
    private MerchantQueryRepository merchantQueryRepository;

    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    @Resource
    private WxSendMessageFacade wxSendMessageFacade;

    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;

    @Resource
    private FenceQueryFacade fenceQueryFacade;

    @Resource
    private ProductCostQueryFacade productCostQueryFacade;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public PageInfo<MerchantFrequentlyBuyingSkuNotificationConfigEntity> getPage(MerchantFrequentlyBuyingSkuNotificationConfigQueryInput input) {
        MerchantFrequentlyBuyingSkuNotificationConfigQueryParam queryParam = MerchantFrequentlyBuyingSkuNotificationConfigAssembler.toMerchantFrequentlyBuyingSkuNotificationConfigQueryParam(input);
        return merchantFrequentlyBuyingSkuNotificationConfigQueryRepository.getPage(queryParam);
    }

    @Override
    public MerchantFrequentlyBuyingSkuNotificationConfigEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return merchantFrequentlyBuyingSkuNotificationConfigQueryRepository.selectById(id);
    }

    @Override
    public void notificationToMerchant(Long activityId) {
        String startTimeString = redisTemplate.opsForValue().get(RedisKeyConstants.ACTIVITY_NOTICE_END_TIME_KEY);

        //默认为当天00:00:00开始
        LocalDateTime startTime = BaseDateUtils.getDayStart(LocalDateTime.now());
        if (!StringUtils.isEmpty(startTimeString)) {
            startTime = JSON.parseObject(startTimeString, LocalDateTime.class);
        }

        LocalDateTime endTime = LocalDateTime.now();
        ActivitySkuDetailQueryParam param = new ActivitySkuDetailQueryParam();
        if (Objects.isNull(activityId)) {
            param.setStartTime(startTime);
            param.setEndTime(endTime);
            param.setIsPermanent(CommonStatus.NO.getCode());
            param.setScopeType(ScopeTypeEnum.MERCHANT_POOL.getCode());

            //设置活动结束时间 下次定时任务用这个时间做为活动开始时间
            redisTemplate.opsForValue ().set (RedisKeyConstants.ACTIVITY_NOTICE_END_TIME_KEY, JSON.toJSONString (endTime), 1, TimeUnit.DAYS);
        } else {
            param.setActivityId(activityId);
        }

        List<ActivitySkuDetailValueObject> activitySkuDetailEntities = activitySkuDetailQueryRepository.selectByConditionV2(param);
        if (CollectionUtils.isEmpty(activitySkuDetailEntities)) {
            log.info("特价活动数据为空");
            return;
        }

        Set<Long> scopeIds = activitySkuDetailEntities.stream().map(ActivitySkuDetailValueObject::getScopeId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(scopeIds)) {
            log.info("特价活动人群包数据为空");
            return;
        }
        List<MerchantPoolDetailEntity> merchantPoolDetailEntities = merchantPoolDetailRepository.selectByPoolInfoIds(scopeIds);
        if (CollectionUtils.isEmpty(merchantPoolDetailEntities)) {
            log.info("人群包详情数据为空");
            return;
        }

        //查询门店消息提醒
        Map<Long, Set<Long>> merchantPoolMap = merchantPoolDetailEntities.stream().collect(Collectors.groupingBy(MerchantPoolDetailEntity::getMId, Collectors.mapping(MerchantPoolDetailEntity::getPoolInfoId, Collectors.toSet())));
        Set<Long> mIds = merchantPoolMap.keySet().stream().collect(Collectors.toSet());
        MerchantFrequentlyBuyingSkuNotificationConfigQueryParam queryParam = new MerchantFrequentlyBuyingSkuNotificationConfigQueryParam();
        queryParam.setMIds(mIds);
        List<MerchantFrequentlyBuyingSkuNotificationConfigEntity> notificationConfigEntities = merchantFrequentlyBuyingSkuNotificationConfigQueryRepository.selectByCondition(queryParam);
        notificationConfigEntities = notificationConfigEntities.stream().filter(entity -> Objects.nonNull(entity.getSpecialOffer()) &&
                Objects.equals(entity.getSpecialOffer(), CommonStatus.YES.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notificationConfigEntities)) {
            log.info("门店常购清单配置数据为空");
            return;
        }
        mIds = notificationConfigEntities.stream().map(MerchantFrequentlyBuyingSkuNotificationConfigEntity::getMId).collect(Collectors.toSet());

        //查询门店采购清单sku信息
        Set<String> skus = activitySkuDetailEntities.stream().map(ActivitySkuDetailValueObject::getSku).collect(Collectors.toSet());
        MerchantFrequentlyBuyingSkuQueryParam merchantFrequentlyBuyingSkuQueryParam = new MerchantFrequentlyBuyingSkuQueryParam();
        merchantFrequentlyBuyingSkuQueryParam.setSkus(skus);
        merchantFrequentlyBuyingSkuQueryParam.setMIds(mIds);
        List<MerchantFrequentlyBuyingSkuEntity> buyingSkuEntities = merchantFrequentlyBuyingSkuQueryRepository.selectByCondition(merchantFrequentlyBuyingSkuQueryParam);
        if (CollectionUtils.isEmpty(buyingSkuEntities)) {
            log.info("门店常购清单数据为空");
            return;
        }
        Map<Long, List<MerchantFrequentlyBuyingSkuEntity>> buyingSkuMap = buyingSkuEntities.stream().collect(Collectors.groupingBy(MerchantFrequentlyBuyingSkuEntity::getMId));

        //获取门店信息
        List<MerchantEntity> merchantEntities = merchantQueryRepository.selectByIds(new ArrayList<>(mIds));
        List<Integer> areaNoList = merchantEntities.stream().map(MerchantEntity::getAreaNo).collect(Collectors.toList());
        Map<Long, MerchantEntity> merchantEntityMap = merchantEntities.stream().collect(Collectors.toMap(MerchantEntity::getMId, Function.identity(), (x1, x2) -> x1));

        //查询sku售卖价信息
        List<String> skuList = new ArrayList<>(skus);
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice(skuList, areaNoList, null);
        Map<String, AreaSkuEntity> areaSkuEntityMap = areaSkuEntities.stream().collect(Collectors.toMap(entity -> entity.getSku() + "_" + entity.getAreaNo(),
                Function.identity(), (x1, x2) -> x1));

        //获取商品信息
        InventoryQueryParam inventoryQueryParam = new InventoryQueryParam();
        inventoryQueryParam.setSkuList(skuList);
        List<InventoryEntity> inventoryEntities = inventoryQueryRepository.queryInfo(inventoryQueryParam);
        Map<String, InventoryEntity> inventoryEntityMap = inventoryEntities.stream().collect(Collectors.toMap(InventoryEntity::getSku,
                Function.identity(), (x1, x2) -> x1));

        //给开启特价活动人群发送公众号消息
        Map<Long, List<ActivitySkuDetailValueObject>> skuDetailMap = activitySkuDetailEntities.stream().collect(Collectors.groupingBy(ActivitySkuDetailValueObject::getScopeId));
        for (MerchantFrequentlyBuyingSkuNotificationConfigEntity notificationConfigEntity : notificationConfigEntities) {
            try {
                MerchantEntity merchantEntity = merchantEntityMap.get(notificationConfigEntity.getMId());
                if (merchantEntity == null || merchantEntity.getOpenid() == null) {
                    log.info("当前用户信息为空！mId:{}", notificationConfigEntity.getMId());
                    continue;
                }

                List<MerchantFrequentlyBuyingSkuEntity> buyingSkuEntityList = buyingSkuMap.get(notificationConfigEntity.getMId());
                if (CollectionUtils.isEmpty(buyingSkuEntityList)) {
                    log.info("根据门店ID获取常购清单为空！mId:{}", notificationConfigEntity.getMId());
                    continue;
                }

                //根据人群包ID获取活动sku信息
                Set<Long> scopeIdSet = merchantPoolMap.get(notificationConfigEntity.getMId());
                List<ActivitySkuDetailValueObject> allActivitySkuDetailValueObjects = new ArrayList<>();
                List<ActivitySkuDetailValueObject> finalAllActivitySkuDetailValueObjects = allActivitySkuDetailValueObjects;
                scopeIdSet.forEach(scopeId -> {
                    List<ActivitySkuDetailValueObject> activitySkuDetailValueObjects = skuDetailMap.get(scopeId);
                    if (CollectionUtils.isEmpty(activitySkuDetailValueObjects)) {
                        return;
                    }
                    finalAllActivitySkuDetailValueObjects.addAll(activitySkuDetailValueObjects);
                });
                if (CollectionUtils.isEmpty(allActivitySkuDetailValueObjects)) {
                    log.info("根据人群包ID获取活动sku信息为空, mId:{}", notificationConfigEntity.getMId());
                    continue;
                }

                //过滤出不在采购清单的sku信息
                Set<String> buyingSkus = buyingSkuEntityList.stream().map(MerchantFrequentlyBuyingSkuEntity::getSku).collect(Collectors.toSet());
                allActivitySkuDetailValueObjects = allActivitySkuDetailValueObjects.stream().filter(activitySkuDetailValueObject ->
                        buyingSkus.contains(activitySkuDetailValueObject.getSku())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(allActivitySkuDetailValueObjects)) {
                    log.info("根据采购单sku过滤活动sku信息为空, mId:{}", notificationConfigEntity.getMId());
                    continue;
                }
                log.info("当前门店需要提醒的活动信息, mId:{}, allActivitySkuDetailValueObjects:{}", notificationConfigEntity.getMId(), JSON.toJSONString(allActivitySkuDetailValueObjects));

                //发送公众号消息
                sendMsgToMerchant(merchantEntity, areaSkuEntityMap, inventoryEntityMap, allActivitySkuDetailValueObjects);
            } catch (Exception e) {
                log.warn("发送公众号消息提醒通知异常, notificationConfigEntity:{}, cause:{}", JSON.toJSONString(notificationConfigEntity), e);
                continue;
            }
        }
    }

    /**
    * @description 发送公众号消息
    * @params [merchantEntity, allActivitySkuDetailValueObjects]
    * @return void
    * <AUTHOR>
    * @date  2025/5/23 10:52
    */
    private void sendMsgToMerchant(MerchantEntity merchantEntity, Map<String, AreaSkuEntity> areaSkuEntityMap, Map<String, InventoryEntity> inventoryEntityMap, List<ActivitySkuDetailValueObject> allActivitySkuDetailValueObjects) {
        ActivityNotice activityNotice = new ActivityNotice();

        SendMessageInput sendMessageInput;
        for (ActivitySkuDetailValueObject allActivitySkuDetailValueObject : allActivitySkuDetailValueObjects) {
            InventoryEntity inventoryEntity = inventoryEntityMap.get(allActivitySkuDetailValueObject.getSku());
            if (inventoryEntity == null) {
                log.info("发送公众号消息-根据sku获取商品信息为空, sku:{}", allActivitySkuDetailValueObject.getSku());
                continue;
            }

            AreaSkuEntity areaSkuEntity = areaSkuEntityMap.get(allActivitySkuDetailValueObject.getSku() + "_" + merchantEntity.getAreaNo());
            if (areaSkuEntity == null) {
                log.info("发送公众号消息-根据sku获取区域sku信息为空, sku:{}", allActivitySkuDetailValueObject.getSku());
                continue;
            }

            //查询活动价格--实时计算
            int unit = inventoryEntity.getBaseSaleUnit() * inventoryEntity.getBaseSaleQuantity();
            ActivityLadderPriceValueObject ladderPrice = ActivityLadderPriceValueObject.getLadderPrice(allActivitySkuDetailValueObject.getLadderConfig(), unit);
            if (ladderPrice == null) {
                log.info("发送公众号消息-根据sku获取活动价格信息为空, sku:{}", allActivitySkuDetailValueObject.getSku());
                continue;
            }
            try {
                BigDecimal costPrice = null;
                if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), ladderPrice.getAdjustType())) {
                    Integer warehouseNo = fenceQueryFacade.getWarehouseNo(allActivitySkuDetailValueObject.getSku(), merchantEntity.getAreaNo());
                    if(Objects.isNull(warehouseNo)) {
                        log.warn("发送公众号消息-sku:{}在城市：{}找不到对应的库存仓!", allActivitySkuDetailValueObject.getSku(), merchantEntity.getAreaNo());
                        continue;
                    }

                    // 查询周期成本
                    BigDecimal cycleInventoryCost = productCostQueryFacade.selectCycleCost(allActivitySkuDetailValueObject.getSku(), warehouseNo);
                    if (Objects.nonNull(cycleInventoryCost) && cycleInventoryCost.compareTo(BigDecimal.ZERO) > 0) {
                        costPrice = cycleInventoryCost;
                    }
                }
                BigDecimal activityPrice = getActivityPrice(costPrice, ladderPrice, areaSkuEntity.getPrice());

                //组装模版消息
                activityNotice.setActivityName(allActivitySkuDetailValueObject.getActivityName());
                activityNotice.setSku(allActivitySkuDetailValueObject.getSku());
                activityNotice.setActivityStartTime(allActivitySkuDetailValueObject.getActivityStartTime());
                activityNotice.setActivityEndTime(allActivitySkuDetailValueObject.getActivityEndTime());
                activityNotice.setPdName(inventoryEntity.getPdName());
                activityNotice.setWeight(inventoryEntity.getWeight());
                activityNotice.setActivityPrice(activityPrice);
                String templateMessage = ActivityNoticeMsg.templateMessage(activityNotice);

                //发送模版消息
                sendMessageInput = new SendMessageInput();
                sendMessageInput.setMessage(templateMessage);
                sendMessageInput.setOpenId(merchantEntity.getOpenid());
                sendMessageInput.setTemplateId(ActivityNoticeMsg.PRO_TEMPLATE_ID);
                sendMessageInput.setJumpUrl(WeChatUtils.getWeChatCode(Conf.PRODUCT_DETAIL_URL + "&pdId=" + inventoryEntity.getPdId() + "&sku=" + inventoryEntity.getSku()));
                wxSendMessageFacade.sendMessage(sendMessageInput);
            } catch (Exception e) {
                log.warn("发送公众号消息提醒通知异常, allActivitySkuDetailValueObject, cause:{}", JSON.toJSONString(allActivitySkuDetailValueObject), e);
            }
        }
    }

    /**
    * @description 根据价格阶梯配置计算活动价格
    * @params [costPrice, ladderPrice, originalPrice]
    * @return java.math.BigDecimal
    * <AUTHOR>
    * @date  2025/5/23 11:37
    */
    private BigDecimal getActivityPrice(BigDecimal costPrice, ActivityLadderPriceValueObject ladderPrice, BigDecimal originalPrice) {
        boolean deFlag = true;
        BigDecimal resultPrice = originalPrice;

        //指定价
        if (Objects.equals(InventoryAdjustPriceTypeEnum.APPOINT.getType(), ladderPrice.getAdjustType())) {
            resultPrice = ladderPrice.getAmount().compareTo(originalPrice) <= 0 ? ladderPrice.getAmount() : originalPrice;
        }//百分比
        else if (Objects.equals(InventoryAdjustPriceTypeEnum.PERCENTAGE.getType(), ladderPrice.getAdjustType())) {
            resultPrice = originalPrice.multiply(ladderPrice.getAmount());
        }//定额减
        else if (Objects.equals(InventoryAdjustPriceTypeEnum.QUOTA_REDUCTION.getType(), ladderPrice.getAdjustType())) {
            resultPrice = originalPrice.subtract(ladderPrice.getAmount());
        } //毛利百分比
        else if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), ladderPrice.getAdjustType())) {
            if (costPrice != null && costPrice.compareTo(BigDecimal.ZERO) > 0) {
                if (originalPrice.compareTo(costPrice) < 0) {
                    //原价小于成本价是不做向上取整
                    deFlag = false;
                } else {
                    resultPrice = originalPrice.subtract((originalPrice.subtract(costPrice).multiply(ladderPrice.getAmount())));
                }
            }
        }

        //价格异常处理
        if (resultPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("价格计算异常，计算后的价格：{}", resultPrice);
            resultPrice = originalPrice;
        }

        //小数处理
        if (Objects.equals(ladderPrice.getRoundingMode(), 0)) {
            resultPrice = resultPrice.setScale(2, RoundingMode.HALF_UP);
        } else if (deFlag) {
            resultPrice = resultPrice.setScale(0, RoundingMode.CEILING);
        }
        log.info("结束价格计算，resultPrice：{}", resultPrice);
        return resultPrice;
    }
}