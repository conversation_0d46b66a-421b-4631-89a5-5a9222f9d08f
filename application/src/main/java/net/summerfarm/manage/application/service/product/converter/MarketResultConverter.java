package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemAggListVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemBaseVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemByLargeAreaListVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemListVO;
import net.summerfarm.manage.domain.product.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MarketResultConverter {

    MarketResultConverter INSTANCE = Mappers.getMapper(MarketResultConverter.class);

    MarketItemBaseVO marketItemBaseEntityToMarketItemBaseVO(MarketItemBaseEntity marketItemBaseEntity);

    MarketItemAggListVO marketItemAggListEntityToMarketItemAggListVO(MarketItemAggListEntity marketItemAggListEntity);

    List<MarketItemAggListVO> marketItemAggListEntityListToMarketItemAggListVOList(List<MarketItemAggListEntity> marketItemAggListEntityList);

    MarketItemListVO marketItemListEntityToMarketItemListVO(MarketItemListEntity marketItemListEntity);

    List<MarketItemListVO> marketItemListEntityListToMarketItemListVOList(List<MarketItemListEntity> marketItemListEntityList);

    List<MarketItemListVO> productEntityListToMarketItemListVOList(List<ProductEntity> list);

    @Mapping(source = "sku", target = "itemCode")
    @Mapping(source = "productName", target = "title")
    @Mapping(source = "weight", target = "specification")
    MarketItemListVO productEntityListToMarketItemListVO(ProductEntity e);


    List<MarketItemByLargeAreaListVO> groupByLargeAreaNoEntityListToMarketItemListVOList(List<InvetoryGroupByLargeAreaNoEntity> entities);

    @Mapping(source = "sku", target = "itemCode")
    @Mapping(source = "skuName", target = "title")
    @Mapping(source = "weight", target = "specification")
    MarketItemByLargeAreaListVO groupByLargeAreaNoEntityListToMarketItemListVO(InvetoryGroupByLargeAreaNoEntity entitie);
}
