package net.summerfarm.manage.application.inbound.controller.merchant;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.assembler.MerchantAccountTransferAssembler;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantTransferCheckInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantTransferCheckVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.application.inbound.controller.merchant.input.command.MerchantAccountTransferCommandInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantAccountTransferVO;
import net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantAccountTransferQueryInput;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.application.service.merchant.MerchantAccountTransferCommandService;
import net.summerfarm.manage.application.service.merchant.MerchantAccountTransferQueryService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * @Title 门店迁移
 * 门店迁移功能模块
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 */
@RestController
@RequestMapping(value="/merchant/transfer")
public class MerchantAccountTransferController{

	@Autowired
	private MerchantAccountTransferCommandService merchantAccountTransferCommandService;
	@Autowired
	private MerchantAccountTransferQueryService merchantAccountTransferQueryService;


	/**
	 * 门店迁移列表
	 * @return MerchantAccountTransferVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<MerchantAccountTransferVO>> getPage(@RequestBody MerchantAccountTransferQueryInput input){
		PageInfo<MerchantAccountTransferEntity> page = merchantAccountTransferQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, MerchantAccountTransferAssembler::toMerchantAccountTransferVO));
	}

	/**
	* 获取详情
	* @return MerchantAccountTransferVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<MerchantAccountTransferVO> detail(@RequestBody MerchantAccountTransferQueryInput input){
		if (input == null || input.getId() == null) {
			throw new BizException("id 不能为空");
		}
		return CommonResult.ok(MerchantAccountTransferAssembler.toMerchantAccountTransferVO(merchantAccountTransferQueryService.getDetail(input.getId())));
	}


	/**
	 * 交验门店是否可以被合并
	 *
	 * @param checkInput 参数
	 * @return 列表
	 */
	@RequestMapping(value = "/query/check", method = RequestMethod.POST)
	@RequiresPermissions(value = {"transfer:merchant:list"}, logical = Logical.OR)
	public CommonResult<MerchantTransferCheckVO> check(@RequestBody @Validated MerchantTransferCheckInput checkInput) {
		return CommonResult.ok(merchantAccountTransferQueryService.check(checkInput));
	}


	/**
	 * 发起合并
	 *
	 * @param input 参数
	 * @return 返回结果
	 */
	@RequestMapping(value = "/upset/add", method = RequestMethod.POST)
	@RequiresPermissions(value = {"transfer:merchant:add"}, logical = Logical.OR)
	public CommonResult<Boolean> transfer(@RequestBody @Validated MerchantAccountTransferCommandInput input) {
		return CommonResult.ok(merchantAccountTransferCommandService.transfer(input));
	}


}

