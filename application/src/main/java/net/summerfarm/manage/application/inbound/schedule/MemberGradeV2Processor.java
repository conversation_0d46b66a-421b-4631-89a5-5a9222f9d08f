package net.summerfarm.manage.application.inbound.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.converter.FastJsonConverter;
import net.summerfarm.manage.common.dto.XmTaskParamDTO;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantCommandRepository;
import net.summerfarm.manage.domain.offline.entity.CustGradeEntity;
import net.summerfarm.manage.domain.offline.repository.CustGradeQueryRepository;
import net.xianmu.authentication.common.utils.SpringContextUtil;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/1  14:31
 */
@Component
@Slf4j
public class MemberGradeV2Processor extends XianMuJavaProcessorV2 {

    @Resource
    CustGradeQueryRepository custGradeQueryRepository;

    @Resource
    MerchantCommandRepository merchantCommandRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String instanceParameterStr = context.getInstanceParameters();
        String jobParameters = context.getJobParameters();
        log.info("【定时任务更新门店积分】开始 instanceParameters: 【{}】,jobParameters:【{}】", JSON.toJSONString(instanceParameterStr), JSON.toJSONString(jobParameters));
        XmTaskParamDTO dto = StringUtils.isBlank(instanceParameterStr) ? JSONObject.parseObject(jobParameters, XmTaskParamDTO.class) : JSONObject.parseObject(instanceParameterStr, XmTaskParamDTO.class);
        this.execute(dto);
        log.info("【定时任务更新门店积分】结束");
        return new ProcessResult(true);
    }



    private void execute(XmTaskParamDTO dto) throws InterruptedException {
        if(dto == null) {
            dto = new XmTaskParamDTO();
        }
        Long startId = dto.getStartId() == null ? 0L : dto.getStartId();
        Long endId = dto.getEndId() == null ? Long.MAX_VALUE : dto.getEndId();
        Integer sleep = dto.getSleep();
        Integer offset = dto.getOffset() == null ? 200 : dto.getOffset();
        String dateTag = dto.getDateTag() == null ? DateUtil.format(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1), DatePattern.PURE_DATE_FORMATTER) : dto.getDateTag();


        while (startId < endId) {
            // 捞取数据
            List<CustGradeEntity> gradeEntities = custGradeQueryRepository.selectTaskDataList(dateTag, startId, offset);
            if (CollUtil.isEmpty(gradeEntities)) {
                log.info("该批次暂无数据, startMId:{},offset :{}", startId, offset);
                return;
            }

            log.info("当前批次开始执行, startMId:{},offset :{}", startId, offset);
            MemberGradeV2Processor bean = SpringContextUtil.getBean("memberGradeV2Processor", MemberGradeV2Processor.class);
            boolean updateMerchantCurrentGradeFlag = updateMerchantCurrentGradeFlag(dto, dateTag);
            bean.updateMerchantGradeBatch(gradeEntities, updateMerchantCurrentGradeFlag);
            log.info("当前批次执行完毕, startMId:{},offset :{}", startId, offset);


            // 获取数量不足批次大小的时候代表同步结束了
            int currentSize = gradeEntities.size();
            if (currentSize < offset) {
                log.info("执行完毕:currentSize:{},offset:{}", currentSize, offset);
                break;
            }


            // 获取当前批次的最大mid
            startId = this.getMaxId(gradeEntities);
            log.info("下一批次执行数据, startMId:{},offset :{}", startId, offset);


            // sleep?
            if (sleep != null) {
                Thread.sleep(sleep);
            }
        }
    }

    private boolean updateMerchantCurrentGradeFlag(XmTaskParamDTO dto, String dateTag){
        log.info("dto:{}, dateTag:{}", JSON.toJSONString(dto), dateTag);
        boolean updateMerchantCurrentGradeFlag = dto.isUpdateMerchantCurrentGradeFlag() || DateUtil.parse(dateTag, "yyyyMMdd").isLastDayOfMonth();

        // 如果是每月的第一天执行或者强制指定，就更新门店当月的等级
        if(updateMerchantCurrentGradeFlag) {
            log.info("需要更新客户当前等级。dateTag：{}", dateTag);
        }
        return updateMerchantCurrentGradeFlag;
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateMerchantGradeBatch(List<CustGradeEntity> gradeEntities, boolean updateMerchantCurrentGradeFlag){
        try {
            List<MerchantCommandParam> list = new ArrayList<>();
            gradeEntities.forEach(grade -> {
                MerchantCommandParam param = new MerchantCommandParam();
                param.setMId(grade.getCustId());
                param.setNextGrade(grade.getNextGrade());
                param.setMemberIntegral(grade.getDlvRealAmt());
                if(updateMerchantCurrentGradeFlag) {
                    param.setGrade(grade.getGrade());
                }
                list.add(param);
            });

            merchantCommandRepository.updateByPrimaryKeySelectiveBatch(list);
        } catch (Exception e) {
            log.error("start error data :{}", FastJsonConverter.convert(gradeEntities.get(0)));
            log.error("【定时任务更新门店积分】失败!", e);
        }

    }


    public Long getMaxId(List<CustGradeEntity> gradeEntities) {
        CustGradeEntity entity = gradeEntities.get(gradeEntities.size() - 1);
        return entity.getCustId();
    }

    public static void main(String[] args) {
        System.out.println(DateUtil.format(DateUtil.parse("20250101").offset(DateField.DAY_OF_MONTH, -1), DatePattern.PURE_DATE_FORMATTER));
    }
}
