package net.summerfarm.manage.application.service.merchant.converter;


import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;

import java.util.*;

public class XmMerchantStoreRespConvert {
    private XmMerchantStoreRespConvert() {
        // 无需实现
    }

    public static List<MerchantVO> toMerchantVOList(List<MerchantStoreAndExtendResp> merchantStoreAndExtendRespList) {
        if (merchantStoreAndExtendRespList == null) {
            return Collections.emptyList();
        }
        List<MerchantVO> merchantVOList = new ArrayList<>();
        for (MerchantStoreAndExtendResp merchantStoreAndExtendResp : merchantStoreAndExtendRespList) {
            merchantVOList.add(toMerchantVO(merchantStoreAndExtendResp));
        }
        return merchantVOList;
    }

    public static MerchantVO toMerchantVO(MerchantStoreAndExtendResp merchantStoreAndExtendResp) {
        if (merchantStoreAndExtendResp == null) {
            return null;
        }
        MerchantVO merchantVO = new MerchantVO();
        merchantVO.setMId(merchantStoreAndExtendResp.getMId());
        merchantVO.setStatus(merchantStoreAndExtendResp.getStatus());
        merchantVO.setRegisterTime(merchantStoreAndExtendResp.getRegisterTime());
        merchantVO.setChannelCode(merchantStoreAndExtendResp.getChannelCode());
        merchantVO.setAuditTime(merchantStoreAndExtendResp.getAuditTime());
        merchantVO.setProvince(merchantStoreAndExtendResp.getProvince());
        merchantVO.setCity(merchantStoreAndExtendResp.getCity());
        merchantVO.setArea(merchantStoreAndExtendResp.getArea());
        merchantVO.setRemark(merchantStoreAndExtendResp.getRemark());
        merchantVO.setAreaNo(merchantStoreAndExtendResp.getAreaNo());
        Arrays.stream(RegionalOrganizationEnums.Size.values()).filter(it -> Objects.equals(merchantStoreAndExtendResp.getSize(), it.getCode())
        ).findFirst().ifPresent(size -> merchantVO.setSize(size.getDesc()));
        merchantVO.setType(MerchantStoreEnums.Type.getDesc(merchantStoreAndExtendResp.getType()));
        merchantVO.setPopView(merchantStoreAndExtendResp.getPopView());
        merchantVO.setFirstLoginPop(merchantStoreAndExtendResp.getFirstLoginPop());
        merchantVO.setChangePop(merchantStoreAndExtendResp.getChangePop());
        merchantVO.setUpdateTime(merchantStoreAndExtendResp.getUpdateTime());
        merchantVO.setDisplayButton(merchantStoreAndExtendResp.getDisplayButton());
        merchantVO.setPreRegisterFlag(merchantStoreAndExtendResp.getPreRegisterFlag());
        merchantVO.setMname(merchantStoreAndExtendResp.getStoreName());
        merchantVO.setStoreId(merchantStoreAndExtendResp.getId());
        merchantVO.setAdminId(merchantStoreAndExtendResp.getAdminId());
        merchantVO.setPoiNote(merchantStoreAndExtendResp.getPoiNote());
        merchantVO.setBusinessType(merchantStoreAndExtendResp.getBusinessType());
        merchantVO.setType(MerchantStoreEnums.Type.getDesc(merchantStoreAndExtendResp.getType()));
        merchantVO.setPoiNote(merchantStoreAndExtendResp.getPoiNote());
        merchantVO.setMcontact(merchantStoreAndExtendResp.getAccountName());
        merchantVO.setPhone(merchantStoreAndExtendResp.getPhone());
        merchantVO.setOperateStatus(merchantStoreAndExtendResp.getOperateStatus());
        merchantVO.setBusinessLine(merchantStoreAndExtendResp.getBusinessLine());
        //merchantVO.setPoiNote(merchantStoreAndExtendResp.get);
// Not mapped TO fields:
// storeId
// roleId
// mname
// mcontact
// openid
// phone
// rankId
// loginTime
// invitecode
// inviterChannelCode
// auditUser
// businessLicense
// address
// poiNote
// shopSign
// otherProof
// lastOrderTime
// tradeArea
// tradeGroup
// unionid
// mpOpenid
// adminId
// direct
// server
// memberIntegral
// grade
// skuShow
// rechargeAmount
// cashAmount
// cashUpdateTime
// showPrice
// mergeAdmin
// mergeTime
// pullBlackRemark
// pullBlackOperator
// houseNumber
// companyBrand
// cluePool
// merchantType
// enterpriseScale
// examineType
// operateStatus
// updater
// doorPic
// adminRealName
// areaName
// invoiceTitle
// Not mapped FROM fields:
// organizationName
// adminId
// mockLoginFlag
// id
// tenantId
// storeName
// auditRemark
// createTime
// billSwitch
// onlinePayment
// balanceAuthority
// storeNo
// regionalId
// businessType
        return merchantVO;
    }



    //MerchantStoreAndExtendResp
}
