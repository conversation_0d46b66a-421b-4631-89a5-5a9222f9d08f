package net.summerfarm.manage.application.service.admin;

import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.application.inbound.controller.admin.input.command.AdminDataPermissionCommandInput;


/**
 * @date 2024-06-19 16:33:43
 * @version 1.0
 */
public interface AdminDataPermissionCommandService {

    /**
     * @description: 新增
     * @return AdminDataPermissionEntity
     **/
    AdminDataPermissionEntity insert(AdminDataPermissionCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(AdminDataPermissionCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}