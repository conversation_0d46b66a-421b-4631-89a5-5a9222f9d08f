package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2024/11/18 17:06
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopMatchedCompetitorSkuVO {

    /**
     * 等级
     */
    private String rank;

    /**
     * 竞对sku
     */
    private String competitorSkuCode;

    /**
     * 分值
     */
    private String similarityScore;

    /**
     * 匹配理由
     */
    private String matchingReason;

    /**
     * 竞对商品名称
     */
    private String competitorProductName;

    /**
     * 竞对价格
     */
    private String competitorSkuPrice;

    /**
     * 竞对
     */
    private String competitor;

    /**
     * 商品的毛重（含包装）
     */
    private String grossWeight;

    /**
     * 商品的净重（不含包装）
     */
    private String netWeight;

    /**
     * 商品的规格描述
     */
    private String specification;

    /**
     * 商品名称
     */
    private String goodsName;

}
