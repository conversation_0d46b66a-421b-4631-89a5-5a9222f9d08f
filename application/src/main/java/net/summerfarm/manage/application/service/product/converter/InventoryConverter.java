package net.summerfarm.manage.application.service.product.converter;

import com.google.common.base.Splitter;
import net.summerfarm.client.provider.product.mall.req.PopGoodsEditReq;
import net.summerfarm.manage.application.inbound.controller.product.input.*;
import net.summerfarm.manage.application.inbound.controller.product.vo.InventoryBaseVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.InventoryDetailVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ItemLabelVO;
import net.summerfarm.manage.application.service.product.command.PopSkuUpdateCommand;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationCategoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationProductQueryParam;
import net.xianmu.common.user.UserInfoHolder;
import net.summerfarm.manage.domain.product.entity.CategoryAllPathEntity;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.facade.market.dto.MarketItemLabelDTO;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName InventoryConverter
 * @Description
 * <AUTHOR>
 * @Date 16:14 2024/5/6
 * @Version 1.0
 **/
public class InventoryConverter {
    public static InventoryCommandParam inputToParma(InventoryUpdateInput input) {
        InventoryCommandParam param = new InventoryCommandParam();
        param.setInvId(input.getInvId());
        param.setSku(input.getSku());
        param.setAitId(input.getAitId());
        param.setPdId(input.getPdId());
        param.setSalesMode(input.getSalesMode());
        param.setOrigin(input.getOrigin());
        param.setUnit(input.getUnit());
        param.setPack(input.getPack());
        param.setTenantId(input.getTenantId());
        param.setCreateType(input.getCreateType());
        param.setWeight(input.getWeight());
        param.setWeightNotes(input.getWeightNotes());
        param.setIsDomestic(input.getIsDomestic());
        param.setVolume(input.getVolume());
        param.setWeightNum(input.getWeightNum());
        param.setMaturity(input.getMaturity());
        param.setProductionDate(input.getProductionDate());
        param.setStorageMethod(input.getStorageMethod());
        param.setSalePrice(input.getSalePrice());
        param.setPromotionPrice(input.getPromotionPrice());
        param.setIntroduction(input.getIntroduction());
        param.setOutdated(input.getOutdated());
        param.setAfterSaleQuantity(input.getAfterSaleQuantity());
        param.setBaseSaleUnit(input.getBaseSaleUnit());
        param.setBaseSaleQuantity(input.getBaseSaleQuantity());
        param.setType(input.getType());
        param.setSubType(input.getSubType());
        param.setAdminId(input.getAdminId());
        param.setSamplePool(input.getSamplePool());
        param.setSkuPic(input.getSkuPic());
        param.setAfterSaleUnit(input.getAfterSaleUnit());
        param.setSupplierVisible(input.getSupplierVisible());
        param.setAuditStatus(input.getAuditStatus());
        param.setAddTime(input.getAuditTime());
        param.setCreator(input.getCreator());
        param.setExtType(input.getExtType());
        param.setCreateRemark(input.getCreateRemark());
        param.setTaskType(input.getTaskType());
        param.setRealName(input.getRealName());
        param.setExtType(input.getExtType());
        param.setAuditor(input.getAuditor() == null ? null : input.getAuditor().intValue());
        param.setAveragePriceFlag(input.getAveragePriceFlag());
        param.setSkuName(input.getSkuName());
        param.setRefuseReason(input.getRefuseReason());
        param.setBuyerId(input.getBuyerId());
        param.setBuyerName(input.getBuyerName());
        param.setNetWeightNum(input.getNetWeightNum());
        param.setNetWeightUnit(input.getNetWeightUnit());
        param.setAfterSaleRuleDetail(input.getAfterSaleRuleDetail());
        param.setVideoUrl(input.getVideoUrl());
        param.setVideoUploadUser(input.getVideoUploadUser());
        param.setVideoUploadTime(input.getVideoUploadTime());
        param.setQuoteType(input.getQuoteType());
        param.setMinAutoAfterSaleThreshold(input.getMinAutoAfterSaleThreshold());
        return param;
    }

    public static ItemLabelVO marketItemLabelDTO2ItemLabelVO(MarketItemLabelDTO marketItemLabelDTO) {
        ItemLabelVO itemLabelVO = new ItemLabelVO();
        itemLabelVO.setLabelName(marketItemLabelDTO.getLabelName());
        itemLabelVO.setId(marketItemLabelDTO.getId());
        return itemLabelVO;
    }

    public static List<ItemLabelVO> marketItemLabelDTO2ItemLabelVOS(List<MarketItemLabelDTO> marketItemLabelDTOS) {
        if (CollectionUtils.isEmpty(marketItemLabelDTOS)) {
            return Collections.emptyList();
        }
        List<ItemLabelVO> itemLabelVOS = new ArrayList<>(marketItemLabelDTOS.size());
        marketItemLabelDTOS.stream().forEach(marketItemLabelDTO -> itemLabelVOS.add(marketItemLabelDTO2ItemLabelVO(marketItemLabelDTO)));
        return itemLabelVOS;
    }

    public static InventoryDetailVO entityToVO(InventoryEntity inventoryEntity) {
        InventoryDetailVO inventoryDetailVO = new InventoryDetailVO();
        inventoryDetailVO.setInvId(inventoryEntity.getInvId());
        inventoryDetailVO.setPdId(inventoryEntity.getPdId());
        inventoryDetailVO.setSku(inventoryEntity.getSku());
        inventoryDetailVO.setWeight(inventoryEntity.getWeight());
        inventoryDetailVO.setType(inventoryEntity.getType());
        inventoryDetailVO.setSubType(inventoryEntity.getSubType());
        inventoryDetailVO.setExtType(inventoryEntity.getExtType());
        inventoryDetailVO.setUnit(inventoryEntity.getUnit());
        inventoryDetailVO.setOutdated(inventoryEntity.getOutdated());
        //inventoryDetailVO.setPic(inventoryEntity.getpic());
        inventoryDetailVO.setAuditStatus(inventoryEntity.getAuditStatus());
        inventoryDetailVO.setAitId(inventoryEntity.getAitId());
        inventoryDetailVO.setSalesMode(inventoryEntity.getSalesMode());
        inventoryDetailVO.setOrigin(inventoryEntity.getOrigin());
        inventoryDetailVO.setPack(inventoryEntity.getPack());
        inventoryDetailVO.setTenantId(inventoryEntity.getTenantId());
        inventoryDetailVO.setCreateType(inventoryEntity.getCreateType());

        inventoryDetailVO.setWeightNotes(inventoryEntity.getWeightNotes());
        inventoryDetailVO.setIsDomestic(inventoryEntity.getIsDomestic());
        inventoryDetailVO.setVolume(inventoryEntity.getVolume());
        inventoryDetailVO.setWeightNum(inventoryEntity.getWeightNum());
        inventoryDetailVO.setExpiryDate(inventoryEntity.getExpiryDate());
        inventoryDetailVO.setShow(inventoryEntity.getShow());
        inventoryDetailVO.setMaturity(inventoryEntity.getMaturity());
        inventoryDetailVO.setProductionDate(inventoryEntity.getProductionDate());
        inventoryDetailVO.setStorageMethod(inventoryEntity.getStorageMethod());
        inventoryDetailVO.setSalePrice(inventoryEntity.getSalePrice());
        inventoryDetailVO.setPromotionPrice(inventoryEntity.getPromotionPrice());
        inventoryDetailVO.setIntroduction(inventoryEntity.getIntroduction());
        inventoryDetailVO.setAfterSaleUnit(inventoryEntity.getAfterSaleUnit());
        inventoryDetailVO.setAfterSaleQuantity(inventoryEntity.getAfterSaleQuantity());

        inventoryDetailVO.setBaseSaleQuantity(inventoryEntity.getBaseSaleQuantity());
        inventoryDetailVO.setBaseSaleUnit(inventoryEntity.getBaseSaleUnit());
        inventoryDetailVO.setAdminId(inventoryEntity.getAdminId());
        inventoryDetailVO.setSamplePool(inventoryEntity.getSamplePool());
        inventoryDetailVO.setSkuPic(inventoryEntity.getSkuPic());
        inventoryDetailVO.setAfterSaleUnit(inventoryEntity.getAfterSaleUnit());
        inventoryDetailVO.setAddTime(inventoryEntity.getAddTime());
        inventoryDetailVO.setUpdateTime(inventoryEntity.getUpdateTime());
        inventoryDetailVO.setSupplierVisible(inventoryEntity.getSupplierVisible());
        inventoryDetailVO.setAuditTime(inventoryEntity.getAuditTime());
        inventoryDetailVO.setCreator(inventoryEntity.getCreator());

        inventoryDetailVO.setCreateRemark(inventoryEntity.getCreateRemark());
        inventoryDetailVO.setTaskType(inventoryEntity.getTaskType());
        inventoryDetailVO.setRealName(inventoryEntity.getRealName());
        inventoryDetailVO.setAuditor(inventoryEntity.getAuditor());
        inventoryDetailVO.setAveragePriceFlag(inventoryEntity.getAveragePriceFlag());
        inventoryDetailVO.setSkuName(inventoryEntity.getSkuName());
        inventoryDetailVO.setPdName(inventoryEntity.getPdName());
        inventoryDetailVO.setRefuseReason(inventoryEntity.getRefuseReason());
        inventoryDetailVO.setNameRemakes(inventoryEntity.getNameRemakes());
        inventoryDetailVO.setBuyerId(inventoryEntity.getBuyerId());
        inventoryDetailVO.setBuyerName(inventoryEntity.getBuyerName());
        inventoryDetailVO.setNetWeightNum(inventoryEntity.getNetWeightNum());
        inventoryDetailVO.setNetWeightUnit(inventoryEntity.getNetWeightUnit());
        inventoryDetailVO.setAfterSaleRuleDetail(inventoryEntity.getAfterSaleRuleDetail());
        inventoryDetailVO.setVideoUrl(inventoryEntity.getVideoUrl());
        inventoryDetailVO.setQuoteType(inventoryEntity.getQuoteType());
        inventoryDetailVO.setMinAutoAfterSaleThreshold(inventoryEntity.getMinAutoAfterSaleThreshold());

        return inventoryDetailVO;
    }

    public static InventoryQueryParam queryInfoInputToParam(ProductInfoInput input) {
        InventoryQueryParam param = new InventoryQueryParam();
        param.setPdId(input.getPdId());
        param.setSku(input.getSku());
        param.setSamplePool(input.getSamplePool());
        param.setAuditStatus(input.getAuditStatus());
        param.setExtType(input.getExtType());
        param.setOutdated(input.getOutdated());

        return param;
    }

    public static InventoryCommandParam videoInputToParma(InventoryVideoInput input) {
        InventoryCommandParam param = new InventoryCommandParam();
        param.setSku(input.getSku());
        param.setVideoUrl (input.getVideoUrl ());
        param.setVideoUploadUser (input.getVideoUploadUser ());
        param.setVideoUploadTime (LocalDateTime.now ());
        return param;
    }

    public static PopSkuUpdateCommand convert(PopGoodsEditReq popGoodsEditReq) {
        return PopSkuUpdateCommand.builder()
                .sku(popGoodsEditReq.getSku())
                .picturePath(popGoodsEditReq.getPicturePath())
                .detailPictureList(popGoodsEditReq.getDetailPictureList())
                .videoUrl(popGoodsEditReq.getVideoUrl())
                .videoUploadUser(UserInfoHolder.getUserRealName())
                .build();
    }


    public static InventoryBaseVO convert(InventoryEntity inventoryEntity, CategoryAllPathEntity categoryAllPathEntity, ProductsPropertyValueEntity fruitSizeEntity) {
        if (Objects.isNull(inventoryEntity)) {
            return null;
        }
        return InventoryBaseVO.builder()
                .sku(inventoryEntity.getSku())
                .pdId(inventoryEntity.getPdId())
                .pdName(inventoryEntity.getPdName())
                .weight(inventoryEntity.getWeight())
                .weightNum(inventoryEntity.getWeightNum())
                .netWeightNum(inventoryEntity.getNetWeightNum())
                .type(inventoryEntity.getType())
                .subType(inventoryEntity.getSubType())
                .tenantId(inventoryEntity.getTenantId())
                .afterSaleRuleDetail(inventoryEntity.getAfterSaleRuleDetail())
                .categoryId(inventoryEntity.getCategoryId())
                .allPathCategoryName(Objects.nonNull(categoryAllPathEntity) ? categoryAllPathEntity.getAllPathCategoryName() : "")
                .picturePath(inventoryEntity.getPicturePath())
                .detailPictureList(buildDetailPictureList(inventoryEntity.getDetailPicture()))
                .fruitSize(Objects.nonNull(fruitSizeEntity) ? fruitSizeEntity.getProductsPropertyValue() : null)
                .build();

    }

    private static List<String> buildDetailPictureList(String detailPicture) {
        if (StringUtils.isBlank(detailPicture)) {
            return Lists.newArrayList();
        }
        return Splitter.on(",").splitToList(detailPicture);
    }

    public static InventoryBaseVO convertInventoryBaseVO(InventoryEntity inventoryEntity) {
        if (Objects.isNull(inventoryEntity)) {
            return null;
        }
        return InventoryBaseVO.builder()
                .sku(inventoryEntity.getSku())
                .pdId(inventoryEntity.getPdId())
                .pdName(inventoryEntity.getPdName())
                .weight(inventoryEntity.getWeight())
                .unit(inventoryEntity.getUnit())
                .weightNum(inventoryEntity.getWeightNum())
                .netWeightNum(inventoryEntity.getNetWeightNum())
                .type(inventoryEntity.getType())
                .subType(inventoryEntity.getSubType())
                .tenantId(inventoryEntity.getTenantId())
                .afterSaleRuleDetail(inventoryEntity.getAfterSaleRuleDetail())
                .categoryId(inventoryEntity.getCategoryId())
                .picturePath(inventoryEntity.getPicturePath())
                .detailPictureList(buildDetailPictureList(inventoryEntity.getDetailPicture()))
                .outdated(inventoryEntity.getOutdated())
                .addTime(inventoryEntity.getAddTime())
                .build();

    }

    public static InventoryQueryParam convert(InventoryBaseQueryInput queryInput) {
        if (Objects.isNull(queryInput)) {
            return null;
        }
        InventoryQueryParam inventoryQueryParam = new InventoryQueryParam();
        inventoryQueryParam.setPageIndex(queryInput.getPageIndex());
        inventoryQueryParam.setPageSize(queryInput.getPageSize());
        inventoryQueryParam.setSku(queryInput.getSku());
        inventoryQueryParam.setPdId(queryInput.getPdId());
        inventoryQueryParam.setFirstCategoryId(queryInput.getFirstCategoryId());
        inventoryQueryParam.setSecondCategoryId(queryInput.getSecondCategoryId());
        inventoryQueryParam.setType(queryInput.getType());
        inventoryQueryParam.setSubType(queryInput.getSubType());
        inventoryQueryParam.setOutdated(queryInput.getOutdated());
        inventoryQueryParam.setProductsPropertyId(queryInput.getProductsPropertyId());
        inventoryQueryParam.setProductsPropertyValue(queryInput.getProductsPropertyValue());
        return inventoryQueryParam;

    }

    public static PendingAssociationProductQueryParam convert(PendingAssociationProductQueryInput queryInput) {
        if (Objects.isNull(queryInput)) {
            return null;
        }
        PendingAssociationProductQueryParam pendingAssociationProductQueryParam = new PendingAssociationProductQueryParam();
        pendingAssociationProductQueryParam.setPageIndex(queryInput.getPageIndex());
        pendingAssociationProductQueryParam.setPageSize(queryInput.getPageSize());
        pendingAssociationProductQueryParam.setCategoryId(queryInput.getCategoryId());
        pendingAssociationProductQueryParam.setWarehouseNo(queryInput.getWarehouseNo());
        return pendingAssociationProductQueryParam;
    }

    public static PendingAssociationCategoryQueryParam convert(PendingAssociationCategoryQueryInput queryInput) {
        if (Objects.isNull(queryInput)) {
            return null;
        }
        PendingAssociationCategoryQueryParam pendingAssociationCategoryQueryParam = new PendingAssociationCategoryQueryParam();
        pendingAssociationCategoryQueryParam.setCategoryName(queryInput.getCategoryName());
        pendingAssociationCategoryQueryParam.setWarehouseNo(queryInput.getWarehouseNo());
        return pendingAssociationCategoryQueryParam;
    }



}
