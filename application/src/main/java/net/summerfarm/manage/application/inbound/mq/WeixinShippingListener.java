package net.summerfarm.manage.application.inbound.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;
import net.summerfarm.manage.application.inbound.mq.msgbody.WeixinShippingDTO;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.application.service.wx.WeixinShippingServiceImpl;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.enums.EnableTraceEnum;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 微信发货管理
 */
@MqListener(topic = "topic_sf_mall_manage_common",
        tag = "tag_wei_xin_shipping",
        consumerGroup = "GID_sf_mall_manage_common",
        consumeThreadMin = 1,
        consumeThreadMax = 1,
        enableTrace = EnableTraceEnum.TRUE,
        maxReconsumeTimes = 3)
@Slf4j
public class WeixinShippingListener extends AbstractMqListener<WeixinShippingDTO> {

    @Resource
    private WeixinShippingServiceImpl weixinShippingService;

    @Override
    public void process(WeixinShippingDTO dto) {
        log.info("【延时上传订单发货信息】开始,msg:{}", JSON.toJSONString(dto));
        if (dto == null || dto.getOrderNo() == null) {
            log.error("【延时上传订单发货信息】订单号为空!");
            return;
        }
        weixinShippingService.uploadShippingInfoByOrderNo(dto.getOrderNo(), dto.getOrdersType());
        log.info("【延时上传订单发货信息】结束。msg:{}", JSON.toJSONString(dto));
    }
}
