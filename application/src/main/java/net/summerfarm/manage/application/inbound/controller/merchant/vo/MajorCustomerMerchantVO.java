package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import lombok.Data;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactVO;
import net.summerfarm.manage.facade.deliivery.dto.DeliveryFeeRuleDTO;
import net.summerfarm.manage.facade.deliivery.dto.DistributionRulesDTO;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MajorCustomerMerchantVO {


    /**
     * 门店id
     */
    private Long mId;


    /**
     * 大客户id
     */
    private Long adminId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 大客户名称
     */
    private String businessName;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系方式
     */
    private String phone;
    /**
     * 区域
     */
    private String area;

    /**
     * 连锁类型：0-直营店 1-加盟店 2-托管店 3-个人店 4-连锁店 5-未知',
     */
    private Integer type;

    /**
     * `status` 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店 4、拉黑 5、注销'
     */
    private Integer status;
    /**
     * 1 账期 2  现结
     */
    private Integer direct;

    /**
     * 客户类型：1-大客户，2-单店',
     */
    private Integer size;

    private Integer areaNo;
    /**
     * 联系人信息 从里面拿收货地址 手机号和收货人
     */
    private ContactVO contactVO;
    /**
     * 运费规则-新版
     */
    private DistributionRulesDTO distributionRulesDTO;

    /**
     * 支持阶梯价的运费规则
     */
    private List<DeliveryFeeRuleDTO> deliveryFeeRuleDTOList;

    private Long storeId;

}
