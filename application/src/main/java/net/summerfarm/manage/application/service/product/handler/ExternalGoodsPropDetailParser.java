package net.summerfarm.manage.application.service.product.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.product.entity.ExternalGoodsPropDetailEntity;
import net.summerfarm.manage.domain.product.entity.TopMatchedXianmuSkuEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Description
 * @Date 2024/12/18 11:23
 * @<AUTHOR>
 */
@Slf4j
public class ExternalGoodsPropDetailParser {

    private static final String levelDesc = "等级";

    private static final String originDesc = "产地";

    private static final String fruitSizeDesc = "单果大小";

    public static ExternalGoodsPropDetailEntity parseGoodsPropDetail(String goodsPropDetail) {
        if (StringUtils.isBlank(goodsPropDetail)) {
            return null;
        }
        try {
            // 原生数据非标准json格式，需要加工后解析
            goodsPropDetail = goodsPropDetail.replaceAll("'", "\"");
            JSONArray jsonArray = JSON.parseArray(goodsPropDetail);
            // 需要解析数据
            String level = StringUtil.EMPTY_STRING;
            String origin = StringUtil.EMPTY_STRING;
            String fruitSize = StringUtil.EMPTY_STRING;
            // 遍历 JSON 数组
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                // 解析产地 果规 等级
                if (jsonObject.containsKey(originDesc)) {
                    origin = jsonObject.getString(originDesc);
                }
                if (jsonObject.containsKey(fruitSizeDesc)) {
                    fruitSize = jsonObject.getString(fruitSizeDesc);
                }
                if (jsonObject.containsKey(levelDesc)) {
                    level = jsonObject.getString(levelDesc);
                }
            }
            return ExternalGoodsPropDetailEntity.builder()
                    .level(level)
                    .fruitSize(fruitSize)
                    .origin(origin).build();

        } catch (Exception e) {
            log.warn("parseGoodsPropDetail fail goodsPropDetail:{}", goodsPropDetail, e);
            return null;
        }

    }

}
