package net.summerfarm.manage.application.service.delivery.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.delivery.DeliveryCommandService;
import net.summerfarm.manage.common.enums.OrderStatusEnum;
import net.summerfarm.manage.common.input.wx.DeliveryNotice;
import net.summerfarm.manage.common.msg.DeliveryNoticeMsg;
import net.summerfarm.manage.common.util.WeChatUtils;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import net.summerfarm.manage.domain.order.param.query.OrderItemQueryParam;
import net.summerfarm.manage.domain.order.repository.OrderItemQueryRepository;
import net.summerfarm.manage.facade.message.WxSendMessageFacade;
import net.summerfarm.manage.facade.message.input.SendMessageInput;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName DeliveryCommandServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 14:49 2024/1/30
 * @Version 1.0
 **/
@Service
@Slf4j
public class DeliveryCommandServiceImpl implements DeliveryCommandService {

    @Resource
    private DeliveryPlanQueryRepository deliveryPlanQueryRepository;

    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;

    @Resource
    private WxSendMessageFacade wxSendMessageFacade;

    @Override
    public void orderDeliveryNotice(String orderNo) {
        int pageIndex = 1;
        int pageSize = 500;

        while (true) {
            int pageStart = pageSize * (pageIndex - 1);
            List<DeliveryPlanFlatObject> noticeList = deliveryPlanQueryRepository.noticeLists(LocalDate.now().plusDays(1), orderNo, pageStart, pageSize);
            int size = noticeList.size();
            log.info("DeliveryCommandServiceImpl[]orderDeliveryNotice[]noticeList[]size:{}", size);
            if (CollectionUtils.isEmpty(noticeList)) {
                log.info("DeliveryCommandServiceImpl[]orderDeliveryNotice[]noticeList is empty!");
                break;
            }

            //历史逻辑 目的过滤order_item已退款的订单
            List<String> orderNos = noticeList.stream().map(DeliveryPlanFlatObject::getOrderNo).collect(Collectors.toList());
            OrderItemQueryParam queryParam = new OrderItemQueryParam();
            queryParam.setStatus(OrderStatusEnum.DELIVERING.getId());
            queryParam.setOrderNos(orderNos);
            List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.selectByCondition(queryParam);
            if (!CollectionUtils.isEmpty(orderItemEntities)) {
                //过滤已退款订单
                Set<String> deliveryOrderNos = orderItemEntities.stream().map(OrderItemEntity::getOrderNo).collect(Collectors.toSet());
                noticeList = noticeList.stream().filter(e -> deliveryOrderNos.contains(e.getOrderNo())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(noticeList)) {
                    DeliveryNotice deliveryNotice;
                    SendMessageInput sendMessageInput;
                    String url = WeChatUtils.getWeChatCode("/home.html#/loading?name=selfOrder&active=0");
                    for (DeliveryPlanFlatObject deliveryPlanFlatObject : noticeList) {
                        //发送微信消息提醒通知
                        deliveryNotice = new DeliveryNotice();
                        deliveryNotice.setDeliveryTime(deliveryPlanFlatObject.getDeliveryTime());
                        deliveryNotice.setContact(deliveryPlanFlatObject.getContact());
                        deliveryNotice.setOpenId(deliveryPlanFlatObject.getOpenid());
                        deliveryNotice.setOrderNo(deliveryPlanFlatObject.getOrderNo());
                        String message = DeliveryNoticeMsg.templateMessage(deliveryNotice);

                        sendMessageInput = new SendMessageInput();
                        sendMessageInput.setMessage(message);
                        sendMessageInput.setOpenId(deliveryPlanFlatObject.getOpenid());
                        sendMessageInput.setTemplateId(DeliveryNoticeMsg.TEMPLATE_ID);
                        sendMessageInput.setJumpUrl(url);
                        wxSendMessageFacade.sendMessage(sendMessageInput);
                    }
                }
            }

            if (size < pageSize) {
                log.info("DeliveryCommandServiceImpl[]orderDeliveryNotice[]pageIndex:{}", pageIndex);
                break;
            }

            pageIndex++;
        }
    }

    @Override
    public void timingDeliveryNotice(String orderNo) {
        int pageIndex = 1;
        int pageSize = 500;

        while (true) {
            int pageStart = pageSize * (pageIndex - 1);
            List<DeliveryPlanFlatObject> timingNoticeLists = deliveryPlanQueryRepository.timingNoticeLists(LocalDate.now().plusDays(2), orderNo, pageStart, pageSize);
            log.info("DeliveryCommandServiceImpl[]timingDeliveryNotice[]timingNoticeLists[]size:{}", timingNoticeLists.size());
            if (CollectionUtils.isEmpty(timingNoticeLists)) {
                log.info("DeliveryCommandServiceImpl[]timingDeliveryNotice[]timingNoticeLists is empty!");
                break;
            }

            DeliveryNotice deliveryNotice;
            SendMessageInput sendMessageInput;
            for (DeliveryPlanFlatObject timingNoticeList : timingNoticeLists) {
                deliveryNotice = new DeliveryNotice();
                deliveryNotice.setDeliveryTime(timingNoticeList.getDeliveryTime());
                deliveryNotice.setContact(timingNoticeList.getContact());
                deliveryNotice.setOpenId(timingNoticeList.getOpenid());
                deliveryNotice.setOrderNo(timingNoticeList.getOrderNo());
                deliveryNotice.setQuantity(timingNoticeList.getQuantity());
                deliveryNotice.setPdName(timingNoticeList.getPdName());
                deliveryNotice.setPhone(timingNoticeList.getPhone());
                String message = DeliveryNoticeMsg.timingDeliveryTemplateMessage(deliveryNotice);

                sendMessageInput = new SendMessageInput();
                sendMessageInput.setMessage(message);
                sendMessageInput.setOpenId(timingNoticeList.getOpenid());
                sendMessageInput.setTemplateId(DeliveryNoticeMsg.TIMING_DELIVERY_TEMPLATE_ID);
                wxSendMessageFacade.sendMessage(sendMessageInput);
            }

            if (timingNoticeLists.size() < pageSize) {
                log.info("DeliveryCommandServiceImpl[]timingDeliveryNotice[]pageIndex:{}", pageIndex);
                break;
            }

            pageIndex++;
        }
    }
}
