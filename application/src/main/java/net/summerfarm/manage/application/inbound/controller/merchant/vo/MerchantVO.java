package net.summerfarm.manage.application.inbound.controller.merchant.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactVO;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantOuterEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-19 10:55:24
 * @version 1.0
 *
 */
@Data
public class MerchantVO implements Serializable {
	private static final long serialVersionUID = 5640865689281001205L;

	/**
	 *
	 */
	@JsonProperty("mId")
	private Long mId;
	/**
	 * 用户中心门店id
	 */
	private Long storeId;

	/**
	 * 商户类型
	 */
	private Integer roleId;

	/**
	 * 商户名称
	 */
	private String mname;

	/**
	 * 主联系人
	 */
	private String mcontact;

	/**
	 * 微信用户id
	 */
	private String openid;

	/**
	 * 手机
	 */
	private String phone;

	/**
	 *   `status` 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、已关店 4、拉黑 5、注销'
	 */
	private Integer status;

	/**
	 *
	 */
	private Integer islock;

	/**
	 * 等级
	 */
	private Integer rankId;

	/**
	 * 注册时间
	 */
	private LocalDateTime registerTime;

	/**
	 * 登录时间
	 */
	private LocalDateTime loginTime;

	/**
	 * 6位邀请码
	 */
	private String invitecode;

	/**
	 * 用户分享码
	 */
	private String channelCode;

	/**
	 * 邀请人渠道码
	 */
	private String inviterChannelCode;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 审核人
	 */
	private Integer auditUser;

	/**
	 * 营业执照路径
	 */
	private String businessLicense;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 地区
	 */
	private String area;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 商家腾讯地图坐标
	 */
	private String poiNote;

	/**
	 * 审核备注
	 */
	private String remark;

	/**
	 * 店铺招牌
	 */
	private String shopSign;

	/**
	 * 其他证明照片
	 */
	private String otherProof;

	/**
	 * 上次下单时间
	 */
	private LocalDateTime lastOrderTime;

	/**
	 * 
	 */
	private Integer areaNo;

	/**
	 *     ADMIN(1, "大客户"),
	 *         MERCHANT(2, "单店"),
	 *         SAAS(3, "saas品牌客户");
	 */
	private String size;

	/**
	 *0-直营店 1-加盟店 2-托管店 3-个人店 4-连锁店 5-未知
	 */
	private String type;

	/**
	 * 门店经营类型
	 */
	private String businessType;

	/**
	 * 商圈
	 */
	private String tradeArea;

	/**
	 * 商圈组
	 */
	private String tradeGroup;

	/**
	 * 
	 */
	private String unionid;

	/**
	 * 
	 */
	private String mpOpenid;

	/**
	 * 用户ID
	 */
	private Long adminId;

	/**
	 *  merchant 里取  1 账期 2  现结
	 */
	private Integer direct;

	/**
	 * 1服务区内 2服务区外
	 */
	private Integer server;

	/**
	 * 
	 */
	private Integer popView;

	/**
	 * 会员当月积分
	 */
	private BigDecimal memberIntegral;

	/**
	 * 会员等级
	 */
	private Integer grade;

	/**
	 * 
	 */
	private Integer skuShow;

	/**
	 * 余额
	 */
	private BigDecimal rechargeAmount;

	/**
	 * 可提现金额
	 */
	private BigDecimal cashAmount;

	/**
	 * cash_amount更新时间
	 */
	private LocalDateTime cashUpdateTime;

	/**
	 * 配送单是否展示价格
	 */
	private Integer showPrice;

	/**
	 * 账号合并人
	 */
	private String mergeAdmin;

	/**
	 * 账号和并时间
	 */
	private LocalDateTime mergeTime;

	/**
	 * 首次登录弹窗：0、未弹 1、已弹
	 */
	private Integer firstLoginPop;

	/**
	 * 更换账号绑定弹窗：0、未弹 1、已弹或未更换账号绑定
	 */
	private Integer changePop;

	/**
	 * 拉黑备注
	 */
	private String pullBlackRemark;

	/**
	 * 操作人
	 */
	private String pullBlackOperator;

	/**
	 * 门牌号
	 */
	private String houseNumber;

	/**
	 * 企业品牌/所属总部
	 */
	private String companyBrand;

	/**
	 * 是否选择线索池 0 不是 1 是
	 */
	private Integer cluePool;

	/**
	 * 大客户类型: ka,批发大客户,普通
	 */
	private String merchantType;

	/**
	 * 经营类型
	 */
	private String enterpriseScale;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 不通过审核类型 0人工  1系统
	 */
	private Integer examineType;

	/**
	 * 开关状态 0 开（展示） 1 关（不展示）
	 */
	private Integer displayButton;

	/**
	 * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
	 */
	private Integer operateStatus;

	/**
	 * 更新人adminId
	 */
	private Integer updater;

	/**
	 * 门头照片
	 */
	private String doorPic;


	/**
	 * 预注册标记,1- 预注册，0- 非预注册
	 */
	private Integer preRegisterFlag;

	/**
	 * 地推人员
	 */
	private String adminRealName;


	/**
	 * 大客户名称
	 */
	private String realName;

	/**
	 *运营服务大区域名称
	 */
	private String areaName;


	/**
	 * 工商名称？？？ 发票本身表里面拿
	 */
	private String invoiceTitle;

	/**
	 * 联系人信息
	 */

	private List<ContactVO> contacts;

	/**
	 * 大客户属性
	 */
	private AdminEntity adminEntity;

	/**
	 * 邀请店铺
	 */
	private String inviterMerchantName;
	/**
	 * 外部情况
	 */
	List<MerchantOuterEntity> outerMappings;
	/**
	 * 大客户合作方式
	 */
	private String contractMethod;

	private Boolean printOutTMSConfig;

	/**
	 * 是否pop客户
	 */
	private boolean popMerchant;

	/**
	 * 业务线:0=鲜沐;1=pop
	 */
	private Integer businessLine;

	/**
	 * 门店主业类型。（仅有一个）
	 */
	private String mainBusinessType;

	/**
	 * 门店副业类型：多个，需使用英文逗号 , 分隔
	 */
	private String sideBusinessType;

	/**
	 * 连锁范围:0（NKA-全国连锁）1(LKA-区域连锁) 2(其他连锁) 3- 跨区域连锁 4- 工厂 99-单店
	 */
	private Integer merchantChainType;
}