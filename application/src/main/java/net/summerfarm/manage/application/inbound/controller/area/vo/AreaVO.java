package net.summerfarm.manage.application.inbound.controller.area.vo;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class AreaVO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 城市编号
     */
    @ApiModelProperty(value = "城市编号（运营服务区）")
    private Integer areaNo;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称（运营服务区）")
    private String areaName;

    /**
     * 城市负责人
     */
    private Integer adminId;

    /**
     * 父城市编号
     */
    private Integer parentNo;

    /**
     * 配送频率
     */
    private String deliveryFrequent;

    /**
     * 是否开放，0不开放，1开放
     */
    private Byte status;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 备注信息
     */
    private String info;

    /**
     * 地址
     */
    private String address;

    /**
     * 快递费
     */
    private BigDecimal expressFee;

    /**
     * 运费规则
     */
    private String deliveryRule;

    /**
     * 会员规则
     */
    private String memberRule;

    /**
     * 收款账号（默认5：杭州）
     */
    private Integer companyAccountId;

    /**
     * 高德地图poi坐标
     */
    private String poiNote;

    /**
     * 仓库类型:0本部仓、1外部仓、2合伙人仓
     */
    private Integer type;

    /**
     * 免配送费日期（结构同delivery_frequent）
     */
    private String freeDay;

    /**
     * 预约入库邮件接收人
     */
    private String mailToAddress;

    /**
     * 截单映射区域（形如 浙江/杭州市/西湖区，多个区域用“,”隔开，其他区域表示市级映射）
     */
    private String mapSection;

    /**
     * 同步城市编号
     */
    private Integer originAreaNo;

    /**
     * 开始/下次配送时间
     */
    private Date nextDeliveryDate;

    /**
     * 支付通道，0微信 1中银 2招行
     */
    private Integer payChannel;

    /**
     * 仓库切换标识：0、未预约切换 1、切换中
     */
    private Integer changeFlag;

    /**
     * 切换的城市编号（处理完后清空）
     */
    private Integer changeStoreNo;

    /**
     * 切换状态：0、默认 1、预约中 2、大客户停服 3、城市停服
     */
    private Integer changeStatus;

    /**
     * 根据国家行政区域划分，精确到市
     */
    private String administrativeArea;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否支持加单 0 支持加单 1 不支持加单
     */
    private Byte supportAddOrder;

    /**
     * 更新是否支持加单
     */
    private Byte updateSupportAddOrder;

    /**
     * 运营大区编号
     */
    private Integer largeAreaNo;

    /**
     * 区域等级:S.A.B.C.D
     */
    private String grade;

    /**
     * 微信小程序是否使用招行收款：0、不使用（默认）1、使用
     */
    private Integer wxlitePayChannel;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

