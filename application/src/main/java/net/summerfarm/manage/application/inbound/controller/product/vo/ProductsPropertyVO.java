package net.summerfarm.manage.application.inbound.controller.product.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ProductsPropertyVO
 * @Description
 * <AUTHOR>
 * @Date 17:05 2024/5/8
 * @Version 1.0
 **/
@Data
public class ProductsPropertyVO implements Serializable {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 属性名
     */
    private String name;

    /**
     * 属性类型：0、关键属性 1、销售属性
     */
    private Integer type;

    /**
     * 格式类型：0、复合型 1、数字+单位 2、选择字符串 3、自定义字符串
     */
    private Integer formatType;

    /**
     * 格式，根据类型不同处理，eg：
     0/1/2:["g","kg","箱"]
     3:["越南","海南","台湾"]
     */
    private String formatStr;
}
