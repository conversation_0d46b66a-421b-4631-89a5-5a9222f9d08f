package net.summerfarm.manage.application.service.searchSynonym;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query.ProductSearchSynonymDictionaryQueryInput;

/**
 *
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
public interface ProductSearchSynonymDictionaryQueryService {

    /**
     * @description: 新增
     * @return ProductSearchSynonymDictionaryEntity
     **/
    PageInfo<ProductSearchSynonymDictionaryEntity> getPage(ProductSearchSynonymDictionaryQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    ProductSearchSynonymDictionaryEntity getDetail(Long id);

}