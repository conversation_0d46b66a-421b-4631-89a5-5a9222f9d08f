package net.summerfarm.manage.application.inbound.controller.merchant.vo.contact;

import lombok.Data;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ContactAdjustVO extends MerchantVO implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;

    /**
     * 记录id
     */
    private Integer contactAdjustId;

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 调整记录
     */
    private ContactAdjustEntity contactAdjustEntity;
    /**
     * 调整时间
     */
    private LocalDateTime contactAdjustTime;
    /**
     * 联系人信息
     */
    private ContactVO contactVO;
}

