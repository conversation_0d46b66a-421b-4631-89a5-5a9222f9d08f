package net.summerfarm.manage.application.service.dts;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.job.CrmJobMerchantDetailCommandService;
import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.summerfarm.manage.common.dto.DtsModel;
import net.summerfarm.manage.common.dto.XmPair;
import net.summerfarm.manage.common.enums.dts.DtsModelTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @ClassName
 * @Description
 * <AUTHOR>
 * @Date 19:00 2024/4/26
 * @Version 1.0
 **/
@Slf4j
@Service
public class DeliveryPlanDmlImpl extends AbstractDbTableDml {

    @Resource
    private CrmJobMerchantDetailCommandService crmJobMerchantDetailCommandService;

    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.UPDATE.name())) {
            this.handlerMerchantJob(pair);
        }
    }


    // 配送完成之后处理门店任务
    private void handlerMerchantJob(XmPair<Map<String, String>, Map<String, String>> pair) {
        try {
            Map<String, String> data = pair.getKey();
            Map<String, String> old = pair.getValue();
            Integer status = Optional.ofNullable(data.get("status")).map(Integer::valueOf).orElse(null);
            Integer oldStatus = Optional.ofNullable(old.get("status")).map(Integer::valueOf).orElse(null);
            if(oldStatus == null) {
                log.info("【履约完成门店任务处理】履约计划状态未发生变化");
                return;
            }
            if (Objects.equals(status, 6)) {
                String orderNo = data.get("order_no");
                crmJobMerchantDetailCommandService.awardRewardAfterDelivery(orderNo);
            }
        } catch (Exception e) {
            log.info("发履约完成后处理任务失败。data：{}", JSON.toJSONString(pair), e);
        }
    }
}
