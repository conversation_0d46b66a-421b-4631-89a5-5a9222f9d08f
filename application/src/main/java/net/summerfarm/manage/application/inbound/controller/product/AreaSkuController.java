package net.summerfarm.manage.application.inbound.controller.product;

import net.summerfarm.manage.application.inbound.controller.product.input.query.AreaSkuMinPriceQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.AreaSkuVO;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/16 14:39
 * @PackageName:net.summerfarm.manage.application.inbound.controller.price
 * @ClassName: AreaSkuController
 * @Description: TODO
 * @Version 1.0
 */
@RestController
@RequestMapping(value="/areaSku")
public class AreaSkuController {

    @Resource
    private AreaSkuQueryService areaSkuQueryService;

    /**
     * 根据sku+仓 查询区域最低售后
     * <AUTHOR> @date 2024/11/15 15:18
     */
    @PostMapping("/query/minPrice")
    public CommonResult<List<AreaSkuVO>> queryMinPrice(@RequestBody @Validated List<AreaSkuMinPriceQueryInput> inputs) {
        return CommonResult.ok(areaSkuQueryService.queryMinPrice(inputs));
    }
}
