package net.summerfarm.manage.application.inbound.controller.job.vo;

import lombok.Data;
import net.xianmu.jobsdk.enums.CrmJobEnum;

import java.time.LocalDateTime;

@Data
public class MerchantJobVo {

    /**
     * 任务id
     */
    private Long id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务类型
     * @see CrmJobEnum.Type#getCode()
     */
    private Integer type;

    /**
     * @see CrmJobEnum.MerchantSelectionType#getCode()
     */
    private Integer rewardType;

    /**
     * @see CrmJobEnum.Status#getCode()
     */
    private Integer status;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 创建人adminId
     */
    private Long creator;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
