package net.summerfarm.manage.application.service.merchant;

import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelInsertInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelUpdateInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantCancelVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:30:16
 */
public interface MerchantCancelCommandService {


    /**
     * @description: 申请注销
     * @author: lzh
     * @date: 2023/4/20 17:47
     * @param: [merchantCancelInsertReq]
     * @return: net.summerfarm.model.vo.MerchantCancelVO
     **/
    MerchantCancelVO insert(MerchantCancelInsertInput input);

    /**
     * @description: 更新门店注销状态
     * @author: lzh
     * @date: 2023/4/20 17:47
     * @param: [merchantCancelReq]
     * @return: java.lang.Boolean
     **/
    Boolean updateStatus(MerchantCancelUpdateInput input);

    /**
     * @description: 立即注销
     * @author: lzh
     * @date: 2023/8/7 11:03
     * @param: [merchantCancelReq]
     * @return: net.summerfarm.model.vo.MerchantCancelVO
     **/
    MerchantCancelVO promptlyCancel(MerchantCancelUpdateInput input);
}
