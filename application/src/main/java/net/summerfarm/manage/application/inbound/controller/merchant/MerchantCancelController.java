package net.summerfarm.manage.application.inbound.controller.merchant;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelInsertInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelPageQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelUpdateInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantCancelVO;
import net.summerfarm.manage.application.service.merchant.MerchantCancelCommandService;
import net.summerfarm.manage.application.service.merchant.MerchantCancelQueryService;
import net.summerfarm.manage.common.constants.Global;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:15:51
 */
@RestController
@RequestMapping("/merchant/cancel")
public class MerchantCancelController {

    @Resource
    private MerchantCancelQueryService merchantCancelQueryService;
    @Resource
    private MerchantCancelCommandService merchantCancelCommandService;

    /**
     * 分页获取门店注销列表
     * @param merchantCancelPageQuery
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/page")
    @RequiresPermissions(value = {"merchant-cancel:select", Global.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<MerchantCancelVO>> getPage(@RequestBody MerchantCancelPageQueryInput merchantCancelPageQuery) {
        return CommonResult.ok(merchantCancelQueryService.getPage(merchantCancelPageQuery));
    }

    /**
     * 申请注销
     * @param merchantCancelInsertReq
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/insert")
    @RequiresPermissions(value = {"merchant-cancel:insert", Global.SA}, logical = Logical.OR)
    public CommonResult<MerchantCancelVO> insert(@RequestBody @Valid MerchantCancelInsertInput merchantCancelInsertReq) {
        return CommonResult.ok(merchantCancelCommandService.insert(merchantCancelInsertReq));
    }

    /**
     * 获取门店注销申请详情
     * @param merchantCancelReq
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/detail")
    @RequiresPermissions(value = {"merchant-cancel:select", Global.SA}, logical = Logical.OR)
    public CommonResult<MerchantCancelVO> getDetail(@RequestBody MerchantCancelQueryInput merchantCancelReq) {
        return CommonResult.ok(merchantCancelQueryService.getDetail(merchantCancelReq));
    }

    /**
     * 更新门店注销状态
     * @param merchantCancelReq
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/update-status")
    @RequiresPermissions(value = {"merchant-cancel:update", Global.SA}, logical = Logical.OR)
    public CommonResult<Boolean> updateStatus(@RequestBody MerchantCancelUpdateInput merchantCancelReq) {
        return CommonResult.ok(merchantCancelCommandService.updateStatus(merchantCancelReq));
    }

    /**
     * 校验门店是否可以注销
     * @param merchantCancelReq
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/check")
    public CommonResult<MerchantCancelVO> check(@RequestBody MerchantCancelQueryInput merchantCancelReq) {
        return CommonResult.ok(merchantCancelQueryService.check(merchantCancelReq));
    }


    /**
     * 立即注销
     * @param input
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/cancel")
    @RequiresPermissions(value = {"merchant-cancel:insert", Global.SA}, logical = Logical.OR)
    public CommonResult<MerchantCancelVO> promptlyCancel(@RequestBody @Valid MerchantCancelUpdateInput input) {
        return CommonResult.ok(merchantCancelCommandService.promptlyCancel(input));
    }

}
