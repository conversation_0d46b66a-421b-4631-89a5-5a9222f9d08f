package net.summerfarm.manage.application.service.product.mall.impl;
import java.util.List;

import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.major.param.command.MajorPriceLogCommandParam;
import net.summerfarm.manage.domain.major.service.MajorPriceLogCommandDomainService;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.manage.domain.major.utils.PriceCalculator;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceUpdateBatchInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.SkuAreaWarehouseNoMapVO;
import net.summerfarm.manage.application.service.major.converter.MajorPriceLogConvert;
import net.summerfarm.manage.application.service.product.converter.MajorPriceConvert;
import net.summerfarm.manage.application.service.product.mall.MajorPriceCommandService;
import net.summerfarm.manage.common.enums.DingTalkMsgTypeEnum;
import net.summerfarm.manage.common.enums.MajorPriceStatusEnum;
import net.summerfarm.manage.common.enums.MajorPriceTypeEnum;
import net.summerfarm.manage.domain.activity.service.ActivityBasicInfoQueryDomainService;
import net.summerfarm.manage.domain.activity.valueObject.ActivitySkuValueObject;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyValueQueryRepository;
import net.summerfarm.manage.domain.product.service.MajorPriceCommandDomainService;
import net.summerfarm.manage.domain.product.service.ProductDomainService;
import net.summerfarm.manage.facade.fence.FenceQueryFacade;
import net.summerfarm.manage.facade.goods.ProductFacade;
import net.summerfarm.manage.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.manage.facade.inventory.dto.ProductCostQueryDto;
import net.summerfarm.manage.facade.message.FeiShuPersonalMsgFacade;
import net.summerfarm.manage.facade.message.input.FeiShuMsgInput;
import net.summerfarm.manage.facade.wnc.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-04-08 15:19:21
 */
@Service
@Slf4j
public class MajorPriceCommandServiceImpl implements MajorPriceCommandService {

    @Resource
    private AdminQueryRepository adminQueryRepository;
    @Resource
    private MajorPriceQueryRepository majorPriceQueryRepository;
    @Resource
    private MajorPriceCommandDomainService majorPriceCommandDomainService;
    @Resource
    private ActivityBasicInfoQueryDomainService activityBasicInfoQueryDomainService;
    @Resource
    private FeiShuPersonalMsgFacade feiShuPersonalMsgFacade;
    @Resource
    private ProductCostQueryFacade productCostQueryFacade;
    @Resource
    private FenceQueryFacade fenceQueryFacade;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private ProductFacade productFacade;
    @Autowired
    private AreaSkuQueryService areaSkuQueryService;
   @Autowired
    private ProductsPropertyValueQueryRepository productsPropertyValueQueryRepository;
    @Autowired
    private MajorPriceLogCommandDomainService majorPriceLogCommandDomainService;
    @Autowired
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;
    @Autowired
    private AreaSkuQueryRepository areaSkuQueryRepository;
    @Override
    public void newLowPriceRemainder(Integer adminId, Integer areaNo, String sku) {
        if(adminId == null && areaNo == null) {
            log.warn("缺失关键参数：adminId:{}, areaNo:{}, sku:{}", adminId, areaNo, sku);
            throw new BizException("参数缺失!");
        }
        log.info("开始进入新报价单低价监控处理：adminId:{}, areaNo:{}, sku:{}", adminId, areaNo, sku);
        AdminEntity AdminEntity = adminQueryRepository.selectByPrimaryKey(Long.valueOf(adminId));
        if (AdminEntity.getLowPriceRemainder() != 1) {
            log.info("大客户：[{}]未开启低价监控", adminId);
            return;
        }
        //指定城市不触发低价监控
        if (StringUtils.isNotBlank(AdminEntity.getNotIncludedArea())) {
            String[] split = AdminEntity.getNotIncludedArea().split(",");
            if (Arrays.stream(split).anyMatch(el -> Objects.equals(el, String.valueOf(areaNo)))) {
                log.info("大客户：[{}]在该城市：[{}]未开启低价监控", adminId, areaNo);
                return;
            }
        }

        //一个大客户同一个sku/areaNo最多两条生效的报价单，账期、现结
        List<MajorPriceLowRemainder> majorList = majorPriceQueryRepository.selectLowPriceRemainderSku(adminId, areaNo, sku);

        // 过滤掉指定价、商城价
        majorList = majorList.stream().filter(x -> x.getPriceType().equals(MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode())).collect(Collectors.toList());

        // 计算实时报价
        majorList.forEach(this::setCurrentMajorPrice);

        // 计算活动价
        Map<String, List<MajorPriceLowRemainder>> majorPriceMap = majorList.stream().filter(x -> x.getPrice() != null)
                .collect(Collectors.toMap(MajorPriceLowRemainder::getSku, Lists::newArrayList, (a, b) -> {
                    a.addAll(b);
                    return a;
                }));
        Set<String> skus = majorPriceMap.keySet();
        List<ActivitySkuValueObject> activitySkuList = activityBasicInfoQueryDomainService.listActivitySku(Lists.newArrayList(skus),
                areaNo);
        if (CollectionUtil.isNotEmpty(activitySkuList)) {
            Map<String, BigDecimal> activitySkuMap = activitySkuList.stream()
                    .filter(x -> x.getActivityPrice() != null)
                    .collect(Collectors.toMap(ActivitySkuValueObject::getSku, ActivitySkuValueObject::getActivityPrice));
            majorList.stream().filter(x -> activitySkuMap.get(x.getSku()) != null).forEach(x -> x.setActivityPrice(activitySkuMap.get(x.getSku())));
        }

        // 获取到最优价格
        majorList.forEach(MajorPriceLowRemainder::initCurrentMinPrice);

        // 过滤掉价格未发生变化的
        List<MajorPriceLowRemainder> voList = majorList.stream()
                .filter(x -> x.getPrice() != null && x.getCurrentMinPrice() != null && x.getCurrentMinPrice().compareTo(x.getPrice()) != 0).collect(Collectors.toList());


        if (CollectionUtils.isEmpty(voList)) {
            log.info("低价监控结束，无需要调整的数据。adminId:{}, areaNo:{}, sku:{}", adminId, areaNo, sku);
            return;
        }


        List<Long> userId = new ArrayList<>();
        if (Objects.nonNull(AdminEntity.getSalerId())) {
            userId.add(AdminEntity.getSalerId().longValue());
        }
        if (Objects.nonNull(AdminEntity.getOperateId())) {
            userId.add(AdminEntity.getOperateId().longValue());
        }

        for (MajorPriceLowRemainder mplr : voList) {
            BigDecimal oldPrice = mplr.getPrice();
            //更新报价单价格
            MajorPriceCommandParam param = new MajorPriceCommandParam();
            param.setId(mplr.getId());
            param.setPrice(mplr.getCurrentMinPrice());
            param.setLowPriceUpdateTime(LocalDateTime.now());
            majorPriceCommandDomainService.update(param);
            log.info("低价监控自动更新了报价单价格，大客户：{}，城市：{}，原报价：{}，当前报价：{}，报价单: {}", adminId, areaNo, oldPrice, mplr.getCurrentMinPrice(), JSON.toJSONString(mplr));

            //发送低价钉钉消息
            if (!CollectionUtils.isEmpty(userId) && oldPrice.compareTo(mplr.getCurrentMinPrice()) > 0) {
                try {
                    String title = "【大客户低价监控提醒】";
                    StringBuilder content = new StringBuilder("##### " + title + "\n");
                    content.append("> ###### ").append(AdminEntity.getRealname());
                    content.append("> ###### ").append("（" + AdminEntity.getNameRemakes() + "） ");
                    content.append("> ###### ").append("因商城价影响，当前品牌报价单价格更新为:" ).append(mplr.getCurrentMinPrice()).append("\n");
                    content.append("> ###### ").append(mplr.getPdName()).append(" ").append(mplr.getWeight()).append("\n");
                    content.append("> ###### SKU：").append(mplr.getSku()).append("\n");
                    content.append("> ###### 城市：").append(mplr.getAreaName()).append("\n");
                    content.append("> ###### 当前客户价格：").append(oldPrice).append("\n");
                    BigDecimal activitySkuPrice = mplr.getActivityPrice();
                    BigDecimal salePrice = mplr.getMallPrice();
                    if (activitySkuPrice == null) {
                        content.append("> ###### 当前商城价格：").append(salePrice).append("\n");
                    } else {
                        content.append("> ###### 当前商城价格：").append(salePrice).append("\n");
                        content.append("> ###### 当前活动价格：").append(activitySkuPrice).append("\n");
                    }
                    content.append("> ###### 发送时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
                    Map<String, String> map = new HashMap<>();
                    map.put("title", title);
                    map.put("text", content.toString());
                    FeiShuMsgInput input = new FeiShuMsgInput();
                    //todo:zyt
                    input.setReceiverIdList(userId);
                    input.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
                    input.setTitle(title);
                    input.setText(content.toString());
                    feiShuPersonalMsgFacade.sendMessageWithFeiShu(input);
                } catch (Exception e) {
                    log.warn("【大客户低价监控提醒】发送飞书消息失败,客户名:{}", AdminEntity.getNameRemakes(), e);
                }
            }
        }
    }

    @Override
    public void majorPriceMallShowBatchUpdate(Integer mallShow, Integer direct, Integer adminId) {
        majorPriceQueryRepository.majorPriceMallShowBatchUpdate (mallShow,  direct,  adminId);
    }

    private static boolean isOverlap(LocalDateTime start1, LocalDateTime end1, LocalDateTime start2, LocalDateTime end2) {
        return (start1.isBefore(end2) || start1.isEqual(end2)) && (end1.isAfter(start2) || end1.isEqual(start2));
    }

    public void validateSkus(Set<String> skus){
        List<ProductsPropertyValueEntity> productsPropertyValueEntities = productsPropertyValueQueryRepository.selectSaleValueBySkuListAndPropertyIds (skus, Collections.singletonList (ProductDomainService.NOMAL));
        if(CollectionUtils.isEmpty (productsPropertyValueEntities)){
            log.info ("新增报价单返回，sku属性空,inputs={}",JSON.toJSONString (skus));
            return;
        }
        Set<String> notNomalSkuList = productsPropertyValueEntities.stream ().filter (productsPropertyValue -> !Objects.equals (productsPropertyValue.getProductsPropertyValue (), "常规")).map (ProductsPropertyValueEntity::getSku).collect (Collectors.toSet ());
        if(CollectionUtil.isNotEmpty (notNomalSkuList)){
            throw new BizException ("非常规sku不可报价,sku=" + JSON.toJSONString (notNomalSkuList));
        }
    }
    public void validateParameters(MajorPriceInput input,String title){
        LocalDateTime validTime = input.getValidTime();
        LocalDateTime invalidTime = input.getInvalidTime();

        //失效日期小于等于生效日期
        if (invalidTime.isBefore(validTime) || invalidTime.isEqual(validTime)) {
            throw new BizException (title + "失效日期应大于生效日期");
        }
        //失效日期不能早于现在时刻
        if (validTime.isBefore(invalidTime) && invalidTime.isBefore(LocalDateTime.now())) {
            throw new BizException(title + "失效日期不能早于现在时刻");
        }
    }

    @Override
    @Transactional
    public void addMajorPrice(List<MajorPriceInput> inputs) {
        if(CollectionUtils.isEmpty (inputs)){
            return;
        }
        Integer adminId = inputs.get (0).getAdminId();
        Integer direct = inputs.get (0).getDirect();

        //非常规sku不可报价
        validateSkus(inputs.stream().map (MajorPriceInput::getSku).collect(Collectors.toSet ()));

        //查询 sku 对应可以报价的运营区域
        SkuAreaWarehouseNoMapVO skuAreaWarehouseNoMapVO = areaSkuQueryService.queryAvailableAreaMap (inputs.stream ().collect (Collectors.groupingBy (MajorPriceInput::getSku, Collectors.mapping (MajorPriceInput::getLargeAreaNo, Collectors.toList ()))),inputs.stream ().map (MajorPriceInput::getLargeAreaNo).collect (Collectors.toList ()));
        Map<String, Set<Integer>> availableAreaMap = skuAreaWarehouseNoMapVO.getSkuAreaNoMap ();
        Map<String, Integer> skuAreaWarehouseNoMap = skuAreaWarehouseNoMapVO.getSku_AreaWarehouseNoMap ();
        Map<Integer, AreaSimpleEntity> areaMap = skuAreaWarehouseNoMapVO.getAreaMap ();
        Map<String, List<AreaSkuEntity>> skuAreaSkuMap = skuAreaWarehouseNoMapVO.getSkuAreaSkuMap ();
        if(CollectionUtils.isEmpty (availableAreaMap)){
            return;
        }
        Set<String> skus = availableAreaMap.keySet ();
        Set<Integer> areanos = availableAreaMap.values().stream() .flatMap(Set::stream).collect(Collectors.toSet ());

        //查询货品
        List<ProductSkuBaseResp> skuList = productFacade.querySkuInfoBySkuList (new ArrayList<> (skus));
        if(CollectionUtils.isEmpty (skuList)){
            log.info ("新增报价单返回，没有可用sku,inputs={}",JSON.toJSONString (inputs));
            return;
        }
        Map<String, ProductSkuBaseResp> skuInfoMap = skuList.stream().collect(Collectors.toMap (ProductSkuBaseResp::getSku, Function.identity()));

        //查询已经存在的报价单
        List<MajorPriceEntity> majorPriceEntities = majorPriceQueryRepository.queryListMajorPriceWithoutTime (direct,Long.valueOf (adminId), skus, areanos);
        Map<String, List<MajorPriceEntity>> dbMajorPriceMap;
        if(CollectionUtil.isNotEmpty (majorPriceEntities)){
            dbMajorPriceMap  = majorPriceEntities.stream().collect(Collectors.groupingBy(MajorPriceEntity::getSku));
        } else {
            dbMajorPriceMap = Collections.emptyMap ();
        }

        // 查询成本
        Map<String, ProductCostQueryResp> productCostQueryRespMap = productCostQueryFacade.selectMapBySkuAndAreaNosAndWarehouseNoMap(availableAreaMap,skuAreaWarehouseNoMap);

        List<Integer> removeIds = new ArrayList<> ();
        List<MajorPriceCommandParam> updateList = new ArrayList<> ();
        List<MajorPriceCommandParam> addList = new ArrayList<> ();
        List<MajorPriceLogCommandParam> logList = new ArrayList<> ();
        inputs.forEach(item -> {

            //获取基本信息
            String sku = item.getSku();
            Set<Integer> areaNos = availableAreaMap.get (sku);
            if(CollectionUtil.isEmpty (areaNos)){
                return;
            }
            Integer largeAreaNo = item.getLargeAreaNo ();
            LocalDateTime validTime = item.getValidTime();
            LocalDateTime invalidTime = item.getInvalidTime();

            String title = sku + "运营大区编码" + largeAreaNo + ": ";

            validateParameters(item,title);

            ProductSkuBaseResp skuItem = skuInfoMap.get (sku);
            if(skuItem == null){
                log.warn ("sku={}不存在",sku);
                return;
            }
            String pdName = skuItem.getTitle ();
            String weight = skuItem.getSpecification ();
            //获取padName
            if (Objects.equals(skuItem.getAgentType (), AgentTypeEnum.AGENT.getType ())) {
                if (skuItem.getTitle () != null && skuItem.getTitle ().contains("-")) {
                    String[] split = skuItem.getTitle ().split("-");
                    pdName = split[0];
                }
                if (!Objects.equals(skuItem.getOwnerId (), adminId)) {
                    throw new BizException (title + "不是本大客户代仓品 不允许报价");
                }
            }

            String finalPdName = pdName;
            areaNos.forEach (areaNo->{
                AreaSimpleEntity areaSimpleEntity = areaMap.get (areaNo);
                if(largeAreaNo != areaSimpleEntity.getLargeAreaNo ()){
                    return;
                }
                String areaName = areaSimpleEntity.getAreaName ();

                ProductCostQueryResp productCostQueryResp = productCostQueryRespMap.get(sku + "_" + areaNo);
                MajorPriceCommandParam param;

                if(dbMajorPriceMap.containsKey (sku)){
                    List<MajorPriceEntity> dbPriceList = dbMajorPriceMap.get (sku).stream ().filter (majorPriceEntity -> areaNo.equals (majorPriceEntity.getAreaNo ())).collect (Collectors.toList ());

                    if(CollectionUtil.isNotEmpty (dbPriceList)){
                        List<MajorPriceEntity> isOverlapList = dbPriceList.stream ().filter (e -> isOverlap (e.getValidTime (), e.getInvalidTime (), validTime,invalidTime)).collect (Collectors.toList ());
                        //如果有时间重叠的，则修改第一个 剩下的删除
                        if (CollectionUtil.isNotEmpty (isOverlapList)) {
                            MajorPriceEntity e = isOverlapList.get (0);
                            if(isOverlapList.size () > 1){
                                removeIds.addAll (isOverlapList.stream ().map (MajorPriceEntity::getId).collect (Collectors.toList ()));
                                removeIds.remove (e.getId ());
                            }
                            param = MajorPriceConvert.INSTANCE.input2Param (areaNo,areaName,item);
                            // 更新
                            param.setId (e.getId ());
                            fillMajorPriceParam (param,productCostQueryResp,weight, finalPdName);
                            addLogEntity(param,logList,productCostQueryResp,skuAreaSkuMap.getOrDefault (sku,new ArrayList<> ()).stream().filter (i->i.getAreaNo ().equals (areaNo)).findFirst ().orElseGet (null));
                            updateList.add (param);
                        }else{
                            //如果有未来生效的则覆盖，保证未来生效的报价单只有一个，提高列表查询效率
                            List<MajorPriceEntity> dbs = dbPriceList.stream ().filter (e -> (e.getValidTime ().isAfter (LocalDateTime.now ())  && validTime.isAfter (LocalDateTime.now ())) || e.getStatus ().equals (MajorPriceStatusEnum.SAVE.getType ())).collect (Collectors.toList ());
                            if(CollectionUtil.isNotEmpty (dbs)){
                                dbs.forEach (e -> {
                                    MajorPriceCommandParam p = MajorPriceConvert.INSTANCE.input2Param (areaNo, areaName,item);
                                    p.setId (e.getId ());
                                    fillMajorPriceParam (p,productCostQueryResp,weight, finalPdName);
                                    addLogEntity(p,logList,productCostQueryResp,skuAreaSkuMap.getOrDefault (sku,new ArrayList<> ()).stream().filter (i->i.getAreaNo ().equals (areaNo)).findFirst ().orElseGet (null));
                                    updateList.add (p);
                                });
                            }else {
                                param = MajorPriceConvert.INSTANCE.input2Param (areaNo,areaName, item);
                                fillMajorPriceParam (param, productCostQueryResp, weight, finalPdName);
                                addLogEntity (param, logList, productCostQueryResp,skuAreaSkuMap.getOrDefault (sku,new ArrayList<> ()).stream().filter (i->i.getAreaNo ().equals (areaNo)).findFirst ().orElseGet (null));
                                addList.add (param);
                            }
                        }

                    }else{
                        param = MajorPriceConvert.INSTANCE.input2Param (areaNo,areaName,item);
                        fillMajorPriceParam (param,productCostQueryResp,weight, finalPdName);
                        addLogEntity(param,logList,productCostQueryResp,skuAreaSkuMap.getOrDefault (sku,new ArrayList<> ()).stream().filter (i->i.getAreaNo ().equals (areaNo)).findFirst ().orElseGet (null));
                        addList.add (param);
                    }
                }else{
                    param = MajorPriceConvert.INSTANCE.input2Param (areaNo,areaName,item);
                    fillMajorPriceParam (param,productCostQueryResp,weight, finalPdName);
                    addLogEntity(param,logList,productCostQueryResp,skuAreaSkuMap.getOrDefault (sku,new ArrayList<> ()).stream().filter (i->i.getAreaNo ().equals (areaNo)).findFirst ().orElseGet (null));
                    addList.add (param);
                }
            });
        });
        if(CollectionUtil.isNotEmpty (removeIds)){
            majorPriceCommandDomainService.removeByIds (removeIds);
        }
        if(CollectionUtil.isNotEmpty (updateList)){
            majorPriceCommandDomainService.updateBatch (updateList);
        }
        if(CollectionUtil.isNotEmpty (addList)){
            majorPriceCommandDomainService.insertBatch (addList);
        }
        if(CollectionUtil.isNotEmpty (logList)){
            majorPriceLogCommandDomainService.insertBatch (logList);
        }
    }

    @Override
    public void commitBatch(List<Long> ids) {
        List<MajorPriceEntity> majorPriceEntities = majorPriceQueryRepository.queryListMajorPriceByIds (ids);
        if(CollectionUtil.isEmpty (majorPriceEntities)){
            throw new BizException ("报价单不存在");
        }

        majorPriceCommandDomainService.commitBatch (ids);
    }

    @Override
    @Transactional
    public void updateBatch(MajorPriceUpdateBatchInput input) {
         LocalDateTime validTime = input.getValidTime();
         LocalDateTime invalidTime = input.getInvalidTime();

        List<Long> ids = input.getIds ();
        List<MajorPriceEntity> majorPriceEntities = majorPriceQueryRepository.queryListMajorPriceByIds (ids);
        if(CollectionUtil.isEmpty (majorPriceEntities)){
            throw new BizException ("报价单不存在");
        }
        MajorPriceEntity majorPriceEntity = majorPriceEntities.get (0);
        Integer adminId = majorPriceEntity.getAdminId ();
        Integer direct = majorPriceEntity.getDirect ();
        Set<String> skus = majorPriceEntities.stream ().map (MajorPriceEntity::getSku).collect (Collectors.toSet ());
        Set<Integer> areanos = majorPriceEntities.stream ().map (MajorPriceEntity::getAreaNo).collect (Collectors.toSet ());

        List<Integer> removeIds = new ArrayList<> ();

        List<MajorPriceCommandParam> updateList = new ArrayList<> ();
        List<MajorPriceLogCommandParam> logList = new ArrayList<> ();

        //查询其他重复的报价单
        List<MajorPriceEntity> majorPriceEntitiesOther = majorPriceQueryRepository.queryListMajorPriceWithoutTime (direct,Long.valueOf (adminId), skus, areanos).stream().filter (e-> !ids.contains (Long.valueOf (e.getId ()))).collect(Collectors.toList());

        //用围栏状态过滤areano
        WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq ();
        List<WarehouseBySkuAreaNoDataReq> areaSkuReqs = new ArrayList<> ();
        majorPriceEntities.stream().collect(Collectors.groupingBy(MajorPriceEntity::getSku)).forEach ((sku, areaSkuList)->{
            WarehouseBySkuAreaNoDataReq reqData = new WarehouseBySkuAreaNoDataReq ();
            reqData.setSku (sku);
            reqData.setAreaNoList (areaSkuList.stream().map (MajorPriceEntity::getAreaNo).collect(Collectors.toList()));
            areaSkuReqs.add (reqData);
        });
        req.setAreaSkuList (areaSkuReqs);
        List<WarehouseBySkuAreaNoResp> resps = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo (req);
        Map<String, String> areaSkuWarehouseMap = resps.stream().collect(Collectors.toMap(
                item -> item.getAreaNo() + item.getSku(),
                item -> item.getWarehouseNo() + "|" + item.getSku(),
                (existingValue, newValue) -> existingValue + "," + newValue // 合并函数
        ));
        // 获取成本集合
        List<ProductCostQueryDto> reqList = resps.stream().map(MajorPriceConvert.INSTANCE::resp2ProductCostQueryDto).collect(Collectors.toList());
        Map<String, ProductCostQueryResp> costQueryRespMap = productCostQueryFacade.selectMapBySkuAndWarehouseNosWithLastRecord(reqList);

        //获取商城价
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice (new ArrayList<> (skus),new ArrayList<> (areanos), null);
        Map<String, List<AreaSkuEntity>> skuAreaSkuMap = areaSkuEntities.stream ().filter (areaSkuEntity -> areaSkuEntity.getPrice () != null).collect(Collectors.groupingBy(AreaSkuEntity::getSku));

        majorPriceEntities.forEach (e-> {
            if(!CollectionUtil.isEmpty (majorPriceEntitiesOther)) {
                List<MajorPriceEntity> sameList = majorPriceEntitiesOther.stream ().filter (i -> i.getSku ().equals (e.getSku ()) && i.getAreaNo ().equals (e.getAreaNo ())).collect (Collectors.toList ());
                if (!CollectionUtil.isEmpty (sameList)) {
                    //比较其他的报价单 时间是否与修改的这个本报价单有交集， 如果有则删除另一个，保存此报价单
                    List<MajorPriceEntity> isOverlap = sameList.stream ().filter (i -> isOverlap (i.getValidTime (), i.getInvalidTime (), validTime, invalidTime)).collect (Collectors.toList ());
                    if (!CollectionUtil.isEmpty (isOverlap)) {
                        removeIds.addAll (isOverlap.stream ().map (MajorPriceEntity::getId).collect (Collectors.toList ()));
                    }
                    //查询是否有未生效的报价单，如果修改后的时间是也是未生效，把另一个未生效的报价单删除
                    List<MajorPriceEntity> dbs = sameList.stream ().filter (i -> (i.getValidTime ().isAfter (LocalDateTime.now ()) && validTime.isAfter (LocalDateTime.now ())) || (e.getStatus ().equals (MajorPriceStatusEnum.SAVE.getType ())  && i.getStatus ().equals (MajorPriceStatusEnum.SAVE.getType ()))).collect (Collectors.toList ());
                    if (!CollectionUtil.isEmpty (dbs)) {
                        removeIds.addAll (dbs.stream ().map (MajorPriceEntity::getId).collect (Collectors.toList ()));
                    }
                }
            }
            addUpdate (input, validTime, invalidTime, updateList, logList, areaSkuWarehouseMap, costQueryRespMap, skuAreaSkuMap, e);
        });
        if(CollectionUtil.isNotEmpty (logList)){
            majorPriceLogCommandDomainService.insertBatch (logList);
        }
        if(CollectionUtil.isNotEmpty (updateList)) {
            majorPriceCommandDomainService.updateBatch (updateList);
        }
        if(CollectionUtil.isNotEmpty (removeIds)){
            majorPriceCommandDomainService.removeByIds (removeIds);
        }
    }




    private void addUpdate(MajorPriceUpdateBatchInput input, LocalDateTime validTime, LocalDateTime invalidTime, List<MajorPriceCommandParam> updateList, List<MajorPriceLogCommandParam> logList, Map<String, String> areaSkuWarehouseMap, Map<String, ProductCostQueryResp> costQueryRespMap, Map<String, List<AreaSkuEntity>> skuAreaSkuMap, MajorPriceEntity e) {
        MajorPriceCommandParam param = MajorPriceConvert.INSTANCE.entity2Param (e);
        param.setValidTime (validTime);
        param.setInvalidTime (invalidTime);
        param.setPriceType (input.getPriceType ());
        param.setPrice (input.getPrice ());
        param.setPriceAdjustmentValue (input.getPriceAdjustmentValue ());
        param.setFixedPrice (input.getFixedPrice ());
        param.setInterestRate (input.getInterestRate ());
        String warehouseNoAndSku = areaSkuWarehouseMap.get(param.getAreaNo() + param.getSku());
        fillPriceInfo (param, costQueryRespMap.get(warehouseNoAndSku));
        updateList.add (param);
        Optional<AreaSkuEntity> first = skuAreaSkuMap.getOrDefault (e.getSku (), new ArrayList<> ()).stream ().filter (i -> i.getAreaNo ().equals (e.getAreaNo ())).findFirst ();
        if(first.isPresent ()) {
            addLogEntity (param, logList, costQueryRespMap.get (warehouseNoAndSku), first.get ());
        }else{
            addLogEntity (param, logList, costQueryRespMap.get (warehouseNoAndSku),null);
        }
    }

    private void addLogEntity(MajorPriceCommandParam param,List<MajorPriceLogCommandParam> logList,ProductCostQueryResp productCostQueryResp,AreaSkuEntity areaSkuEntity) {
        BigDecimal price = null;
        if(areaSkuEntity != null && areaSkuEntity.getPrice ()!=null &&  param.getPriceType () !=null) {
            price = PriceCalculator.calculateMajorPriceByType (param.getPrice (), areaSkuEntity.getPrice (), param.getPriceAdjustmentValue (), param.getPriceType ());
            if(price.compareTo (BigDecimal.ZERO)<=0){
                throw new BizException ("售价不可小于或等于0");
            }
        }
        if(param.getStatus ().equals (MajorPriceStatusEnum.COMMIT.getType ())) {
            MajorPriceLogCommandParam logEntity = MajorPriceLogConvert.INSTANCE.buildMajorPriceLogCommandParam (param);
            if (!Objects.equals(param.getPriceType (), MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode ())) {
                if (productCostQueryResp != null && price != null) {
                    logEntity.setInterestRate (price.subtract (productCostQueryResp.getCurrentCost ()).divide (price, 2, RoundingMode.HALF_UP));
                }
            }else{
                logEntity.setInterestRate (param.getInterestRate ());
            }
            logEntity.setPrice (price);
            logList.add (logEntity);
        }
    }

    private void fillMajorPriceParam(MajorPriceCommandParam param,ProductCostQueryResp productCostQueryResp,String weight,String pdName) {
        param.setWeight (weight);
        param.setPdName (pdName);
        fillPriceInfo (param,productCostQueryResp);
    }

    public void fillPriceInfo(MajorPriceCommandParam param, ProductCostQueryResp productCostQueryResp) {
        Integer priceType = param.getPriceType ();
        String sku = param.getSku ();
        String areaName = param.getAreaName ();
        if (Objects.equals(priceType, MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode ())) {
            if (Objects.isNull(productCostQueryResp)) {
                throw new BizException ("sku:" + sku + "在"+areaName+"关联的库存仓没有日周期/库存成本,无法计算毛利率,请更换价格形式或添加库存");
            }
            BigDecimal one = BigDecimal.ONE;
            BigDecimal interestRate = Optional.ofNullable(param.getInterestRate()).orElse(BigDecimal.ZERO);
            BigDecimal fixedPrice = param.getFixedPrice();
            if (Objects.isNull(fixedPrice)) {
                fixedPrice = BigDecimal.ZERO;
                param.setFixedPrice(fixedPrice);
            }
            BigDecimal price = productCostQueryResp.getCurrentCost ().divide(one.subtract(interestRate.divide(new BigDecimal(100),4,RoundingMode.HALF_DOWN)), 2, RoundingMode.HALF_UP).add(fixedPrice);
            param.setPrice(price);
            param.setOriginalPrice(price);
            param.setCost(productCostQueryResp.getCurrentCost ());
            param.setInterestRate(interestRate.setScale(2, RoundingMode.DOWN));
            param.setPriceAdjustmentValue (null);
        } else if (Objects.equals(priceType, MajorPriceTypeEnum.CONTRACT_PRICE_SPECIFIED.getCode ())) {
            param.setInterestRate(null);
            param.setFixedPrice(null);
            param.setOriginalPrice(param.getPrice());
            param.setCost(null);
            param.setPriceAdjustmentValue (null);
        } else {
            if (Objects.equals (priceType, MajorPriceTypeEnum.MALL_PRICE.getCode ())){
                param.setPriceAdjustmentValue (null);
            }
            param.setPrice(null);
            param.setCost(null);
            param.setInterestRate(null);
            param.setFixedPrice(null);
            param.setOriginalPrice(null);
        }
    }

    /**
     * 设置当前报价单价格
     *
     * @param majorPrice
     */
    private void setCurrentMajorPrice(MajorPriceLowRemainder majorPrice) {
        //取毛利率成本
        log.info("开始计算当前报价：sku:{}, areaNo:{}", majorPrice.getSku(), majorPrice.getAreaNo());
        Integer warehouseNo = fenceQueryFacade.getWarehouseNo(majorPrice.getSku(), majorPrice.getAreaNo());
        if(Objects.isNull(warehouseNo)) {
            log.warn("sku:{}在城市：{}找不到对应的库存仓!", majorPrice.getSku(), majorPrice.getAreaNo());
            return;
        }

        BigDecimal newCost = null;
        // 查询周期成本
        BigDecimal cycleInventoryCost = productCostQueryFacade.selectCycleCost(majorPrice.getSku(), warehouseNo);
        if (Objects.nonNull(cycleInventoryCost) && cycleInventoryCost.compareTo(BigDecimal.ZERO) > 0) {
            newCost = cycleInventoryCost;
        }
        if (Objects.isNull(newCost)) {
            log.info("日周期成本不存在，取最新批次成本。majorPrice：{}", JSON.toJSONString(majorPrice));
            // 查询库存最新批次成本
            CostChangeVo costChangeVo = areaStoreMapper.selectLastBatchCostPriceBySkuAndAreaNo(warehouseNo, majorPrice.getSku());
            if (Objects.nonNull(costChangeVo) && costChangeVo.getCostPrice().compareTo(BigDecimal.ZERO) > 0) {
                newCost = costChangeVo.getCostPrice();
            }
        }
        log.warn("实时报价单价格。在仓库 {} 下的sku {} 成本价为 {}", warehouseNo, majorPrice.getSku(), newCost);
        //判断采购价是否为0
        if (newCost == null || BigDecimal.ZERO.equals(newCost) || newCost.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("实时报价单价格计算停止，在仓库 {} 下的sku {} 采购价为 {}", warehouseNo, majorPrice.getSku(), newCost);
            return;
        }
        BigDecimal interestRate = majorPrice.getInterestRate().divide(new BigDecimal(100), 4, RoundingMode.HALF_DOWN);
        BigDecimal newPrice = (newCost.divide(BigDecimal.ONE.subtract(interestRate), 2, RoundingMode.HALF_UP)).add(majorPrice.getFixedPrice());
        majorPrice.setCurrentMajorPrice(newPrice);
    }
}