package net.summerfarm.manage.application.service.admin;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminVO;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminWithDataPermissionVO;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminQueryParam;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminQueryInput;

/**
 *
 * @date 2024-06-18 13:21:08
 * @version 1.0
 *
 */
public interface AdminQueryService {

    /**
     * @description: 新增
     * @return AdminEntity
     **/
    PageInfo<AdminEntity> getPage(AdminQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    AdminVO getDetail(Long adminId);

    AdminWithDataPermissionVO getCurrentUserDetail();

    AdminWithDataPermissionVO getCurrentUserDetail(Long adminId);
}