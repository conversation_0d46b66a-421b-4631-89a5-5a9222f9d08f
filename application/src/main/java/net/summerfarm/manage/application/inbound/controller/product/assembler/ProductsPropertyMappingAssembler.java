package net.summerfarm.manage.application.inbound.controller.product.assembler;

import net.summerfarm.manage.application.inbound.controller.product.input.ProductsPropertyMappingQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductsPropertyMappingVO;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingRelationInfo;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyMappingRelationQueryParam;

import java.util.Objects;

/**
 * @Description
 * @Date 2025/3/31 18:27
 * @<AUTHOR>
 */
public class ProductsPropertyMappingAssembler {

    public static ProductsPropertyMappingVO assemble(ProductsPropertyMappingRelationInfo mappingRelationInfo) {
        if (Objects.isNull(mappingRelationInfo)) {
            return null;
        }
        return ProductsPropertyMappingVO.builder()
                .id(mappingRelationInfo.getId())
                .productsPropertyId(mappingRelationInfo.getProductsPropertyId())
                .productsPropertyName(mappingRelationInfo.getProductsPropertyName())
                .mappingId(mappingRelationInfo.getMappingId())
                .type(mappingRelationInfo.getType()).build();
    }

    public static ProductsPropertyMappingRelationQueryParam assembleQueryParam(ProductsPropertyMappingQueryInput input) {
        if (Objects.isNull(input)) {
            return null;
        }
        return ProductsPropertyMappingRelationQueryParam.builder()
                .propertyType(input.getPropertyType())
                .mappingType(input.getMappingType())
                .mappingIdList(input.getMappingIdList())
                .build();

    }

}
