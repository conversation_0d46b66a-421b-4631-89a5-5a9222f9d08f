package net.summerfarm.manage.application.inbound.controller.price;

import net.summerfarm.manage.application.inbound.controller.price.assembler.DocAssembler;
import net.summerfarm.manage.application.inbound.controller.price.input.DocQueryInput;
import net.summerfarm.manage.application.inbound.controller.price.vo.DocInfoVO;
import net.summerfarm.manage.common.enums.price.StockWarningLabelEnum;
import net.summerfarm.manage.facade.scp.DocQueryFacade;
import net.summerfarm.manage.facade.scp.dto.DocInfoDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value="/price")
public class PriceQueryController {


    @Resource
    private DocQueryFacade docQueryFacade;

    @PostMapping("/query/doc")
    public CommonResult<DocInfoVO> queryDocInfo(@RequestBody @Validated DocQueryInput docQueryInput) {
        DocInfoDTO docInfoDTO = docQueryFacade.queryDocInfo(docQueryInput.getSku(), docQueryInput.getWarehouseNo());
        // 处理label展示
        StockWarningLabelEnum stockWarningLabelEnum = StockWarningLabelEnum.dealLabel(docInfoDTO.getStockLevelMaximumDay(),
                docInfoDTO.getStockLevelMinimumDay(), docInfoDTO.getDoc(), docInfoDTO.getSalesQuantity());
        DocInfoVO docInfoVO = DocAssembler.convertVO(docInfoDTO, stockWarningLabelEnum);
        return CommonResult.ok(docInfoVO);
    }
}
