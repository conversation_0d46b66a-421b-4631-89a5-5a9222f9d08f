package net.summerfarm.manage.application.service.product.mall.impl;


import net.summerfarm.manage.application.inbound.controller.product.handler.ExternalProductHandler;
import net.summerfarm.manage.application.service.product.mall.AppPopTopMatchedCompetitorSkuListQueryService;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;
import net.summerfarm.manage.domain.product.repository.AppPopTopMatchedCompetitorSkuListQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopTopMatchedCompetitorSkuListQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopTopMatchedCompetitorSkuListAssembler;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.repository.ExternalProductMappingQueryRepository;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2024-11-18 15:55:40
* @version 1.0
*
*/
@Service
public class AppPopTopMatchedCompetitorSkuListQueryServiceImpl implements AppPopTopMatchedCompetitorSkuListQueryService {

    @Resource
    private AppPopTopMatchedCompetitorSkuListQueryRepository appPopTopMatchedCompetitorSkuListQueryRepository;
    @Resource
    private ExternalProductHandler externalProductHandler;
    @Resource
    private ExternalProductMappingQueryRepository externalProductMappingQueryRepository;

    @Override
    public PageInfo<AppPopTopMatchedCompetitorSkuListEntity> getPage(AppPopTopMatchedCompetitorSkuListQueryInput input) {
        AppPopTopMatchedCompetitorSkuListQueryParam queryParam = AppPopTopMatchedCompetitorSkuListAssembler.toAppPopTopMatchedCompetitorSkuListQueryParam(input);
        return appPopTopMatchedCompetitorSkuListQueryRepository.getPage(queryParam);
    }

    @Override
    public AppPopTopMatchedCompetitorSkuListEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return appPopTopMatchedCompetitorSkuListQueryRepository.selectById(id);
    }

    @Override
    public AppPopTopMatchedCompetitorSkuListEntity getOne(AppPopTopMatchedCompetitorSkuListQueryInput input) {
        AppPopTopMatchedCompetitorSkuListQueryParam queryParam = AppPopTopMatchedCompetitorSkuListAssembler.toAppPopTopMatchedCompetitorSkuListQueryParam(input);
        List<AppPopTopMatchedCompetitorSkuListEntity> competitorSkuListEntityList = appPopTopMatchedCompetitorSkuListQueryRepository.selectByCondition(queryParam);
        AppPopTopMatchedCompetitorSkuListEntity competitorSkuListEntity = CollectionUtils.isNotEmpty(competitorSkuListEntityList) ? competitorSkuListEntityList.get(0) : null;
        if (Objects.isNull(competitorSkuListEntity)) {
            return null;
        }
        competitorSkuListEntity.getSku();
        ExternalProductMappingQueryParam mappingQueryParam = new ExternalProductMappingQueryParam();
        mappingQueryParam.setInternalValue(competitorSkuListEntity.getSku());
        List<ExternalProductMappingEntity> externalProductMappingEntityList = externalProductMappingQueryRepository.selectByCondition(mappingQueryParam);
        competitorSkuListEntity.setExternalSkuCode(CollectionUtils.isNotEmpty(externalProductMappingEntityList) ? externalProductMappingEntityList.get(0).getExternalSkuCode(): null);
        competitorSkuListEntity.setCompetitorSkuEntityList(externalProductHandler.parseTopMatchedCompetitorSku(competitorSkuListEntity));
        return competitorSkuListEntity;
    }
}