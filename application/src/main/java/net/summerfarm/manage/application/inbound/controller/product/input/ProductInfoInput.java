package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ProductInfoInput
 * @Description
 * <AUTHOR>
 * @Date 15:47 2024/5/8
 * @Version 1.0
 **/
@Data
public class ProductInfoInput implements Serializable {

    /**
     * pdId
     */
    @NotNull(message = "pdId.null", groups = {Add.class})
    private Long pdId;

    /**
     *标记位-过时的spu  1代表过时，商品被删除
     */
    private Integer outdated;

    /**
     * 上新审核状态：0、待审核 1、审核通过 2、审核失败
     */
    private Integer auditStatus;

    /**
     * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 是否放入样品池
     */
    private Integer samplePool;

    /**
     * sku
     */
    private String sku;

    /**
     * 城市编号
     */
    private Integer areaNo;
}
