package net.summerfarm.manage.application.inbound.controller.admin.assembler;


import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import net.summerfarm.manage.application.inbound.controller.admin.input.command.AdminCommandInput;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminQueryInput;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminDataPermissionVO;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminVO;
import net.summerfarm.manage.application.inbound.controller.admin.vo.AdminWithDataPermissionVO;
import net.summerfarm.manage.common.enums.AdminTypeEnum;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.param.command.AdminCommandParam;
import net.summerfarm.manage.domain.admin.param.query.AdminQueryParam;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.facade.wms.dto.WarehouseStorageDTO;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-18 13:21:08
 */
public class AdminAssembler {

    private AdminAssembler() {
        // 无需实现
    }

    public static List<AdminDataPermissionVO> fromEntity(List<AdminDataPermissionEntity> adminDataPermissionEntityList) {
        if (CollectionUtils.isEmpty(adminDataPermissionEntityList)) {
            return Collections.emptyList();
        }
        return adminDataPermissionEntityList.stream().map(entity -> {
            AdminDataPermissionVO permissionVO = new AdminDataPermissionVO();
            permissionVO.setPermissionName(entity.getPermissionName());
            permissionVO.setPermissionValue(entity.getPermissionValue());
            permissionVO.setType(permissionVO.getType());
            return permissionVO;
        }).collect(Collectors.toList());
    }

    public static List<AdminDataPermissionVO> fromAreaEntity(List<AreaSimpleEntity> areaSimpleEntityList) {
        if (CollectionUtils.isEmpty(areaSimpleEntityList)) {
            return Lists.newArrayList();
        }
        return areaSimpleEntityList.stream().map(entity -> {
            AdminDataPermissionVO permissionVO = new AdminDataPermissionVO();
            permissionVO.setPermissionName(entity.getAreaName());
            permissionVO.setPermissionValue(String.valueOf(entity.getAreaNo()));
            // hardcoded permission value for area。。。
            permissionVO.setType("1");
            return permissionVO;
        }).collect(Collectors.toList());
    }

    public static List<AdminDataPermissionVO> fromWarehouseDTO(List<WarehouseStorageDTO> warehouseStorageDTOList) {
        if (CollectionUtils.isEmpty(warehouseStorageDTOList)) {
            return Lists.newArrayList();
        }
        return warehouseStorageDTOList.stream().map(entity -> {
            AdminDataPermissionVO permissionVO = new AdminDataPermissionVO();
            permissionVO.setPermissionName(entity.getWarehouseName());
            permissionVO.setPermissionValue(String.valueOf(entity.getWarehouseNo()));
            // hardcoded permission value for area。。。
            permissionVO.setType("0");
            return permissionVO;
        }).collect(Collectors.toList());
    }


    // ------------------------------- request ----------------------------
    public static AdminQueryParam toAdminQueryParam(AdminQueryInput adminQueryInput) {
        if (adminQueryInput == null) {
            return null;
        }
        AdminQueryParam adminQueryParam = new AdminQueryParam();
        adminQueryParam.setAdminId(adminQueryInput.getAdminId());
        adminQueryParam.setCreateTime(adminQueryInput.getCreateTime());
        adminQueryParam.setLoginFailTimes(adminQueryInput.getLoginFailTimes());
        adminQueryParam.setIsDisabled(adminQueryInput.getIsDisabled());
        adminQueryParam.setUsername(adminQueryInput.getUsername());
        adminQueryParam.setPassword(adminQueryInput.getPassword());
        adminQueryParam.setLoginTime(adminQueryInput.getLoginTime());
        adminQueryParam.setRealname(adminQueryInput.getRealname());
        adminQueryParam.setGender(adminQueryInput.getGender());
        adminQueryParam.setDepartment(adminQueryInput.getDepartment());
        adminQueryParam.setPhone(adminQueryInput.getPhone());
        adminQueryParam.setKp(adminQueryInput.getKp());
        adminQueryParam.setSalerId(adminQueryInput.getSalerId());
        adminQueryParam.setSalerName(adminQueryInput.getSalerName());
        adminQueryParam.setContract(adminQueryInput.getContract());
        adminQueryParam.setContractMethod(adminQueryInput.getContractMethod());
        adminQueryParam.setNameRemakes(adminQueryInput.getNameRemakes());
        adminQueryParam.setOperateId(adminQueryInput.getOperateId());
        adminQueryParam.setMajorCycle(adminQueryInput.getMajorCycle());
        adminQueryParam.setCloseOrderType(adminQueryInput.getCloseOrderType());
        adminQueryParam.setCooperationStage(adminQueryInput.getCooperationStage());
        adminQueryParam.setUpdateTime(adminQueryInput.getUpdateTime());
        adminQueryParam.setCloseOrderTime(adminQueryInput.getCloseOrderTime());
        adminQueryParam.setUpdateCloseOrderTime(adminQueryInput.getUpdateCloseOrderTime());
        adminQueryParam.setLowPriceRemainder(adminQueryInput.getLowPriceRemainder());
        adminQueryParam.setNotIncludedArea(adminQueryInput.getNotIncludedArea());
        adminQueryParam.setSkuSorting(adminQueryInput.getSkuSorting());
        adminQueryParam.setAdminType(adminQueryInput.getAdminType());
        adminQueryParam.setAdminChain(adminQueryInput.getAdminChain());
        adminQueryParam.setAdminGrade(adminQueryInput.getAdminGrade());
        adminQueryParam.setAdminSwitch(adminQueryInput.getAdminSwitch());
        adminQueryParam.setCreditCode(adminQueryInput.getCreditCode());
        adminQueryParam.setBusinessLicenseAddress(adminQueryInput.getBusinessLicenseAddress());
        adminQueryParam.setBillToPay(adminQueryInput.getBillToPay());
        adminQueryParam.setBaseUserId(adminQueryInput.getBaseUserId());
        adminQueryParam.setPageIndex(adminQueryInput.getPageIndex());
        adminQueryParam.setPageSize(adminQueryInput.getPageSize());
        return adminQueryParam;
    }


    public static AdminCommandParam buildCreateParam(AdminCommandInput adminCommandInput) {
        if (adminCommandInput == null) {
            return null;
        }
        AdminCommandParam adminCommandParam = new AdminCommandParam();
        adminCommandParam.setAdminId(adminCommandInput.getAdminId());
        adminCommandParam.setCreateTime(adminCommandInput.getCreateTime());
        adminCommandParam.setLoginFailTimes(adminCommandInput.getLoginFailTimes());
        adminCommandParam.setIsDisabled(adminCommandInput.getIsDisabled());
        adminCommandParam.setUsername(adminCommandInput.getUsername());
        adminCommandParam.setPassword(adminCommandInput.getPassword());
        adminCommandParam.setLoginTime(adminCommandInput.getLoginTime());
        adminCommandParam.setRealname(adminCommandInput.getRealname());
        adminCommandParam.setGender(adminCommandInput.getGender());
        adminCommandParam.setDepartment(adminCommandInput.getDepartment());
        adminCommandParam.setPhone(adminCommandInput.getPhone());
        adminCommandParam.setKp(adminCommandInput.getKp());
        adminCommandParam.setSalerId(adminCommandInput.getSalerId());
        adminCommandParam.setSalerName(adminCommandInput.getSalerName());
        adminCommandParam.setContract(adminCommandInput.getContract());
        adminCommandParam.setContractMethod(adminCommandInput.getContractMethod());
        adminCommandParam.setNameRemakes(adminCommandInput.getNameRemakes());
        adminCommandParam.setOperateId(adminCommandInput.getOperateId());
        adminCommandParam.setMajorCycle(adminCommandInput.getMajorCycle());
        adminCommandParam.setCloseOrderType(adminCommandInput.getCloseOrderType());
        adminCommandParam.setCooperationStage(adminCommandInput.getCooperationStage());
        adminCommandParam.setUpdateTime(adminCommandInput.getUpdateTime());
        adminCommandParam.setCloseOrderTime(adminCommandInput.getCloseOrderTime());
        adminCommandParam.setUpdateCloseOrderTime(adminCommandInput.getUpdateCloseOrderTime());
        adminCommandParam.setLowPriceRemainder(adminCommandInput.getLowPriceRemainder());
        adminCommandParam.setNotIncludedArea(adminCommandInput.getNotIncludedArea());
        adminCommandParam.setSkuSorting(adminCommandInput.getSkuSorting());
        adminCommandParam.setAdminType(adminCommandInput.getAdminType());
        adminCommandParam.setAdminChain(adminCommandInput.getAdminChain());
        adminCommandParam.setAdminGrade(adminCommandInput.getAdminGrade());
        adminCommandParam.setAdminSwitch(adminCommandInput.getAdminSwitch());
        adminCommandParam.setCreditCode(adminCommandInput.getCreditCode());
        adminCommandParam.setBusinessLicenseAddress(adminCommandInput.getBusinessLicenseAddress());
        adminCommandParam.setBillToPay(adminCommandInput.getBillToPay());
        adminCommandParam.setBaseUserId(adminCommandInput.getBaseUserId());
        return adminCommandParam;
    }


    public static AdminCommandParam buildUpdateParam(AdminCommandInput adminCommandInput) {
        if (adminCommandInput == null) {
            return null;
        }
        AdminCommandParam adminCommandParam = new AdminCommandParam();
        adminCommandParam.setAdminId(adminCommandInput.getAdminId());
        adminCommandParam.setCreateTime(adminCommandInput.getCreateTime());
        adminCommandParam.setLoginFailTimes(adminCommandInput.getLoginFailTimes());
        adminCommandParam.setIsDisabled(adminCommandInput.getIsDisabled());
        adminCommandParam.setUsername(adminCommandInput.getUsername());
        adminCommandParam.setPassword(adminCommandInput.getPassword());
        adminCommandParam.setLoginTime(adminCommandInput.getLoginTime());
        adminCommandParam.setRealname(adminCommandInput.getRealname());
        adminCommandParam.setGender(adminCommandInput.getGender());
        adminCommandParam.setDepartment(adminCommandInput.getDepartment());
        adminCommandParam.setPhone(adminCommandInput.getPhone());
        adminCommandParam.setKp(adminCommandInput.getKp());
        adminCommandParam.setSalerId(adminCommandInput.getSalerId());
        adminCommandParam.setSalerName(adminCommandInput.getSalerName());
        adminCommandParam.setContract(adminCommandInput.getContract());
        adminCommandParam.setContractMethod(adminCommandInput.getContractMethod());
        adminCommandParam.setNameRemakes(adminCommandInput.getNameRemakes());
        adminCommandParam.setOperateId(adminCommandInput.getOperateId());
        adminCommandParam.setMajorCycle(adminCommandInput.getMajorCycle());
        adminCommandParam.setCloseOrderType(adminCommandInput.getCloseOrderType());
        adminCommandParam.setCooperationStage(adminCommandInput.getCooperationStage());
        adminCommandParam.setUpdateTime(adminCommandInput.getUpdateTime());
        adminCommandParam.setCloseOrderTime(adminCommandInput.getCloseOrderTime());
        adminCommandParam.setUpdateCloseOrderTime(adminCommandInput.getUpdateCloseOrderTime());
        adminCommandParam.setLowPriceRemainder(adminCommandInput.getLowPriceRemainder());
        adminCommandParam.setNotIncludedArea(adminCommandInput.getNotIncludedArea());
        adminCommandParam.setSkuSorting(adminCommandInput.getSkuSorting());
        adminCommandParam.setAdminType(adminCommandInput.getAdminType());
        adminCommandParam.setAdminChain(adminCommandInput.getAdminChain());
        adminCommandParam.setAdminGrade(adminCommandInput.getAdminGrade());
        adminCommandParam.setAdminSwitch(adminCommandInput.getAdminSwitch());
        adminCommandParam.setCreditCode(adminCommandInput.getCreditCode());
        adminCommandParam.setBusinessLicenseAddress(adminCommandInput.getBusinessLicenseAddress());
        adminCommandParam.setBillToPay(adminCommandInput.getBillToPay());
        adminCommandParam.setBaseUserId(adminCommandInput.getBaseUserId());
        return adminCommandParam;
    }

// ------------------------------- response ----------------------------

    public static List<AdminVO> toAdminVOList(List<AdminEntity> adminEntityList) {
        if (adminEntityList == null) {
            return Collections.emptyList();
        }
        List<AdminVO> adminVOList = new ArrayList<>(adminEntityList.size());
        for (AdminEntity adminEntity : adminEntityList) {
            adminVOList.add(toAdminVO(adminEntity));
        }
        return adminVOList;
    }


    public static AdminVO toAdminVO(AdminEntity adminEntity) {
        if (adminEntity == null) {
            return null;
        }
        AdminVO adminVO = new AdminVO();
        adminVO.setAdminId(adminEntity.getAdminId());
        adminVO.setCreateTime(adminEntity.getCreateTime());
        adminVO.setLoginFailTimes(adminEntity.getLoginFailTimes());
        adminVO.setDisabled(Optional.ofNullable(adminEntity.getIsDisabled()).orElse(0) == 1);
        adminVO.setUsername(adminEntity.getUsername());
        // password 不应该展示
        // adminVO.setPassword(adminEntity.getPassword());
        adminVO.setLoginTime(adminEntity.getLoginTime());
        adminVO.setRealname(adminEntity.getRealname());
        adminVO.setGender(adminEntity.getGender());
        adminVO.setDepartment(adminEntity.getDepartment());
        adminVO.setPhone(adminEntity.getPhone());
        adminVO.setKp(adminEntity.getKp());
        adminVO.setSalerId(adminEntity.getSalerId());
        adminVO.setSalerName(adminEntity.getSalerName());
        adminVO.setContract(adminEntity.getContract());
        adminVO.setContractMethod(adminEntity.getContractMethod());
        adminVO.setNameRemakes(adminEntity.getNameRemakes());
        adminVO.setOperateId(adminEntity.getOperateId());
        adminVO.setMajorCycle(adminEntity.getMajorCycle());
        adminVO.setCloseOrderType(adminEntity.getCloseOrderType());
        adminVO.setCooperationStage(adminEntity.getCooperationStage());
        adminVO.setUpdateTime(adminEntity.getUpdateTime());
        adminVO.setCloseOrderTime(adminEntity.getCloseOrderTime());
        adminVO.setUpdateCloseOrderTime(adminEntity.getUpdateCloseOrderTime());
        adminVO.setLowPriceRemainder(adminEntity.getLowPriceRemainder());
        adminVO.setNotIncludedArea(adminEntity.getNotIncludedArea());
        adminVO.setSkuSorting(adminEntity.getSkuSorting());
        adminVO.setAdminType(adminEntity.getAdminType());
        adminVO.setAdminChain(adminEntity.getAdminChain());
        adminVO.setAdminGrade(adminEntity.getAdminGrade());
        adminVO.setAdminSwitch(adminEntity.getAdminSwitch());
        adminVO.setCreditCode(adminEntity.getCreditCode());
        adminVO.setBusinessLicenseAddress(adminEntity.getBusinessLicenseAddress());
        adminVO.setBillToPay(adminEntity.getBillToPay());
        adminVO.setBaseUserId(adminEntity.getBaseUserId());
        return adminVO;
    }

    public static AdminWithDataPermissionVO toAdminVO(AdminVO adminVO) {
        if (adminVO == null) {
            return null;
        }
        AdminWithDataPermissionVO adminWithDataPermissionVO = new AdminWithDataPermissionVO();
        adminWithDataPermissionVO.setAdminId(adminVO.getAdminId());
        adminWithDataPermissionVO.setCreateTime(adminVO.getCreateTime());
        adminWithDataPermissionVO.setLoginFailTimes(adminVO.getLoginFailTimes());
        adminWithDataPermissionVO.setDisabled(adminVO.isDisabled());
        adminWithDataPermissionVO.setUsername(adminVO.getUsername());
        adminWithDataPermissionVO.setLoginTime(adminVO.getLoginTime());
        adminWithDataPermissionVO.setRealname(adminVO.getRealname());
        adminWithDataPermissionVO.setGender(adminVO.getGender());
        adminWithDataPermissionVO.setDepartment(adminVO.getDepartment());
        adminWithDataPermissionVO.setPhone(adminVO.getPhone());
        adminWithDataPermissionVO.setKp(adminVO.getKp());
        adminWithDataPermissionVO.setSalerId(adminVO.getSalerId());
        adminWithDataPermissionVO.setSalerName(adminVO.getSalerName());
        adminWithDataPermissionVO.setContract(adminVO.getContract());
        adminWithDataPermissionVO.setContractMethod(adminVO.getContractMethod());
        adminWithDataPermissionVO.setNameRemakes(adminVO.getNameRemakes());
        adminWithDataPermissionVO.setOperateId(adminVO.getOperateId());
        adminWithDataPermissionVO.setMajorCycle(adminVO.getMajorCycle());
        adminWithDataPermissionVO.setCloseOrderType(adminVO.getCloseOrderType());
        adminWithDataPermissionVO.setCooperationStage(adminVO.getCooperationStage());
        adminWithDataPermissionVO.setUpdateTime(adminVO.getUpdateTime());
        adminWithDataPermissionVO.setCloseOrderTime(adminVO.getCloseOrderTime());
        adminWithDataPermissionVO.setUpdateCloseOrderTime(adminVO.getUpdateCloseOrderTime());
        adminWithDataPermissionVO.setLowPriceRemainder(adminVO.getLowPriceRemainder());
        adminWithDataPermissionVO.setNotIncludedArea(adminVO.getNotIncludedArea());
        adminWithDataPermissionVO.setSkuSorting(adminVO.getSkuSorting());
        adminWithDataPermissionVO.setAdminType(adminVO.getAdminType());
        adminWithDataPermissionVO.setAdminChain(adminVO.getAdminChain());
        adminWithDataPermissionVO.setAdminGrade(adminVO.getAdminGrade());
        adminWithDataPermissionVO.setAdminSwitch(adminVO.getAdminSwitch());
        adminWithDataPermissionVO.setCreditCode(adminVO.getCreditCode());
        adminWithDataPermissionVO.setBusinessLicenseAddress(adminVO.getBusinessLicenseAddress());
        adminWithDataPermissionVO.setBillToPay(adminVO.getBillToPay());
        adminWithDataPermissionVO.setBaseUserId(adminVO.getBaseUserId());
        // 设置Type:
        adminWithDataPermissionVO.setType(Optional.ofNullable(AdminTypeEnum.getByType(adminVO.getAdminType())).orElse(AdminTypeEnum.XIANMU_EMPLOYEE).getType());
        return adminWithDataPermissionVO;
    }

}
