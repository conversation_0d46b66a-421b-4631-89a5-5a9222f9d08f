package net.summerfarm.manage.application.inbound.controller.product.assembler;


import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopBiaoguoProductsDfVO;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoProductsDfQueryInput;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-12 11:19:19
 * @version 1.0
 *
 */
public class AppPopBiaoguoProductsDfAssembler {

    private AppPopBiaoguoProductsDfAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static AppPopBiaoguoProductsDfQueryParam toAppPopBiaoguoProductsDfQueryParam(AppPopBiaoguoProductsDfQueryInput appPopBiaoguoProductsDfQueryInput) {
        if (appPopBiaoguoProductsDfQueryInput == null) {
            return null;
        }
        AppPopBiaoguoProductsDfQueryParam appPopBiaoguoProductsDfQueryParam = new AppPopBiaoguoProductsDfQueryParam();
        appPopBiaoguoProductsDfQueryParam.setId(appPopBiaoguoProductsDfQueryInput.getId());
        appPopBiaoguoProductsDfQueryParam.setCategoryName(appPopBiaoguoProductsDfQueryInput.getCategoryName());
        appPopBiaoguoProductsDfQueryParam.setCategory1(appPopBiaoguoProductsDfQueryInput.getCategory1());
        appPopBiaoguoProductsDfQueryParam.setCategory2(appPopBiaoguoProductsDfQueryInput.getCategory2());
        appPopBiaoguoProductsDfQueryParam.setCategory3(appPopBiaoguoProductsDfQueryInput.getCategory3());
        appPopBiaoguoProductsDfQueryParam.setBackCategoryName(appPopBiaoguoProductsDfQueryInput.getBackCategoryName());
        appPopBiaoguoProductsDfQueryParam.setCompetitor(appPopBiaoguoProductsDfQueryInput.getCompetitor());
        appPopBiaoguoProductsDfQueryParam.setSkuCode(appPopBiaoguoProductsDfQueryInput.getSkuCode());
        appPopBiaoguoProductsDfQueryParam.setGoodsName(appPopBiaoguoProductsDfQueryInput.getGoodsName());
        appPopBiaoguoProductsDfQueryParam.setBabyName(appPopBiaoguoProductsDfQueryInput.getBabyName());
        appPopBiaoguoProductsDfQueryParam.setStandardPrice(appPopBiaoguoProductsDfQueryInput.getStandardPrice());
        appPopBiaoguoProductsDfQueryParam.setFinalStandardPrice(appPopBiaoguoProductsDfQueryInput.getFinalStandardPrice());
        appPopBiaoguoProductsDfQueryParam.setLastTimeStandardPrice(appPopBiaoguoProductsDfQueryInput.getLastTimeStandardPrice());
        appPopBiaoguoProductsDfQueryParam.setFinalUnitPriceCatty(appPopBiaoguoProductsDfQueryInput.getFinalUnitPriceCatty());
        appPopBiaoguoProductsDfQueryParam.setUnitPriceCatty(appPopBiaoguoProductsDfQueryInput.getUnitPriceCatty());
        appPopBiaoguoProductsDfQueryParam.setGoodsType(appPopBiaoguoProductsDfQueryInput.getGoodsType());
        appPopBiaoguoProductsDfQueryParam.setSpecification(appPopBiaoguoProductsDfQueryInput.getSpecification());
        appPopBiaoguoProductsDfQueryParam.setUnit(appPopBiaoguoProductsDfQueryInput.getUnit());
        appPopBiaoguoProductsDfQueryParam.setGrossWeight(appPopBiaoguoProductsDfQueryInput.getGrossWeight());
        appPopBiaoguoProductsDfQueryParam.setNetWeight(appPopBiaoguoProductsDfQueryInput.getNetWeight());
        appPopBiaoguoProductsDfQueryParam.setMonthSale(appPopBiaoguoProductsDfQueryInput.getMonthSale());
        appPopBiaoguoProductsDfQueryParam.setGoodsSiphonCommissionRate(appPopBiaoguoProductsDfQueryInput.getGoodsSiphonCommissionRate());
        appPopBiaoguoProductsDfQueryParam.setSellerSiphonCommissionRate(appPopBiaoguoProductsDfQueryInput.getSellerSiphonCommissionRate());
        appPopBiaoguoProductsDfQueryParam.setSellerName(appPopBiaoguoProductsDfQueryInput.getSellerName());
        appPopBiaoguoProductsDfQueryParam.setGoodsPropDetailList(appPopBiaoguoProductsDfQueryInput.getGoodsPropDetailList());
        appPopBiaoguoProductsDfQueryParam.setUrl(appPopBiaoguoProductsDfQueryInput.getUrl());
        appPopBiaoguoProductsDfQueryParam.setSevenDayAfterSale(appPopBiaoguoProductsDfQueryInput.getSevenDayAfterSale());
        appPopBiaoguoProductsDfQueryParam.setSpiderFetchTime(appPopBiaoguoProductsDfQueryInput.getSpiderFetchTime());
        appPopBiaoguoProductsDfQueryParam.setCreateTime(appPopBiaoguoProductsDfQueryInput.getCreateTime());
        appPopBiaoguoProductsDfQueryParam.setDs(appPopBiaoguoProductsDfQueryInput.getDs());
        appPopBiaoguoProductsDfQueryParam.setPageIndex(appPopBiaoguoProductsDfQueryInput.getPageIndex());
        appPopBiaoguoProductsDfQueryParam.setPageSize(appPopBiaoguoProductsDfQueryInput.getPageSize());
        return appPopBiaoguoProductsDfQueryParam;
    }






// ------------------------------- response ----------------------------

    public static List<AppPopBiaoguoProductsDfVO> toAppPopBiaoguoProductsDfVOList(List<AppPopBiaoguoProductsDfEntity> appPopBiaoguoProductsDfEntityList) {
        if (appPopBiaoguoProductsDfEntityList == null) {
            return Collections.emptyList();
        }
        List<AppPopBiaoguoProductsDfVO> appPopBiaoguoProductsDfVOList = new ArrayList<>();
        for (AppPopBiaoguoProductsDfEntity appPopBiaoguoProductsDfEntity : appPopBiaoguoProductsDfEntityList) {
            appPopBiaoguoProductsDfVOList.add(toAppPopBiaoguoProductsDfVO(appPopBiaoguoProductsDfEntity));
        }
        return appPopBiaoguoProductsDfVOList;
}


   public static AppPopBiaoguoProductsDfVO toAppPopBiaoguoProductsDfVO(AppPopBiaoguoProductsDfEntity appPopBiaoguoProductsDfEntity) {
       if (appPopBiaoguoProductsDfEntity == null) {
            return null;
       }
       AppPopBiaoguoProductsDfVO appPopBiaoguoProductsDfVO = new AppPopBiaoguoProductsDfVO();
       appPopBiaoguoProductsDfVO.setId(appPopBiaoguoProductsDfEntity.getId());
       appPopBiaoguoProductsDfVO.setCategoryName(appPopBiaoguoProductsDfEntity.getCategoryName());
       appPopBiaoguoProductsDfVO.setCategory1(appPopBiaoguoProductsDfEntity.getCategory1());
       appPopBiaoguoProductsDfVO.setCategory2(appPopBiaoguoProductsDfEntity.getCategory2());
       appPopBiaoguoProductsDfVO.setCategory3(appPopBiaoguoProductsDfEntity.getCategory3());
       appPopBiaoguoProductsDfVO.setBackCategoryName(appPopBiaoguoProductsDfEntity.getBackCategoryName());
       appPopBiaoguoProductsDfVO.setCompetitor(appPopBiaoguoProductsDfEntity.getCompetitor());
       appPopBiaoguoProductsDfVO.setSkuCode(appPopBiaoguoProductsDfEntity.getSkuCode());
       appPopBiaoguoProductsDfVO.setGoodsName(appPopBiaoguoProductsDfEntity.getGoodsName());
       appPopBiaoguoProductsDfVO.setBabyName(appPopBiaoguoProductsDfEntity.getBabyName());
       appPopBiaoguoProductsDfVO.setStandardPrice(appPopBiaoguoProductsDfEntity.getStandardPrice());
       appPopBiaoguoProductsDfVO.setFinalStandardPrice(appPopBiaoguoProductsDfEntity.getFinalStandardPrice());
       appPopBiaoguoProductsDfVO.setLastTimeStandardPrice(appPopBiaoguoProductsDfEntity.getLastTimeStandardPrice());
       appPopBiaoguoProductsDfVO.setFinalUnitPriceCatty(appPopBiaoguoProductsDfEntity.getFinalUnitPriceCatty());
       appPopBiaoguoProductsDfVO.setUnitPriceCatty(appPopBiaoguoProductsDfEntity.getUnitPriceCatty());
       appPopBiaoguoProductsDfVO.setGoodsType(appPopBiaoguoProductsDfEntity.getGoodsType());
       appPopBiaoguoProductsDfVO.setSpecification(appPopBiaoguoProductsDfEntity.getSpecification());
       appPopBiaoguoProductsDfVO.setUnit(appPopBiaoguoProductsDfEntity.getUnit());
       appPopBiaoguoProductsDfVO.setGrossWeight(appPopBiaoguoProductsDfEntity.getGrossWeight());
       appPopBiaoguoProductsDfVO.setNetWeight(appPopBiaoguoProductsDfEntity.getNetWeight());
       appPopBiaoguoProductsDfVO.setMonthSale(appPopBiaoguoProductsDfEntity.getMonthSale());
       appPopBiaoguoProductsDfVO.setGoodsSiphonCommissionRate(appPopBiaoguoProductsDfEntity.getGoodsSiphonCommissionRate());
       appPopBiaoguoProductsDfVO.setSellerSiphonCommissionRate(appPopBiaoguoProductsDfEntity.getSellerSiphonCommissionRate());
       appPopBiaoguoProductsDfVO.setSellerName(appPopBiaoguoProductsDfEntity.getSellerName());
       appPopBiaoguoProductsDfVO.setGoodsPropDetailList(appPopBiaoguoProductsDfEntity.getGoodsPropDetailList());
       appPopBiaoguoProductsDfVO.setUrl(appPopBiaoguoProductsDfEntity.getUrl());
       appPopBiaoguoProductsDfVO.setSevenDayAfterSale(appPopBiaoguoProductsDfEntity.getSevenDayAfterSale());
       appPopBiaoguoProductsDfVO.setSpiderFetchTime(appPopBiaoguoProductsDfEntity.getSpiderFetchTime());
       appPopBiaoguoProductsDfVO.setCreateTime(appPopBiaoguoProductsDfEntity.getCreateTime());
       appPopBiaoguoProductsDfVO.setDs(appPopBiaoguoProductsDfEntity.getDs());
       return appPopBiaoguoProductsDfVO;
   }

}
