package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 茶百道门店批量导入VO
 * @Date: 2021/1/8 11:37
 * @Author: <EMAIL>
 */

@Data
public class MerchantBatchVO implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;

    /** 已存在门店id */
    private Long exId;

    /** 店铺名称 */
    private String mname;

    /** 门店编码（安佳) merchant_outer表内的 outer_no */
    private String outerNo;

    /** 联系人 （merchant表内的主联系人， merchant_sub_account 表内的 contact */
    private String mContact;

    private String phone;

    private String leaderMerchant;

    private String province;

    private String city;

    private String area;

    private String address;

    private String failRemark;

    private String type;

    private String adminId;

    private String enterpriseScale;

    /**
     * 外部平台id
     */
    private Integer outerPlatformId;
    /**
     * 外部平台名称
     */
    private String outerPlatformName;

    //发票抬头
    private Integer invoiceId;

    public MerchantBatchVO() {

    }

    public MerchantBatchVO(String outerPlatformName, String mname, String outerNo, String mContact, String phone, String leaderMerchant, String province, String city, String area, String address) {
        this.outerPlatformName = outerPlatformName;
        this.mname = mname;
        this.outerNo = outerNo;
        this.mContact = mContact;
        this.phone = phone;
        this.leaderMerchant = leaderMerchant;
        this.province = province;
        this.city = city;
        this.area = area;
        this.address = address;
    }


}


