package net.summerfarm.manage.application.inbound.controller.merchant.assembler;


import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelPageQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantCancelVO;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelQueryInput;
import net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCancelCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-12-27 14:45:17
 * @version 1.0
 *
 */
public class MerchantCancelAssembler {

    private MerchantCancelAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static MerchantCancelQueryParam toMerchantCancelQueryParam(MerchantCancelQueryInput merchantCancelQueryInput) {
        if (merchantCancelQueryInput == null) {
            return null;
        }
        MerchantCancelQueryParam merchantCancelQueryParam = new MerchantCancelQueryParam();
        merchantCancelQueryParam.setId(merchantCancelQueryInput.getId());
        merchantCancelQueryParam.setMId(merchantCancelQueryInput.getMId());
        merchantCancelQueryParam.setStatus(merchantCancelQueryInput.getStatus());
        merchantCancelQueryParam.setRemake(merchantCancelQueryInput.getRemake());
        merchantCancelQueryParam.setCertificate(merchantCancelQueryInput.getCertificate());
        merchantCancelQueryParam.setPhone(merchantCancelQueryInput.getPhone());
        return merchantCancelQueryParam;
    }


    public static MerchantCancelQueryParam toMerchantCancelQueryParam(MerchantCancelPageQueryInput merchantCancelQueryInput) {
        if (merchantCancelQueryInput == null) {
            return null;
        }
        MerchantCancelQueryParam merchantCancelQueryParam = new MerchantCancelQueryParam();
        merchantCancelQueryParam.setStatus(merchantCancelQueryInput.getStatus());
        merchantCancelQueryParam.setPhone(merchantCancelQueryInput.getPhone());
        merchantCancelQueryParam.setMname(merchantCancelQueryInput.getMerchantName());
        merchantCancelQueryParam.setAreaNo(merchantCancelQueryInput.getAreaNo());
        merchantCancelQueryParam.setStartTime(merchantCancelQueryInput.getStartTime());
        merchantCancelQueryParam.setEndTime(merchantCancelQueryInput.getEndTime());
        merchantCancelQueryParam.setPageIndex(merchantCancelQueryInput.getPageIndex());
        merchantCancelQueryParam.setPageSize(merchantCancelQueryInput.getPageSize());
        return merchantCancelQueryParam;
    }









// ------------------------------- response ----------------------------

    public static List<MerchantCancelVO> toMerchantCancelVOList(List<MerchantCancelEntity> merchantCancelEntityList) {
        if (merchantCancelEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantCancelVO> merchantCancelVOList = new ArrayList<>();
        for (MerchantCancelEntity merchantCancelEntity : merchantCancelEntityList) {
            merchantCancelVOList.add(toMerchantCancelVO(merchantCancelEntity));
        }
        return merchantCancelVOList;
}


   public static MerchantCancelVO toMerchantCancelVO(MerchantCancelEntity merchantCancelEntity) {
       if (merchantCancelEntity == null) {
            return null;
       }
       MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
       merchantCancelVO.setId(merchantCancelEntity.getId());
       merchantCancelVO.setMId(merchantCancelEntity.getMId());
       merchantCancelVO.setStatus(merchantCancelEntity.getStatus());
       merchantCancelVO.setRemake(merchantCancelEntity.getRemake());
       merchantCancelVO.setCertificate(merchantCancelEntity.getCertificate());
       merchantCancelVO.setCreateTime(merchantCancelEntity.getCreateTime());
       merchantCancelVO.setUpdateTime(merchantCancelEntity.getUpdateTime());
       merchantCancelVO.setResource(merchantCancelEntity.getResource());
       merchantCancelVO.setPhone(merchantCancelEntity.getPhone());
       merchantCancelVO.setMerchantName(merchantCancelEntity.getMname());
       merchantCancelVO.setAreaNo(merchantCancelEntity.getAreaNo() == null ? null : merchantCancelEntity.getAreaNo().toString());
       return merchantCancelVO;
   }

}
