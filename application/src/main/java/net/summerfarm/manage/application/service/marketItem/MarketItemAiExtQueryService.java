package net.summerfarm.manage.application.service.marketItem;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.query.MarketItemAiExtQueryInput;

/**
 *
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
public interface MarketItemAiExtQueryService {

    /**
     * @description: 新增
     * @return MarketItemAiExtEntity
     **/
    PageInfo<MarketItemAiExtEntity> getPage(MarketItemAiExtQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    MarketItemAiExtEntity getDetail(Long id);

}