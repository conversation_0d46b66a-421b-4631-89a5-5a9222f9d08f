package net.summerfarm.manage.application.service.account.impl;

import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantSubAccountVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.service.account.MerchantAccountService;
import net.summerfarm.manage.application.service.account.converter.MerchantSubAccountConverter;
import net.summerfarm.manage.application.service.merchant.MerchantBaseService;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.facade.merchant.MerchantAccountFacade;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class MerchantAccountServiceImpl extends MerchantBaseService implements MerchantAccountService {
    @Resource
    private MerchantAccountFacade merchantAccountFacade;


    @Override
    public List<MerchantSubAccountVO> getByStoreId(Long storeId) {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setStoreId(storeId);
        List<MerchantStoreAccountResultResp> merchantStoreAccountResultResps
                = merchantAccountFacade.queryMerchantAccount(req);
        return MerchantSubAccountConverter.toMerchantSubAccountVOList(merchantStoreAccountResultResps);
    }

    @Override
    public List<MerchantSubAccountVO> getByMId(Long mId) {
        MerchantQueryInput input = new MerchantQueryInput();
        input.setMId(mId);
        List<MerchantVO> merchantStoreAndExtends = getMerchantStoreAndExtends(input);
        if (CollectionUtils.isEmpty(merchantStoreAndExtends)){
             new BizException("门店不存在");
        }
        return getByStoreId(merchantStoreAndExtends.get(0).getStoreId());
    }


}
