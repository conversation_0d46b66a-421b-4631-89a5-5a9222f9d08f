package net.summerfarm.manage.application.inbound.provider.areasku;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.areasku.AreaSkuQueryProvider;
import net.summerfarm.client.req.areasku.AreaSkuQueryReq;
import net.summerfarm.client.resp.areasku.AreaSkuResp;
import net.summerfarm.manage.application.inbound.controller.product.input.AreaSkuQueryInput;
import net.summerfarm.manage.application.inbound.provider.areasku.converter.AreaSkuConverter;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@DubboService
@Component
public class AreaSkuQueryProviderImpl implements AreaSkuQueryProvider {
    @Autowired
    private AreaSkuQueryService areaSkuQueryService;

    @Override
    public DubboResponse<List<AreaSkuResp>> queryAreaSkuBySkuList(List<String> skus, Boolean onsale) {
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryService.queryAreaSkuBySkuList (skus, onsale);
        return DubboResponse.getOK (AreaSkuConverter.INSTANCE.vo2AreaSkuRespList (areaSkuEntities));
    }

    @Override
    public DubboResponse<List<AreaSkuResp>> queryAreaSkuBySkuAndAreaNoList(List<AreaSkuQueryReq> list) {
        if (CollectionUtils.isEmpty(list)) {
            return DubboResponse.getOK (Collections.emptyList());
        }

        List<AreaSkuQueryInput> input = new ArrayList<>();
        for (AreaSkuQueryReq areaSkuQueryReq : list) {
            AreaSkuQueryInput areaSkuQueryInput = new AreaSkuQueryInput();
            areaSkuQueryInput.setSku(areaSkuQueryReq.getSku());
            areaSkuQueryInput.setAreaNos(areaSkuQueryReq.getAreaNos());
            input.add(areaSkuQueryInput);
        }
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryService.queryAreaSkuBySkuAndAreaNoList(input);
        return DubboResponse.getOK (AreaSkuConverter.INSTANCE.vo2AreaSkuRespList (areaSkuEntities));
    }
}
