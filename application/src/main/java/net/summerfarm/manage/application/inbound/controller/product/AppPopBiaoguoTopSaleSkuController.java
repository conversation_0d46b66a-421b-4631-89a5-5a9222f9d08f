package net.summerfarm.manage.application.inbound.controller.product;

import com.aliyun.odps.utils.StringUtils;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopBiaoguoTopSaleSkuAssembler;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopBiaoguoCategoryVO;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopBiaoguoTopSaleSkuVO;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoTopSaleSkuQueryInput;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.util.ExceptionUtil;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import net.summerfarm.manage.application.service.product.mall.AppPopBiaoguoTopSaleSkuQueryService;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title app_pop_biaoguo_top_sale_sku table
 * @Description app_pop_biaoguo_top_sale_sku table功能模块
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 */
@RestController
@RequestMapping(value="/appPopBiaoguoTopSaleSku")
public class AppPopBiaoguoTopSaleSkuController{

	@Resource
	private AppPopBiaoguoTopSaleSkuQueryService appPopBiaoguoTopSaleSkuQueryService;


	/**
	 * 查询外部待引用品列表
	 *
	 * <AUTHOR>
	 * @date 2024/11/18 16:46
	 */
	@PostMapping(value="/query/list")
	public CommonResult<List<AppPopBiaoguoTopSaleSkuVO>> getList(@RequestBody AppPopBiaoguoTopSaleSkuQueryInput input){
		input.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
		List<AppPopBiaoguoTopSaleSkuEntity> biaoguoTopSaleSkuEntityList = appPopBiaoguoTopSaleSkuQueryService.getList(input);
		List<AppPopBiaoguoTopSaleSkuVO> result = biaoguoTopSaleSkuEntityList.stream().map(AppPopBiaoguoTopSaleSkuAssembler::toAppPopBiaoguoTopSaleSkuVO).collect(Collectors.toList());
		return CommonResult.ok(result);
	}

	/**
	 * 查询外部待引用品类目列表
	 *
	 * <AUTHOR>
	 * @date 2024/11/18 16:46
	 */
	@PostMapping(value="/category/query/list")
	public CommonResult<List<AppPopBiaoguoCategoryVO>> getCategoryList(@RequestBody AppPopBiaoguoTopSaleSkuQueryInput input){
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getBuyerId()), "买手信息不能为空");
		input.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
		List<AppPopBiaoguoCategoryEntity> categoryList = appPopBiaoguoTopSaleSkuQueryService.getCategoryList(input);
		List<AppPopBiaoguoCategoryVO> result = categoryList.stream().map(AppPopBiaoguoTopSaleSkuAssembler::convert).collect(Collectors.toList());
		return CommonResult.ok(result);
	}

	/**
	 * app_pop_biaoguo_top_sale_sku table列表
	 * @return AppPopBiaoguoTopSaleSkuVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<AppPopBiaoguoTopSaleSkuVO>> getPage(@RequestBody AppPopBiaoguoTopSaleSkuQueryInput input){
		PageInfo<AppPopBiaoguoTopSaleSkuEntity> page = appPopBiaoguoTopSaleSkuQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, AppPopBiaoguoTopSaleSkuAssembler::toAppPopBiaoguoTopSaleSkuVO));
	}

	/**
	* 推荐鲜沐商品列表
	* @return AppPopBiaoguoTopSaleSkuVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<AppPopBiaoguoTopSaleSkuVO> detail(@RequestBody AppPopBiaoguoTopSaleSkuQueryInput input){
		ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(input.getSkuCode()), "sku编码不能为空");
		input.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
		return CommonResult.ok(AppPopBiaoguoTopSaleSkuAssembler.toAppPopBiaoguoTopSaleSkuVO(appPopBiaoguoTopSaleSkuQueryService.getOne(input)));
	}


}

