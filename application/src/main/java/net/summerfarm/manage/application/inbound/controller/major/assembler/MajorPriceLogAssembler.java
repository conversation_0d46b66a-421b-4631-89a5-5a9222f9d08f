package net.summerfarm.manage.application.inbound.controller.major.assembler;


import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceLogQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLogVO;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-02-19 11:19:11
 */
public class MajorPriceLogAssembler {

    private MajorPriceLogAssembler() {
        // 无需实现
    }


    // ------------------------------- request ----------------------------
    public static MajorPriceLogQueryParam toMajorPriceLogQueryParam(MajorPriceLogQueryInput majorPriceLogQueryInput) {
        if (majorPriceLogQueryInput == null) {
            return null;
        }
        MajorPriceLogQueryParam majorPriceLogQueryParam = new MajorPriceLogQueryParam();
        majorPriceLogQueryParam.setSku(majorPriceLogQueryInput.getSku());
        majorPriceLogQueryParam.setAreaNo(majorPriceLogQueryInput.getAreaNo());
        majorPriceLogQueryParam.setDirect(majorPriceLogQueryInput.getDirect());
        majorPriceLogQueryParam.setAdminId(majorPriceLogQueryInput.getAdminId());
        majorPriceLogQueryParam.setPageIndex(majorPriceLogQueryInput.getPageIndex());
        majorPriceLogQueryParam.setPageSize(majorPriceLogQueryInput.getPageSize());
        return majorPriceLogQueryParam;
    }


// ------------------------------- response ----------------------------

    public static List<MajorPriceLogVO> toMajorPriceLogVOList(List<MajorPriceLogEntity> majorPriceLogEntityList) {
        if (majorPriceLogEntityList == null) {
            return Collections.emptyList();
        }
        List<MajorPriceLogVO> majorPriceLogVOList = new ArrayList<>();
        for (MajorPriceLogEntity majorPriceLogEntity : majorPriceLogEntityList) {
            majorPriceLogVOList.add(toMajorPriceLogVO(majorPriceLogEntity));
        }
        return majorPriceLogVOList;
    }


    public static MajorPriceLogVO toMajorPriceLogVO(MajorPriceLogEntity majorPriceLogEntity) {
        if (majorPriceLogEntity == null) {
            return null;
        }
        MajorPriceLogVO majorPriceLogVO = new MajorPriceLogVO();
        majorPriceLogVO.setId(majorPriceLogEntity.getId());
        majorPriceLogVO.setSku(majorPriceLogEntity.getSku());
        majorPriceLogVO.setPdName(majorPriceLogEntity.getPdName());
        majorPriceLogVO.setWeight(majorPriceLogEntity.getWeight());
        majorPriceLogVO.setAreaNo(majorPriceLogEntity.getAreaNo());
        majorPriceLogVO.setAdminId(majorPriceLogEntity.getAdminId());
        majorPriceLogVO.setPrice(majorPriceLogEntity.getPrice());
        majorPriceLogVO.setAreaName(majorPriceLogEntity.getAreaName());
        majorPriceLogVO.setDirect(majorPriceLogEntity.getDirect());
        majorPriceLogVO.setLargeAreaNo(majorPriceLogEntity.getLargeAreaNo());
        majorPriceLogVO.setPriceAdjustmentValue(majorPriceLogEntity.getPriceAdjustmentValue());
        majorPriceLogVO.setRemark(majorPriceLogEntity.getRemark());
        majorPriceLogVO.setCostPrice(majorPriceLogEntity.getCostPrice());
        majorPriceLogVO.setMallPrice(majorPriceLogEntity.getMallPrice());
        majorPriceLogVO.setPayMethod(majorPriceLogEntity.getPayMethod());
        majorPriceLogVO.setValidTime(majorPriceLogEntity.getValidTime());
        majorPriceLogVO.setInvalidTime(majorPriceLogEntity.getInvalidTime());
        majorPriceLogVO.setMallShow(majorPriceLogEntity.getMallShow());
        majorPriceLogVO.setPriceType(majorPriceLogEntity.getPriceType());
        majorPriceLogVO.setCost(majorPriceLogEntity.getCost());
        majorPriceLogVO.setInterestRate(majorPriceLogEntity.getInterestRate());
        majorPriceLogVO.setFixedPrice(majorPriceLogEntity.getFixedPrice());
        majorPriceLogVO.setOriginalPrice(majorPriceLogEntity.getOriginalPrice());
        majorPriceLogVO.setUpdateTime(majorPriceLogEntity.getUpdateTime());
        majorPriceLogVO.setLowPriceUpdateTime(majorPriceLogEntity.getLowPriceUpdateTime());
        return majorPriceLogVO;
    }

}
