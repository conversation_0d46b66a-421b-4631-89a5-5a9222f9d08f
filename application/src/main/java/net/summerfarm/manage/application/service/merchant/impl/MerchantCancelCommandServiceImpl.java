package net.summerfarm.manage.application.service.merchant.impl;


import cn.hutool.core.collection.CollUtil;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.mall.client.resp.MerchantCancelResp;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelInsertInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MerchantCancelUpdateInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantCancelVO;
import net.summerfarm.manage.application.service.merchant.MerchantCancelCommandService;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.common.enums.CommonStatus;
import net.summerfarm.manage.common.enums.MerchantCancelEnum;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantCancelQueryParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantCancelQueryRepository;
import net.summerfarm.manage.facade.merchant.MerchantCancelFacade;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2023-12-27 14:45:17
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class MerchantCancelCommandServiceImpl implements MerchantCancelCommandService {

    @Autowired
    private MerchantCancelQueryRepository merchantCancelQueryRepository;
    @Resource
    private MerchantCancelFacade merchantCancelFacade;

    @Override
    public MerchantCancelVO insert(MerchantCancelInsertInput input) {
        //校验是否已经注销
        MerchantCancelQueryParam queryParam = new MerchantCancelQueryParam();
        queryParam.setMId(input.getMId());
        queryParam.setStatus(MerchantCancelEnum.CANCELLED.getCode());
        List<MerchantCancelEntity> cancelEntities = merchantCancelQueryRepository.selectByCondition(queryParam);
        if (CollUtil.isNotEmpty(cancelEntities)) {
            throw new BizException("当前门店已注销，无需重复申请！");
        }
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setCertificate(input.getCertificate());
        merchantCancelInputReq.setRemake(input.getRemake());
        merchantCancelInputReq.setMId(input.getMId());
        merchantCancelInputReq.setResource(CommonStatus.YES.getCode());
        merchantCancelInputReq.setCreator(UserInfoHolder.getAdminId());
        merchantCancelInputReq.setMName(input.getMName());
        MerchantCancelResp merchantCancelResp = merchantCancelFacade.insert(merchantCancelInputReq);

        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setId(merchantCancelResp.getId());
        merchantCancelVO.setCause(merchantCancelResp.getCause());
        return merchantCancelVO;
    }

    @Override
    public Boolean updateStatus(MerchantCancelUpdateInput input) {
        if (Objects.isNull(input) || Objects.isNull(input.getId())) {
            throw new BizException("门店申请注销ID不能为空！");
        }
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setId(input.getId());
        merchantCancelInputReq.setStatus(input.getStatus());
        merchantCancelInputReq.setUpdater(UserInfoHolder.getAdminId());
        return merchantCancelFacade.updateStatus(merchantCancelInputReq);
    }

    @Override
    public MerchantCancelVO promptlyCancel(MerchantCancelUpdateInput input) {
        //假如不是待注销状态则不处理
        MerchantCancelEntity merchantCancel = merchantCancelQueryRepository.selectById(input.getId());
        if (Objects.isNull(merchantCancel) || !Objects.equals(merchantCancel.getStatus(), MerchantCancelEnum.TO_BE_CANCELLED.getCode())) {
            throw new BizException("门店注销记录为空或者不是待注销状态！");
        }
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();

        //校验是否可以注销
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setMId(merchantCancel.getMId());
        List<String> check = merchantCancelFacade.check(merchantCancelInputReq);
        if (!CollectionUtils.isEmpty(check)) {
            merchantCancelVO.setCause(check);
            return merchantCancelVO;
        }

        //立即注销
        merchantCancelInputReq.setId(merchantCancel.getId());
        Boolean aBoolean = merchantCancelFacade.promptlyCancel(merchantCancelInputReq);
        if (!aBoolean) {
            throw new BizException("注销失败！");
        }
        return merchantCancelVO;
    }
}