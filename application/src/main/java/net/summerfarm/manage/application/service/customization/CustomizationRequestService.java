package net.summerfarm.manage.application.service.customization;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.req.customization.CustomizationRequestReq;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.manage.application.inbound.controller.customization.assembler.CustomizationRequestAssembler;
import net.summerfarm.manage.application.inbound.controller.customization.input.CustomizationRequestQueryInput;
import net.summerfarm.manage.application.inbound.controller.customization.vo.CustomizationRequestRelationOrderVO;
import net.summerfarm.manage.application.inbound.controller.customization.vo.CustomizationRequestVO;
import net.summerfarm.manage.application.service.order.OrderQueryService;
import net.summerfarm.manage.application.service.product.mall.InventoryCommandService;
import net.summerfarm.manage.application.service.product.mall.SkuCopyService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.constants.Conf;
import net.summerfarm.manage.common.constants.SMSConstant;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.dto.SMS;
import net.summerfarm.manage.common.dto.SkuCopyDTO;
import net.summerfarm.manage.common.enums.CustomizationRequestStatusEnum;
import net.summerfarm.manage.common.enums.MType;
import net.summerfarm.manage.common.msg.ArrivalNoticeMsg;
import net.summerfarm.manage.common.msg.CustomizationConfirmMsg;
import net.summerfarm.manage.common.util.WeChatUtils;
import net.summerfarm.manage.common.valueobject.MQData;
import net.summerfarm.manage.domain.activity.service.ActivitySkuDetailCommandDomainService;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestEntity;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;
import net.summerfarm.manage.domain.customization.param.CustomizationRequestQueryParam;
import net.summerfarm.manage.domain.customization.repository.CustomizationRequestRepository;
import net.summerfarm.manage.domain.customization.repository.CustomizationRequestSkuMappingRepository;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.order.flatObject.CustomOrderInventoryInfoDTO;
import net.summerfarm.manage.domain.order.service.OrderRelationQueryDomainService;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.repository.AreaSkuCommandRepository;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.domain.timing.entity.TimingRuleEntity;
import net.summerfarm.manage.domain.timing.repository.TimingRuleRepository;
import net.summerfarm.manage.domain.timing.service.TimingRuleDomainService;
import net.summerfarm.manage.facade.goods.TaxRateFacade;
import net.summerfarm.manage.facade.message.WxSendMessageFacade;
import net.summerfarm.manage.facade.message.input.SendMessageInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 定制需求查询服务
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Slf4j
@Service
public class CustomizationRequestService {

    @Autowired
    private CustomizationRequestRepository customizationRequestRepository;

    @Autowired
    private CustomizationRequestSkuMappingRepository customizationRequestSkuMappingRepository;

    @Autowired
    private OrderQueryService orderQueryService;

    @Autowired
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Autowired
    private MerchantQueryRepository merchantQueryRepository;

    @Autowired
    private FollowUpRelationRepository followUpRelationRepository;

    @Autowired
    private WxSendMessageFacade wxSendMessageFacade;

    @Autowired
    private SkuCopyService skuCopyService;

    @Autowired
    private ActivitySkuDetailCommandDomainService activitySkuDetailCommandDomainService;

    @Autowired
    private TimingRuleDomainService timingRuleDomainService;

    @Autowired
    private InventoryCommandService inventoryCommandService;

    @Autowired
    private AreaSkuCommandRepository areaSkuCommandRepository;

    @Resource
    private OrderRelationQueryDomainService orderRelationQueryDomainService;

    @Resource
    private TimingRuleRepository timingRuleRepository;

    @Resource(name = "customizationTaskExecutor")
    private Executor taskExecutor;

    @Autowired
    private DeliveryPlanQueryRepository deliveryPlanQueryRepository;

    @Autowired
    private InventoryQueryRepository inventoryQueryRepository;

    @Autowired
    private MqProducer mqProducer;

    @Autowired
    private TaxRateFacade taxRateFacade;


    /**
     * 分页查询定制需求列表
     *
     * @param input 查询条件
     * @return 分页结果
     */
    public PageInfo<CustomizationRequestVO> getPage(CustomizationRequestQueryInput input) {
        String orderNo = input.getOrderNo ();

        CustomizationRequestQueryParam param = CustomizationRequestAssembler.queryInputToParam (input);

        if(StringUtils.isNotBlank (orderNo)){
            String masterOrderNo = orderRelationQueryDomainService.queryMasterOrderNoByOrderNo (orderNo);
            if(StringUtils.isBlank (masterOrderNo)){
                return PageInfo.of (new ArrayList<> ());
            }
            param.setMasterOrderNo (masterOrderNo);
        }

        PageInfo<CustomizationRequestEntity> page = customizationRequestRepository.page(param, input.getPageIndex(), input.getPageSize());
        return PageInfoConverter.toPageResp(page, CustomizationRequestAssembler::entityToVO);
    }

    /**
     * 根据ID查询定制需求详情
     *
     * @param id 主键ID
     * @return 定制需求详情
     */
    public CustomizationRequestVO getDetail(Long id) {
        CustomizationRequestEntity entity = customizationRequestRepository.findById(id);
        CustomizationRequestVO vo = CustomizationRequestAssembler.entityToVO(entity);

        List<CustomizationRequestSkuMappingEntity> skuMappingEntities = customizationRequestSkuMappingRepository.findByCustomizationRequestId(id);

        if (!CollectionUtil.isEmpty(skuMappingEntities)) {
            List<String> skus = skuMappingEntities.stream().map(CustomizationRequestSkuMappingEntity::getSku).collect(Collectors.toList());
            List<CustomOrderInventoryInfoDTO> customOrderInventoryInfoDTOS = orderQueryService.queryCustomOrderInventoryInfo(entity.getMasterOrderNo());

            List<CustomizationRequestRelationOrderVO> relationOrderVOList = customOrderInventoryInfoDTOS.stream().filter(i -> skus.contains(i.getSkuCode())).map(i -> CustomizationRequestAssembler.customOrderInventoryInfoDTO2VO(i)).collect(Collectors.toList());
            vo.setRelationOrderVOList(relationOrderVOList);
        }
        return vo;
    }

    public CustomizationRequestVO queryByMasterOrderNo(String masterOrderNo) {
        if(StringUtils.isBlank (masterOrderNo)){
            return null;
        }
        CustomizationRequestEntity entity = customizationRequestRepository.queryByMasterOrderNo(masterOrderNo);
        return CustomizationRequestAssembler.entityToVO(entity);
    }


    /**
     * 提交设计
     *
     * @param id             定制需求ID
     * @param designImage    设计效果图
     * @param designerRemark 设计师备注
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitDesign(Long id, String designImage, String designerRemark) {
        CustomizationRequestEntity repository = customizationRequestRepository.findById(id);
        if (repository == null) {
            throw new BizException("定制需求不存在");
        }
        boolean sendMessage = false;
        if (StringUtils.isNotBlank(designImage)) {
            sendMessage = !designImage.equals (repository.getDesignImage ());
            repository.setDesignImage(designImage);
            if (!CustomizationRequestStatusEnum.canTransition(repository.getStatus(), CustomizationRequestStatusEnum.PENDING_CONFIRM.getCode())) {
                throw new BizException("当前状态不允许流转");
            }
            repository.setStatus(CustomizationRequestStatusEnum.PENDING_CONFIRM.getCode());
        }
        repository.setDesignerRemark(designerRemark);
        repository.setDesignImage(designImage);

        if (sendMessage) {
            // 发送模版消息
            final MerchantEntity merchantEntity = merchantQueryRepository.selectById(repository.getMId());
            if (merchantEntity == null) {
                throw new BizException("当前定制需求的门店信息不存在");
            }
            List<CustomOrderInventoryInfoDTO> customOrderInventoryInfoList = orderQueryService.queryCustomOrderInventoryInfo(repository.getMasterOrderNo());
            for (CustomOrderInventoryInfoDTO customOrderInventoryInfo : customOrderInventoryInfoList) {
                SendMessageInput sendMessageInput = new SendMessageInput();
                sendMessageInput.setMessage(CustomizationConfirmMsg.buildTemplateMessage(customOrderInventoryInfo.getOrderNo(), customOrderInventoryInfo.getPdName(), customOrderInventoryInfo.getOrderQuantity(), customOrderInventoryInfo.getOrderTotalPrice()));
                sendMessageInput.setOpenId(merchantEntity.getOpenid());
                sendMessageInput.setTemplateId(nacosPropertiesHolder.getCustomizationRequestDesignConfirmWxTemplateId());
                sendMessageInput.setJumpUrl(WeChatUtils.getWeChatCode(Conf.CUSTOM_DETAIL_URL + customOrderInventoryInfo.getOrderNo()));
                wxSendMessageFacade.sendMessage(sendMessageInput);
            }
        }
        customizationRequestRepository.update(repository);
    }


    /**
     * 复制SKU并保存定制需求（带事务 + 并行执行）
     *
     * @param req 定制需求请求
     * @return 新SKU列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> copySkuAndSaveCustomizationRequestWithTransaction(CustomizationRequestReq req) {
        List<InventoryEntity> inventoryEntities = skuCopyService.queryTemplateSku (req.getPdId (), req.getWeightLike ());

        Set<String> srcSkuList = inventoryEntities.stream ()
                .map (InventoryEntity::getSku)
                .collect (Collectors.toSet ());

        List<TimingRuleEntity> rules = timingRuleRepository.findByAreaAndSkus(req.getAreaNo (), srcSkuList);
        if (CollectionUtils.isEmpty(rules)) {
            throw new BizException ("模版sku没有对应的省心送规则");
        }
        List<String> collect = rules.stream ().map (TimingRuleEntity::getTimingSku).collect (Collectors.toList ());
        List<InventoryEntity> templateSkuList = inventoryEntities.stream ().filter (i -> collect.contains (i.getSku ())).collect (Collectors.toList ());

        try {
            // 第一步：查询模版sku并复制（必须先执行，其他步骤依赖这个结果）
            SkuCopyDTO skuCopyDTO = skuCopyService.queryTemplateSkuAndSave (req.getPdId (), templateSkuList, req.getAreaNo (), req.getStoreName ());
            Map<String, String> map = skuCopyDTO.getSkuMap ();

            if (map.isEmpty()) {
                throw new RuntimeException("未找到可复制的模板SKU");
            }

            // 第二步：并行执行可以独立进行的操作
            CompletableFuture<Void> activityTask = CompletableFuture.runAsync(() -> {
                try {
                    // 复制阶梯价 活动
                    activitySkuDetailCommandDomainService.selectAndCopyByAreaAndSku(map, req.getAreaNo());
                } catch (Exception e) {
                    log.error("复制活动配置失败", e);
                    throw new RuntimeException("复制活动配置失败", e);
                }
            }, taskExecutor);

            CompletableFuture<Void> timingTask = CompletableFuture.runAsync(() -> {
                try {
                    // 复制省心送
                    timingRuleDomainService.selectAndCopyBySku(map, rules);
                } catch (Exception e) {
                    log.error("复制省心送配置失败", e);
                    throw new RuntimeException("复制省心送配置失败", e);
                }
            }, taskExecutor);

            CompletableFuture<Void> inventoryTask = CompletableFuture.runAsync(() -> {
                try {
                    // 查询标签并复制
                    inventoryCommandService.queryAndCopy(map);
                } catch (Exception e) {
                    log.error("复制库存标签失败", e);
                    throw new RuntimeException("复制库存标签失败", e);
                }
            }, taskExecutor);

            // 保存定制需求及sku映射关系（可以与上面的操作并行）
            CompletableFuture<Void> saveTask = CompletableFuture.runAsync(() -> {
                try {
                    saveCustomizationRequest(req, map);
                } catch (Exception e) {
                    log.error("保存定制需求失败", e);
                    throw new RuntimeException("保存定制需求失败", e);
                }
            }, taskExecutor);

            CompletableFuture<Void> taxRateTask = CompletableFuture.runAsync(() -> {
                try {
                    // 复制税率
                    taxRateFacade.copyTaxRate (req.getPdId (), skuCopyDTO.getPdId ());
                } catch (Exception e) {
                    log.error("复制税率失败", e);
                    throw new RuntimeException("复制税率失败", e);
                }
            }, taskExecutor);


            // 等待所有并行任务完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                activityTask, timingTask, inventoryTask, saveTask,taxRateTask
            );

            // 阻塞等待所有任务完成，如果有异常会抛出
            allTasks.join();

            log.info("SKU复制和定制需求保存完成，共处理{}个SKU", map.size());
            return new ArrayList<>(map.values());

        } catch (Exception e) {
            log.error("复制SKU并保存定制需求失败", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveCustomizationRequest(CustomizationRequestReq req, Map<String, String> map) {
        CustomizationRequestEntity entity = CustomizationRequestAssembler.commandInputToEntity(req);
        // 设置默认状态
        entity.setStatus(CustomizationRequestStatusEnum.PENDING_GENERATION.getCode());
        CustomizationRequestEntity repository = customizationRequestRepository.save(entity);

        Long customizationRequestId = repository.getId();

        List<CustomizationRequestSkuMappingEntity> mappings = new ArrayList<>();
        map.forEach((srcsku, newsku) -> {
            CustomizationRequestSkuMappingEntity mappingEntity = new CustomizationRequestSkuMappingEntity();
            mappingEntity.setCustomizationRequestId(customizationRequestId);
            mappingEntity.setSku(newsku);
            mappingEntity.setSourceSku(srcsku);
            mappings.add(mappingEntity);
        });
        if (CollectionUtil.isNotEmpty(mappings)) {
            customizationRequestSkuMappingRepository.batchSave(mappings);
        }
    }


    public void updateStatusByOrderNo(String masterOrderNo, Set<Integer> orderStatus) {
        Set<Integer> status = orderStatus.stream().map(CustomizationRequestStatusEnum::getByOrdertatus).filter(Objects::nonNull).collect(Collectors.toSet());

        if (CollectionUtil.isEmpty(status) || status.size() > 1) {
            log.info("updateStatusByOrderNo，没有需要更新的数据 或者订单状态不一致,orderNo={},status={}", masterOrderNo, status);
            return;
        }
        customizationRequestRepository.updateStatusByMasterOrderNo(status.iterator().next(), masterOrderNo);
    }


    /**
     * 发送定制需求的到货提醒
     */
    public void sendArrivalNotice() {
        // 1. 查询出状态是3的定制需求
        CustomizationRequestQueryParam param = new CustomizationRequestQueryParam();
        param.setStatus(CustomizationRequestStatusEnum.APPROVED.getCode());
        List<CustomizationRequestEntity> customizationRequestEntityList = customizationRequestRepository.findList(param);
        if (CollectionUtils.isEmpty(customizationRequestEntityList)) {
            log.info("没有待发送的定制需求");
            return;
        }
        // 2. 循环定制需求，查询库存并发送通知
        for (CustomizationRequestEntity customizationRequestEntity : customizationRequestEntityList) {
            log.info("循环定制需求，查询库存并发送通知 >>> {}", JSON.toJSONString(customizationRequestEntity));
            List<CustomizationRequestSkuMappingEntity> customizationRequestSkuList = customizationRequestSkuMappingRepository.findByCustomizationRequestId(customizationRequestEntity.getId());
            if (CollectionUtils.isEmpty(customizationRequestSkuList)) {
                log.info("当前定制需求没有定制的sku");
                continue;
            }
            // 查询库存
            List<CustomOrderInventoryInfoDTO> customOrderInventoryInfo = orderQueryService.queryCustomOrderInventoryInfo(customizationRequestEntity.getMasterOrderNo());
            if (CollectionUtils.isEmpty(customOrderInventoryInfo)) {
                log.info("当前定制需求没有定制的库存");
                continue;
            }
            //获取当前订单的所有配送信息
            List<DeliveryPlanFlatObject> deliveryPlanList = deliveryPlanQueryRepository.getWaitingDeliveryPlanQuantity(customOrderInventoryInfo.stream().map(CustomOrderInventoryInfoDTO::getOrderNo).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(deliveryPlanList)) {
                log.info("当前定制需求已经设置过配送计划，说明已经存在过库存，不处理当前定制需求");
                continue;
            }
            final MerchantEntity merchantEntity = merchantQueryRepository.selectById(customizationRequestEntity.getMId());
            if (merchantEntity == null) {
                log.info("当前定制需求没有定制的门店");
                continue;
            }
            List<FollowUpRelationEntity> followUpRelationEntityList = followUpRelationRepository.batchQueryByMids(Collections.singletonList(customizationRequestEntity.getMId()));
            String bdName = CollectionUtils.isEmpty(followUpRelationEntityList) ? "" : followUpRelationEntityList.get(0).getAdminName();

            // 商品基础属性信息
            List<InventoryEntity> skuInfoList = inventoryQueryRepository.listBySkus(customOrderInventoryInfo.stream().map(CustomOrderInventoryInfoDTO::getSkuCode).distinct().collect(Collectors.toList()));
            Map<String, InventoryEntity> skuInfoMap = skuInfoList.stream().collect(Collectors.toMap(InventoryEntity::getSku, Function.identity(), (a, b) -> a));

            // 发送通知
            for (CustomOrderInventoryInfoDTO customOrderInventoryInfoDTO : customOrderInventoryInfo) {
                final InventoryEntity inventoryEntity = skuInfoMap.get(customOrderInventoryInfoDTO.getSkuCode());
                if (inventoryEntity == null) {
                    log.info("当前定制需求没有定制的商品基础信息 customOrderInventoryInfoDTO >>> {}", JSON.toJSONString(customOrderInventoryInfoDTO));
                    continue;
                }
                // 部分到货，发送飞书群消息
                if (customOrderInventoryInfoDTO.getSaleInventoryQuantity() > 0
                        && customOrderInventoryInfoDTO.getSaleInventoryQuantity() < customOrderInventoryInfoDTO.getOrderQuantity()) {
                    StringBuilder msg = new StringBuilder();
                    msg.append("# 有定制品未全部到货，请关注！").append("\n")
                            .append("**商品名称：**").append(customOrderInventoryInfoDTO.getPdName()).append("/").append(customOrderInventoryInfoDTO.getWeight()).append("\n")
                            .append("**SKU编号：**").append(customOrderInventoryInfoDTO.getSkuCode()).append("\n")
                            .append("**下单数量：**").append(customOrderInventoryInfoDTO.getOrderQuantity()).append("\n")
                            .append("**到货库存：**").append(customOrderInventoryInfoDTO.getSaleInventoryQuantity()).append("\n")
                            .append("**门店名称：**").append(merchantEntity.getMname()).append("\n")
                            .append("**所属销售：**").append(bdName);
                    FeishuBotUtil.sendMarkdownMsg(nacosPropertiesHolder.getCustomizationRequestFeishuAlarmUrl(), msg.toString());
                }
                // 全部到货，发送微信模板消息 和 短信
                if (customOrderInventoryInfoDTO.getSaleInventoryQuantity() >= customOrderInventoryInfoDTO.getOrderQuantity()){
                    // 发送微信模板消息
                    SendMessageInput sendMessageInput = new SendMessageInput();
                    sendMessageInput.setMessage(ArrivalNoticeMsg.templateMessage(merchantEntity.getMname(), customOrderInventoryInfoDTO.getPdName()));
                    sendMessageInput.setOpenId(merchantEntity.getOpenid());
                    sendMessageInput.setTemplateId(nacosPropertiesHolder.getArrivalNoticeWxTemplateId());
                    sendMessageInput.setJumpUrl(WeChatUtils.getWeChatCode(Conf.ARRIVAL_NOTICE_URL +"&pdId="+inventoryEntity.getPdId()+"&sku="+customOrderInventoryInfoDTO.getSkuCode()));
                    wxSendMessageFacade.sendMessage(sendMessageInput);

                    // 发短信
                    SMS sms = new SMS();
                    sms.setPhone(merchantEntity.getPhone());
                    sms.setSceneId(SMSConstant.ARRIVAL_NOTICE_SCENE_ID);
                    sms.setArgs(Collections.singletonList(customOrderInventoryInfoDTO.getPdName()));
                    log.info("订单到货通知发送短信 >>> {}", JSON.toJSONString(sms));
                    MQData mqData = new MQData();
                    mqData.setType(MType.SMS.name());
                    mqData.setData(sms);
                    mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null, JSONObject.toJSONString(mqData));
                }
            }
        }


    }

}
