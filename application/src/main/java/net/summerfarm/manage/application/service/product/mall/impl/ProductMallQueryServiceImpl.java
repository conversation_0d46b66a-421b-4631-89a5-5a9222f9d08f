package net.summerfarm.manage.application.service.product.mall.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.product.input.MarketBaseQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductPageInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.*;
import net.summerfarm.manage.application.service.product.converter.MarketResultConverter;
import net.summerfarm.manage.application.service.product.converter.ProductInputConverter;
import net.summerfarm.manage.application.service.product.converter.ProductResultConverter;
import net.summerfarm.manage.application.service.product.mall.ProductQueryService;
import net.summerfarm.manage.domain.product.entity.MarketItemAggListEntity;
import net.summerfarm.manage.domain.product.entity.MarketItemListEntity;
import net.summerfarm.manage.domain.product.entity.ProductEntity;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.service.MarketDomainService;
import net.summerfarm.manage.domain.product.service.ProductDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
@Slf4j
@Service
public class ProductMallQueryServiceImpl implements ProductQueryService {

    @Resource
    private ProductDomainService productDomainService;

    @Resource
    private MarketDomainService marketDomainService;

    @Override
    public PageInfo<ProductVO> pageByQuery(ProductPageInput input) {
        if (input.getPageIndex() == null || input.getPageSize() == null) {
            input.setPageIndex(1);
            input.setPageSize(10);
        }
        ProductPageQueryParam param = ProductInputConverter.toProductPageQueryParam(input);
        PageInfo<ProductEntity> pageInfo = productDomainService.pageByQuery(param);
        if (pageInfo == null || CollectionUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        List<ProductVO> list = ProductResultConverter.toProductVOList(pageInfo.getList());

        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<MarketItemAggListVO> pageAgg(MarketBaseQueryInput input) {
        PageInfo<MarketItemAggListEntity> pageInfo = marketDomainService.pageAgg(ProductInputConverter.toMarketBaseQueryParam(input));

        List<MarketItemAggListVO> list = MarketResultConverter.INSTANCE
                .marketItemAggListEntityListToMarketItemAggListVOList(pageInfo.getList());
        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<MarketItemListVO> pageSku(MarketBaseQueryInput input) {
        PageInfo<MarketItemListEntity> pageInfo = marketDomainService.pageSku(ProductInputConverter.toMarketBaseQueryParam(input));

        List<MarketItemListVO> list = MarketResultConverter.INSTANCE
                .marketItemListEntityListToMarketItemListVOList(pageInfo.getList());
        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<ProductVO> selectPage(ProductPageInput input) {
        if (input.getPageIndex() == null || input.getPageSize() == null) {
            input.setPageIndex(1);
            input.setPageSize(10);
        }
        ProductPageQueryParam param = ProductInputConverter.toProductPageQueryParam(input);
        PageInfo<ProductEntity> pageInfo = productDomainService.selectPage(param);
        if (pageInfo == null || CollectionUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        List<ProductVO> list = ProductResultConverter.toProductVOList(pageInfo.getList());
        PageInfo result = new PageInfo<>(pageInfo.getList());
        result.setList(list);
        return result;
    }
}
