package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

import net.xianmu.common.input.BasePageInput;



/**
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
@Data
public class AppPopBiaoguoTopSaleSkuQueryInput extends BasePageInput implements Serializable{
	/**
	 * Unique ID (auto increment starting from 10000)
	 */
	private Long id;

	/**
	 * 商品所属的一级类目名称
	 */
	private String categoryName;

	/**
	 * 竞争对手的名称
	 */
	private String competitor;

	/**
	 * 商品的SKU编码（唯一标识）
	 */
	private String skuCode;

	/**
	 * 数据爬取时间，格式为字符串
	 */
	private String spiderFetchTime;

	/**
	 * 商品的标准最终售价
	 */
	private Double finalStandardPrice;

	/**
	 * 月销量（最近一个月的销量）
	 */
	private Long monthSale;

	/**
	 * 商品详情页面的URL链接
	 */
	private String url;

	/**
	 * 商品的毛重（含包装）
	 */
	private String grossWeight;

	/**
	 * 商品的净重（不含包装）
	 */
	private String netWeight;

	/**
	 * 商品的规格描述
	 */
	private String specification;

	/**
	 * 商品名称
	 */
	private String goodsName;

	/**
	 * 近三天的销量
	 */
	private Long salesVolume3d;

	/**
	 * 月销量的成交总金额（GMV）
	 */
	private Double monthsaleGmv;

	/**
	 * 商品所属的二级类目名称
	 */
	private String categoryLevel2;

	/**
	 * 三天前的月销量成交总金额（GMV）
	 */
	private Double monthsaleGmv3dAgo;

	/**
	 * 与当前商品匹配的Xianmu SKU列表（JSON格式或逗号分隔）
	 */
	private String topMatchedXianmuSkuList;

	/**
	 * 数据创建时间，格式为字符串
	 */
	private String createTime;

	/**
	 * 数据分区字段，通常表示日期（例如20240101）
	 */
	private String ds;

	/**
	 * 只查询待引用
	 */
	private boolean onlyUnMapped;

	/**
	 * 一级类目
	 */
	private String category1;
	/**
	 * 二级类目
	 */
	private String category2;
	/**
	 * 三级类目
	 */
	private String category3;

	/**
	 * 商品的SKU编码（唯一标识）
	 */
	private List<String> notInSkuCodeList;

	/**
	 * 买手id
	 */
	private Long buyerId;


}