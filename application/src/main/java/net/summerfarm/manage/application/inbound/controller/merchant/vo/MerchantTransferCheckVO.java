package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
@Data
public class MerchantTransferCheckVO implements Serializable{


    /**
     *
     */
    @JsonProperty("mId")
    private Long mId;

    /**
     * 商户名称
     */
    private String mname;

    /**
     * 主联系人
     */
    private String mcontact;

    /**
     * 手机
     */
    private String phone;

    /**
     *
     */
    private Integer islock;


    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 地区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     *
     */
    private Integer areaNo;

    /**
     *     ADMIN(1, "大客户"),
     *         MERCHANT(2, "单店"),
     *         SAAS(3, "saas品牌客户");
     */
    private String size;

    /**
     *运营服务大区域名称
     */
    private String areaName;

    /**
     * 归属bd
     */
    private String adminName;



}