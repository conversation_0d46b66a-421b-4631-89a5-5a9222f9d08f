package net.summerfarm.manage.application.service.major.converter;

import net.summerfarm.manage.domain.major.param.command.MajorPriceLogCommandParam;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MajorPriceLogConvert {
    MajorPriceLogConvert INSTANCE = Mappers.getMapper(MajorPriceLogConvert.class);

    MajorPriceLogCommandParam buildMajorPriceLogCommandParam(MajorPriceCommandParam param);
}
