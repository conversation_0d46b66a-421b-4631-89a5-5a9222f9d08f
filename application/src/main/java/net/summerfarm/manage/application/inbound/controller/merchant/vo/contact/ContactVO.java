package net.summerfarm.manage.application.inbound.controller.merchant.vo.contact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantContactAddressRemarkVO;
import net.summerfarm.manage.facade.deliivery.dto.DistributionRulesDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ContactVO implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;

    @ApiModelProperty(value = "鲜沐联系人id")
    private Long contactId;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "性别")
    private Boolean gender;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "微信号")
    private String weixincode;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "状态(1正常或审核通过、2删除、3待审核、4审核不通过)")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "配送车辆")
    private String deliveryCar;

    @ApiModelProperty(value = "1默认地址 与merchat中一致")
    private Integer isDefault;

    @ApiModelProperty(value = "经纬度")
    private String poiNote;

    @ApiModelProperty(value = "到仓库距离")
    private BigDecimal distance;

    @ApiModelProperty(value = "预排路线")
    private String path;

    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    @ApiModelProperty(value = "配送仓编号")
    private Integer storeNo;

    @ApiModelProperty(value = "配送周期")
    private String deliveryFrequent;

    /**
     * 运营区域id
     */
    private Integer areaNo;
    /**
     * 运营区域名
     */
    private String areaName;

    /**
     * 是否修改
     */
    private Boolean modify;
    /**
     * 是否使用新地址
     */
    private Boolean useNew;

    /**
     * 首配日
     */
    private LocalDate nextDeliveryDate;

    /**
     * 运费规则
     */
    private String deliveryRule;

    /**
     * 运费
     */
    private BigDecimal deliveryFee;

    /**
     * 归属区域id adCodeMsg表
     */
    private Integer acmId;


    /**
     * 地址备注
     */
    private String addressRemark;

    /**
     * 自定义地址备注
     */
    private MerchantContactAddressRemarkVO contactAddressRemark;


    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 鲜沐m_id
     */
    private Long mId;

    /**
     * 运费规则-新版
     */
    private DistributionRulesDTO distributionRulesDTO;



    /**
     * 围栏状态  0：正常，3：暂停
     */
    private Integer fenceStatus;
}
