package net.summerfarm.manage.application.converter.job;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.job.vo.JobMerchantPoolVO;
import net.summerfarm.manage.application.inbound.controller.job.vo.JobProductVO;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobDetailVo;
import net.summerfarm.manage.application.inbound.controller.job.vo.MerchantJobVo;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductVO;
import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.xianmu.jobsdk.model.po.CrmJob;
import net.xianmu.jobsdk.model.po.CrmJobCompletionCriteria;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MerchantJobConverter {

    MerchantJobConverter INSTANCE = Mappers.getMapper(MerchantJobConverter.class);

    MerchantJobVo poToVo(CrmJob crmJob);

    PageInfo<MerchantJobVo> poToVo(PageInfo<CrmJob> crmJobs);

    @Mapping(target = "id", source = "crmJob.id")
    @Mapping(target = "categoryList", qualifiedByName = "convertCategoryList")
    @Mapping(target = "orderTypeList", qualifiedByName = "convertJsonToIntegerList")
    @Mapping(target = "merchantPoolList", ignore = true) // 人群包列表要带入详情,先忽略
    @Mapping(target = "productList", ignore = true) // 品列表要带入详情,先忽略
    MerchantJobDetailVo assembleDetail(CrmJob crmJob, CrmJobCompletionCriteria completionCriteria);

    JobMerchantPoolVO poolEntityToVo(MerchantPoolInfoEntity entity);

    List<JobMerchantPoolVO> poolEntityToVo(List<MerchantPoolInfoEntity> entities);

    JobProductVO productVoToJobProductVo(ProductVO productVO);

    List<JobProductVO> productVoToJobProductVo(List<ProductVO> productVOs);

    // ------------------------------------------------ 以下为自定义规则 ------------------------------------------------

    @Named("convertCategoryList")
    default List<List<Long>> convertCategoryList(String categoryList) {
        if (StrUtil.isEmpty(categoryList)) {
            return null;
        }

        return JSON.parseObject(categoryList, new TypeReference<List<List<Long>>>() {
        });
    }

    @Named("convertJsonToIntegerList")
    default List<Integer> convertIntegerList(String jsonString) {
        if (StrUtil.isEmpty(jsonString)) {
            return null;
        }

        return JSON.parseArray(jsonString, Integer.class);
    }
}
