package net.summerfarm.manage.application.inbound.controller.merchant.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:33:03
 */
@Data
public class MerchantCancelUpdateInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @NotNull(message = "注销ID不能为空！")
    private Long id;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 注销状态 默认-1（待注销） MerchantCancelEnum
     */
    private Integer status;

    /**
     * 申请原因
     */
    private String remake;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 申请凭证-后台申请必填
     */
    private String certificate;
}
