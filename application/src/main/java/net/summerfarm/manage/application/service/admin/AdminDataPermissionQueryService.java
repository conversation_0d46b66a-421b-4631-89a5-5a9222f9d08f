package net.summerfarm.manage.application.service.admin;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.application.inbound.controller.admin.input.query.AdminDataPermissionQueryInput;

/**
 *
 * @date 2024-06-19 16:33:43
 * @version 1.0
 *
 */
public interface AdminDataPermissionQueryService {

    /**
     * @description: 新增
     * @return AdminDataPermissionEntity
     **/
    PageInfo<AdminDataPermissionEntity> getPage(AdminDataPermissionQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    AdminDataPermissionEntity getDetail(Long id);

}