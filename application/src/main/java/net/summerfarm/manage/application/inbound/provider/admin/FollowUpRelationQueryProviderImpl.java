package net.summerfarm.manage.application.inbound.provider.admin;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.FollowUpRelationQueryProvider;
import net.summerfarm.client.req.admin.FollowUpRelationQueryReq;
import net.summerfarm.client.resp.admin.FollowUpRelationQueryResp;
import net.summerfarm.manage.application.inbound.provider.admin.converter.FollowUpRelationConverter;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/19 16:13
 * @PackageName:net.summerfarm.manage.application.inbound.provider.admin
 * @ClassName: FollowUpRelationQueryProviderImpl
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@DubboService
@Component
public class FollowUpRelationQueryProviderImpl implements FollowUpRelationQueryProvider {

    @Resource
    private FollowUpRelationRepository followUpRelationRepository;

    @Override
    public DubboResponse<List<FollowUpRelationQueryResp>> queryFollowUpRelation(@Valid FollowUpRelationQueryReq followUpRelationQueryReq) {
        List<FollowUpRelationEntity> entities = followUpRelationRepository.batchQueryByMids(followUpRelationQueryReq.getMIds());
        return DubboResponse.getOK(FollowUpRelationConverter.toFollowUpRelationQueryRespList(entities));
    }
}
