package net.summerfarm.manage.application.inbound.controller.merchant.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
/**
 * 更换账号待审核
 */
public class MerchantAuditVO extends MerchantBaseVO implements Serializable {
    private static final long serialVersionUID = 5640865689281001205L;
    /**
     * 提交时间
     */
    private LocalDateTime registerTime;
    /**
     * 门店修改的记录
     */
    private Long contactAdjustId;
}
