package net.summerfarm.manage.application.service.merchant.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MajorMerchantQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MajorCustomerMerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.contact.ContactVO;
import net.summerfarm.manage.application.service.merchant.MerchantBaseService;
import net.summerfarm.manage.application.service.merchant.MerchantQueryService;
import net.summerfarm.manage.application.service.merchant.converter.XmMerchantStorePageReqConvert;
import net.summerfarm.manage.application.service.merchant.converter.XmMerchantStorePageRespConvert;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.enums.DataSynchronizationInformationEnum;
import net.summerfarm.manage.common.enums.DistributionRulesTypeEnum;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.service.AreaService;
import net.summerfarm.manage.domain.invoice.entity.InvoiceConfig;
import net.summerfarm.manage.domain.invoice.repository.InvoiceConfigRepository;
import net.summerfarm.manage.domain.invoice.service.InvoiceConfigService;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.offline.entity.DataSynchronizationInformationEntity;
import net.summerfarm.manage.domain.offline.repository.CrmMerchantDayLabelRepository;
import net.summerfarm.manage.domain.offline.repository.DataSynchronizationInformationRepository;
import net.summerfarm.manage.facade.deliivery.dto.DeliveryFeeRuleDTO;
import net.summerfarm.manage.facade.deliivery.dto.DistributionRulesDTO;
import net.summerfarm.manage.facade.deliivery.input.DistributionRulesInfoInput;
import net.summerfarm.manage.facade.merchant.ContactFacade;
import net.summerfarm.manage.facade.merchant.MerchantQueryFacade;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreChangeLogResultResp;
import net.xianmu.usercenter.client.merchant.resp.XmMerchantStorePageResp;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-10-19 15:25:01
 */
@Service
public class MerchantQueryServiceImpl extends MerchantBaseService implements MerchantQueryService {

    @Resource
    private MerchantQueryRepository merchantQueryRepository;
    @Resource
    private DataSynchronizationInformationRepository dataSynchronizationInformationRepository;
    @Resource
    private CrmMerchantDayLabelRepository crmMerchantDayLabelRepository;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    MerchantQueryFacade merchantQueryFacade;
    @Resource
    InvoiceConfigService invoiceConfigService;
    @Resource
    AreaService areaService;
    @Resource
    MerchantBaseService merchantBaseService;
    @Resource
    private ContactFacade contactFacade;
    @Resource
    InvoiceConfigRepository invoiceConfigRepository;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    @Override
    public PageInfo<MerchantVO> getPage(MerchantQueryInput input) {
        //初始化query
        initQuery(input);

        // 离线库筛选,目前的写法会导致查询的数据遗漏
        if (Objects.nonNull(input.getMerchantLabel())) {
            return queryMerchantInfoByOffline(input);
        }
        return queryMerchantInfo(input);
    }

    /**
     * 查询标签的数据
     *
     * @param input
     * @return
     */
    private PageInfo<MerchantVO> queryMerchantInfoByOffline(MerchantQueryInput input) {
        DataSynchronizationInformationEntity thisMonth = dataSynchronizationInformationRepository.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer dateFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        // 构建标签查询条件
        String[] merchantLabel = input.getMerchantLabel().trim().split(Global.SEPARATING_SYMBOL);
        List<String> merchantLabelList = Arrays.asList(merchantLabel);
        input.setMerchantLabelList(merchantLabelList);
        // 查询具有该标签的客户id具体信息,循环pageSize次
        PageHelper.startPage(input.getPageIndex(), input.getPageSize());
        List<Long> mids = crmMerchantDayLabelRepository.selectMidListByInput(input, dateFlag);
        if (CollectionUtils.isEmpty(mids)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>());
        }
        input.setMIds(mids);
        List<MerchantVO> merchantVOS = this.queryMerchantInfoDetail(input);
        return PageInfoHelper.createPageInfo(merchantVOS);
    }

    private PageInfo<MerchantVO> queryMerchantInfo(MerchantQueryInput selectKeys) {
        // 买家域筛选
        PageInfo<MerchantVO> basePage = getBasePage(selectKeys);
        //组装数据
        baseListMerge(basePage.getList());
        // 组装数据
        return basePage;
    }

    /**
     * 查询商户信息
     *
     * @param selectKeys 查询条件
     * @return 商户信息
     */
    public List<MerchantVO> queryMerchantInfoDetail(MerchantQueryInput selectKeys) {
        selectKeys = Optional.ofNullable(selectKeys).orElse(new MerchantQueryInput());

        List<MerchantVO> merchantVOs = getMerchantStoreAndExtends(selectKeys);
        if (CollectionUtil.isNotEmpty(merchantVOs)) {
            this.baseListMerge(merchantVOs);
        }
        return merchantVOs;
    }


    @Override
    public MerchantVO getDetail(Long mid) {
        if (Objects.isNull(mid)) {
            throw new BizException("id不能为空！");
        }
        MerchantQueryInput selectKeys = new MerchantQueryInput();
        selectKeys.setMId(mid);
        List<MerchantVO> merchantVOS = getMerchantStoreAndExtends(selectKeys);
        if (CollectionUtils.isEmpty(merchantVOS)) {
            return null;
        }

        baseDetailMerge(merchantVOS.get(0));

        return merchantVOS.get(0);
    }



    @Override
    public MerchantEntity getByMid(Long mid) {
        MerchantEntity merchantEntity = merchantQueryRepository.selectById(mid);
        return merchantEntity;
    }

    @Override
    public PageInfo<MajorCustomerMerchantVO> majorMerchantList(MajorMerchantQueryInput selectKeys) {
        //rpc调用
        PageInfo<XmMerchantStorePageResp> xmMerchantStorePageRespPageInfo = merchantQueryFacade.pageQuery(XmMerchantStorePageReqConvert.toMerchantStorePageQueryReq(selectKeys));

        PageInfo<MajorCustomerMerchantVO> majorCustomerMerchantVOPageInfo = PageInfoConverter.toPageResp(xmMerchantStorePageRespPageInfo, XmMerchantStorePageRespConvert::toMajorCustomerMerchantVO);
        //门店基础信息
        List<MajorCustomerMerchantVO> list = majorCustomerMerchantVOPageInfo.getList();

        //补充其他信息
        merge(selectKeys.getAdminId(), list);

        return majorCustomerMerchantVOPageInfo;

    }

    private void merge(Long adminId, List<MajorCustomerMerchantVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> mIds = list.stream().map(MajorCustomerMerchantVO::getMId).collect(Collectors.toList());
        List<Integer> areas = list.stream().map(MajorCustomerMerchantVO::getAreaNo).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> storeIds = list.stream().map(MajorCustomerMerchantVO::getStoreId).collect(Collectors.toList());

        //地区码
        Map<Integer, Area> areaMap = areaService.getAreaMap(areas);

        //大客户下发票
        Map<Long, List<InvoiceConfig>> adminInvoiceConfigTittleMap = invoiceConfigRepository.selectByMajorAdminId(adminId).stream().filter(it -> it.getMerchantId() != null
                && it.getMerchantId() > 0).collect(Collectors.groupingBy(InvoiceConfig::getMerchantId));

        Map<Long, List<InvoiceConfig>> merchantInvoiceConfigTittleMap = invoiceConfigRepository.selectByMajorByMids(mIds).stream().collect(Collectors.groupingBy(InvoiceConfig::getMerchantId));
        //
        //主账号信息
        Map<Long, List<MerchantStoreAccountResultResp>> accountMap = queryMerchantAccountMap(storeIds, MerchantAccountEnums.Type.MANAGER);

        Map<Long, List<ContactVO>> contactMap = merchantBaseService.queryNormalContact(storeIds).stream().collect(Collectors.groupingBy(ContactVO::getStoreId));

        Map<Long, MerchantEntity> merchantEntityMap = getMerchantEntityMap(mIds);

        //地址
        list.forEach(it -> {
                    //发票抬头
                    List<InvoiceConfig> invoiceConfigs = adminInvoiceConfigTittleMap.get(it.getMId());
                    if (!CollectionUtils.isEmpty(invoiceConfigs)) {
                        it.setBusinessName(invoiceConfigs.get(0).getInvoiceTitle());
                    }
                    if (StringUtils.isEmpty(it.getBusinessName())) {
                        List<InvoiceConfig> merchantConfig = CollectionUtil.isEmpty(merchantInvoiceConfigTittleMap.get(it.getMId())) ? new ArrayList<>() : merchantInvoiceConfigTittleMap.get(it.getMId()).stream().filter(u -> u.getValidStatus() != null && u.getValidStatus() == 0).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(merchantConfig)) {
                            it.setBusinessName(merchantConfig.get(0).getInvoiceTitle());
                        }
                    }
                    //地区
                    if (it.getAreaNo() != null) {
                        Area area = areaMap.get(it.getAreaNo());
                        if (area != null) {
                            it.setArea(area.getAreaName());
                        }
                    }
                    //联系人信息
                    List<ContactVO> contactVOS = CollectionUtils.isEmpty(contactMap.get(it.getStoreId())) ? new ArrayList<>() : contactMap.get(it.getStoreId()).stream().filter(a -> Objects.equals(a.getIsDefault(), 1)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(contactVOS)) {
                        it.setContactVO(contactVOS.get(0));
                    }
                    //默认没有的就拿id最小的
                    if (it.getContactVO() == null &&  !CollectionUtils.isEmpty(contactMap.get(it.getStoreId()))){
                        List<ContactVO> collect = contactMap.get(it.getStoreId()).stream().sorted(Comparator.comparing(ContactVO::getContactId)).collect(Collectors.toList());
                        it.setContactVO(collect.get(0));
                    }

                    //主账号手机号
                    List<MerchantStoreAccountResultResp> merchantStoreAccountResultResps = accountMap.get(it.getStoreId());
                    if (!CollectionUtils.isEmpty(merchantStoreAccountResultResps)) {
                        it.setPhone(merchantStoreAccountResultResps.get(0).getPhone());
                        it.setContact(merchantStoreAccountResultResps.get(0).getAccountName());
                    }
                    if (nacosPropertiesHolder.getMajorMerchantListQueryDistributionRules()) {
                        DistributionRulesDTO info = contactFacade.getDetail(adminId, it.getContactVO() == null ? null : it.getContactVO().getContactId(), it.getAreaNo());
                        it.setDistributionRulesDTO(info);
                    }

                    if (it.getContactVO() != null) {
                        List<DeliveryFeeRuleDTO> deliveryFeeRuleDTOList = contactFacade.getDeliveryFeeRule(it.getContactVO().getContactId(), adminId, it.getAreaNo(), it.getContactVO().getProvince(), it.getContactVO().getCity());
                        // 只保留全部类目下的阶梯价配置
                        deliveryFeeRuleDTOList.forEach(rule -> {
                            if (!CollectionUtils.isEmpty(rule.getDeliveryFeeRuleDetailDTOList())) {
                                rule.getDeliveryFeeRuleDetailDTOList().removeIf(x -> !Integer.valueOf(1).equals(x.getCategoryType()));
                            }
                        });
                        it.setDeliveryFeeRuleDTOList(deliveryFeeRuleDTOList);
                    }

                    if (merchantEntityMap.get(it.getMId()) != null) {
                        it.setDirect(merchantEntityMap.get(it.getMId()).getDirect());
                    }
                }

        );


    }


}