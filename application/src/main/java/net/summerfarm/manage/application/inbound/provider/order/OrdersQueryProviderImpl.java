package net.summerfarm.manage.application.inbound.provider.order;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.order.OrdersQueryProvider;
import net.summerfarm.client.req.order.OrderDeliveryPlanDetailQueryReq;
import net.summerfarm.client.resp.order.OrderDeliveryPlanDetailResp;
import net.summerfarm.manage.application.inbound.provider.order.converter.OrdersConverter;
import net.summerfarm.manage.application.service.order.OrderQueryService;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 订单查询<br/>
 * date: 2024/12/30 15:42<br/>
 *
 * <AUTHOR> />
 */
@DubboService
@Slf4j
public class OrdersQueryProviderImpl implements OrdersQueryProvider {

    @Resource
    private OrderQueryService orderQueryService;

    @Override
    public DubboResponse<List<OrderDeliveryPlanDetailResp>> queryValidOrderDeliveryPlanDetail(@Valid OrderDeliveryPlanDetailQueryReq req) {
        List<OrderDeliveryPlanFlatObject> orderDeliveryPlanFlatObjects = orderQueryService.queryValidOrderDeliveryPlanDetail(req);

        List<OrderDeliveryPlanDetailResp> respList = orderDeliveryPlanFlatObjects.stream()
                .map(OrdersConverter::flat2OrderDeliveryPlanDetailResp)
                .collect(Collectors.toList());

        return DubboResponse.getOK(respList);
    }
}
