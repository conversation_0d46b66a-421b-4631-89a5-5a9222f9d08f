package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName ProductUpdateInput
 * @Description
 * <AUTHOR>
 * @Date 18:57 2024/5/6
 * @Version 1.0
 **/
@Data
public class ProductUpdateInput implements Serializable {

    @NotNull(message = "商品ID不能为空")
    private Long pdId;

    private String pdNo;

    /**
     * 类目ID
     */
    private Integer categoryId;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 上架时间
     */
    private LocalDateTime createTime;

    /**
     * 商品描述
     */
    private String pddetail;

    /**
     * 详情图片
     */
    private String detailPicture;

    /**
     * 从配送开始的售后时间
     */
    private Integer afterSaleTime;

    /**
     * 售后类型
     */
    private String afterSaleType;

    /**
     * @deprecated 售后单位放到sku维度
     */
    @Deprecated
    private String afterSaleUnit;

    /**
     * 产地
     */
    private Integer origin;

    /**
     * 贮存方式
     */
    private String storageMethod;

    /**
     * 商品介绍
     */
    private String slogan;

    /**
     *
     */
    private String otherSlogan;

    /**
     * 首页缩略图
     */
    private String picturePath;

    /**
     * 贮存区域
     */
    @NotNull(message = "请填贮存区域")
    private Integer storageLocation;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 标记位-过时的spu  1代表过时，商品被删除
     */
    private Integer outdated;

    /**
     * 商品介绍信息
     */
    private String productIntroduction;

    /**
     * 退款原因
     */
    private String  refundType;

    /**
     * 保质期时长
     */
    @Min(value = 1, groups = {Add.class, Update.class})
    @NotNull(message = "请填写保质期时长", groups = Add.class)
    private Integer qualityTime;

    /**
     * 保质期时长单位
     */
    private String qualityTimeUnit;

    /**
     * 保质期时长类型, 0 固定时长, 1 到期时间
     */
    private Integer qualityTimeType;

    /**
     * 操作人adminId
     */
    private Integer warnTime;

    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 上新类型：0、平台 1、大客户 2、帆台上新
     */
    private Integer createType;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 上新审核状态：0、待上新 1、上新成功 2、上新失败
     */
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 商品实物名
     */
    private String realName;

    /**
     * 操作人adminId
     */
    private Integer auditor;

    /**
     * 税率 1%传递 0.01
     */
    private BigDecimal taxRateValue;
    /**
     * 税率码
     */
    private String taxRateCode;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 审核标识：null、仅更新数据 0、拒绝 1、通过
     */
    private Boolean auditFlag;

    /**
     * 外部申请id
     */
    private Long outerApplicationId;

    /**
     * 销售属性值
     */
    private List<ProductsPropertyValueInput> saleValueList;

    /**
     * sku信息集合
     */
    private List<InventoryUpdateInput> skuList;

    /**
     * 关键属性值
     */
    private List<ProductsPropertyValueInput> keyValueList;

    /**
     * 销售属性值
     */
    private List<ProductsPropertyInput> salePropertyList;
}
