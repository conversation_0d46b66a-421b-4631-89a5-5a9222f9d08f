package net.summerfarm.manage.application.inbound.provider.order.converter;

import com.aliyun.odps.Volume;
import net.summerfarm.client.resp.order.OrderDeliveryPlanDetailResp;
import net.summerfarm.client.resp.order.OrderItemResp;
import net.summerfarm.client.resp.order.TimingOrderNoFreezeResp;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanItemFlatObject;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 转换类<br/>
 * date: 2024/12/31 13:55<br/>
 *
 * <AUTHOR> />
 */
public class OrdersConverter {

    public static OrderDeliveryPlanDetailResp flat2OrderDeliveryPlanDetailResp(OrderDeliveryPlanFlatObject flatObject) {
        if(flatObject == null){
            return null;
        }
        OrderDeliveryPlanDetailResp resp = new OrderDeliveryPlanDetailResp();

        resp.setOrderId(flatObject.getOrderId());
        resp.setDpId(flatObject.getDpId());
        resp.setOrderNo(flatObject.getOrderNo());
        resp.setMname(flatObject.getMname());
        resp.setMsize(flatObject.getMsize());
        resp.setMId(flatObject.getMId());
        resp.setContactName(flatObject.getContactName());
        resp.setDeliveryTime(flatObject.getDeliveryTime());
        resp.setStoreNo(flatObject.getStoreNo());
        resp.setContactId(flatObject.getContactId());
        resp.setContactPhone(flatObject.getContactPhone());
        resp.setContactAddress(flatObject.getContactAddress());
        resp.setTimeFrame(flatObject.getTimeFrame());
        resp.setTotalPrice(flatObject.getTotalPrice());
        resp.setDeliveryFee(flatObject.getDeliveryFee());
        resp.setOutTimesFee(flatObject.getOutTimesFee());
        resp.setOrderRemark(flatObject.getOrderRemark());
        resp.setOrderTime(flatObject.getOrderTime());
        resp.setBrandName(flatObject.getBrandName());
        resp.setBigCustomerId(flatObject.getBigCustomerId());
        List<OrderDeliveryPlanItemFlatObject> itemFlatObjects = flatObject.getOrderDeliveryPlanItemFlatObjectList();
        if(!CollectionUtils.isEmpty(itemFlatObjects)){
            resp.setOrderItemList(itemFlatObjects.stream().map(OrdersConverter::flat2OrderItemResp).collect(Collectors.toList()));
        }
        return resp;
    }

    public static OrderItemResp flat2OrderItemResp(OrderDeliveryPlanItemFlatObject flatObject) {
        if(flatObject == null){
            return null;
        }
        OrderItemResp resp = new OrderItemResp();
        resp.setId(flatObject.getId());
        resp.setPdName(flatObject.getPdName());
        resp.setSku(flatObject.getSku());
        resp.setWeight(flatObject.getWeight());
        resp.setMaturity(flatObject.getMaturity());
        resp.setOrderNo(flatObject.getOrderNo());
        resp.setAmount(flatObject.getAmount());
        resp.setPrice(flatObject.getPrice());
        resp.setOriginalPrice(flatObject.getOriginalPrice());
        resp.setPicturePath(flatObject.getPicturePath());
        resp.setAddTime(flatObject.getAddTime());
        resp.setStorageLocation(flatObject.getStorageLocation());
        resp.setStatus(flatObject.getStatus());
        resp.setVolume(flatObject.getVolume());
        resp.setWeightNum(flatObject.getWeightNum());
        resp.setProductType(flatObject.getProductType());
        resp.setSkuName(flatObject.getSkuName());

        return resp;
    }
}
