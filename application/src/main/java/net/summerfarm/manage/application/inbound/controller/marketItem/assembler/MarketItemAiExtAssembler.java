package net.summerfarm.manage.application.inbound.controller.marketItem.assembler;


import net.summerfarm.manage.application.inbound.controller.marketItem.vo.MarketItemAiExtVO;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.query.MarketItemAiExtQueryInput;
import net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;

/**
 *
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
public class MarketItemAiExtAssembler {

    private MarketItemAiExtAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static MarketItemAiExtQueryParam toMarketItemAiExtQueryParam(MarketItemAiExtQueryInput marketItemAiExtQueryInput) {
        if (marketItemAiExtQueryInput == null) {
            return null;
        }
        MarketItemAiExtQueryParam marketItemAiExtQueryParam = new MarketItemAiExtQueryParam();
        marketItemAiExtQueryParam.setId(marketItemAiExtQueryInput.getId());
        marketItemAiExtQueryParam.setCreateTime(marketItemAiExtQueryInput.getCreateTime());
        marketItemAiExtQueryParam.setUpdateTime(marketItemAiExtQueryInput.getUpdateTime());
        marketItemAiExtQueryParam.setTenantId(marketItemAiExtQueryInput.getTenantId());
        marketItemAiExtQueryParam.setSku(marketItemAiExtQueryInput.getSku());
        marketItemAiExtQueryParam.setPdId(marketItemAiExtQueryInput.getPdId());
        marketItemAiExtQueryParam.setExtType(marketItemAiExtQueryInput.getExtType());
        marketItemAiExtQueryParam.setExtValue(marketItemAiExtQueryInput.getExtValue());
        marketItemAiExtQueryParam.setPageIndex(marketItemAiExtQueryInput.getPageIndex());
        marketItemAiExtQueryParam.setPageSize(marketItemAiExtQueryInput.getPageSize());
        return marketItemAiExtQueryParam;
    }





    public static MarketItemAiExtCommandParam buildCreateParam(MarketItemAiExtCommandInput marketItemAiExtCommandInput) {
        if (marketItemAiExtCommandInput == null) {
            return null;
        }
        MarketItemAiExtCommandParam marketItemAiExtCommandParam = new MarketItemAiExtCommandParam();
        marketItemAiExtCommandParam.setId(marketItemAiExtCommandInput.getId());
        marketItemAiExtCommandParam.setCreateTime(marketItemAiExtCommandInput.getCreateTime());
        marketItemAiExtCommandParam.setUpdateTime(marketItemAiExtCommandInput.getUpdateTime());
        marketItemAiExtCommandParam.setTenantId(marketItemAiExtCommandInput.getTenantId());
        marketItemAiExtCommandParam.setSku(marketItemAiExtCommandInput.getSku());
        marketItemAiExtCommandParam.setPdId(marketItemAiExtCommandInput.getPdId());
        marketItemAiExtCommandParam.setExtType(marketItemAiExtCommandInput.getExtType());
        marketItemAiExtCommandParam.setExtValue(marketItemAiExtCommandInput.getExtValue());
        return marketItemAiExtCommandParam;
    }


    public static MarketItemAiExtCommandParam buildUpdateParam(MarketItemAiExtCommandInput marketItemAiExtCommandInput) {
        if (marketItemAiExtCommandInput == null) {
            return null;
        }
        MarketItemAiExtCommandParam marketItemAiExtCommandParam = new MarketItemAiExtCommandParam();
        marketItemAiExtCommandParam.setId(marketItemAiExtCommandInput.getId());
        marketItemAiExtCommandParam.setCreateTime(marketItemAiExtCommandInput.getCreateTime());
        marketItemAiExtCommandParam.setUpdateTime(marketItemAiExtCommandInput.getUpdateTime());
        marketItemAiExtCommandParam.setTenantId(marketItemAiExtCommandInput.getTenantId());
        marketItemAiExtCommandParam.setSku(marketItemAiExtCommandInput.getSku());
        marketItemAiExtCommandParam.setPdId(marketItemAiExtCommandInput.getPdId());
        marketItemAiExtCommandParam.setExtType(marketItemAiExtCommandInput.getExtType());
        marketItemAiExtCommandParam.setExtValue(marketItemAiExtCommandInput.getExtValue());
        return marketItemAiExtCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<MarketItemAiExtVO> toMarketItemAiExtVOList(List<MarketItemAiExtEntity> marketItemAiExtEntityList) {
        if (marketItemAiExtEntityList == null) {
            return Collections.emptyList();
        }
        List<MarketItemAiExtVO> marketItemAiExtVOList = new ArrayList<>();
        for (MarketItemAiExtEntity marketItemAiExtEntity : marketItemAiExtEntityList) {
            marketItemAiExtVOList.add(toMarketItemAiExtVO(marketItemAiExtEntity));
        }
        return marketItemAiExtVOList;
}


   public static MarketItemAiExtVO toMarketItemAiExtVO(MarketItemAiExtEntity marketItemAiExtEntity) {
       if (marketItemAiExtEntity == null) {
            return null;
       }
       MarketItemAiExtVO marketItemAiExtVO = new MarketItemAiExtVO();
       marketItemAiExtVO.setId(marketItemAiExtEntity.getId());
       marketItemAiExtVO.setCreateTime(marketItemAiExtEntity.getCreateTime());
       marketItemAiExtVO.setUpdateTime(marketItemAiExtEntity.getUpdateTime());
       marketItemAiExtVO.setTenantId(marketItemAiExtEntity.getTenantId());
       marketItemAiExtVO.setSku(marketItemAiExtEntity.getSku());
       marketItemAiExtVO.setPdId(marketItemAiExtEntity.getPdId());
       marketItemAiExtVO.setExtType(marketItemAiExtEntity.getExtType());
       marketItemAiExtVO.setExtValue(marketItemAiExtEntity.getExtValue());
       return marketItemAiExtVO;
   }

}
