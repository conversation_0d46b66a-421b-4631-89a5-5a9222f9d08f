package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

/**
 * @Description
 * @Date 2025/4/3 16:17
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PendingAssociationProductQueryInput extends BasePageInput {

    /**
     * 三级类目id
     */
    private Long categoryId;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

}
