package net.summerfarm.manage.application.inbound.controller.merchant.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
@Data
public class MerchantAccountTransferCommandInput implements Serializable{

	/**
	 * 主门店id
	 */
	@NotNull
	private Long mId;
	/**
	 * 被迁移对么门店
	 */
	@NotNull
	private List<Long> transferMIds;


}