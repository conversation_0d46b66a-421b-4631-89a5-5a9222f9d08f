package net.summerfarm.manage.application.inbound.controller.admin.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-06-19 16:33:43
 * @version 1.0
 *
 */
@Data
public class AdminDataPermissionQueryInput extends BasePageInput implements Serializable{
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private Integer adminId;

	/**
	 * 
	 */
	private String permissionValue;

	/**
	 * 
	 */
	private String permissionName;

	/**
	 * 
	 */
	private LocalDateTime addtime;

	/**
	 * 
	 */
	private String type;



}