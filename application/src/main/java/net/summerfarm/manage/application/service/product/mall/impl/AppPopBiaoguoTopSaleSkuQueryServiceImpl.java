package net.summerfarm.manage.application.service.product.mall.impl;


import com.google.common.collect.Lists;
import net.summerfarm.manage.application.inbound.controller.product.handler.ExternalProductHandler;
import net.summerfarm.manage.application.service.product.handler.ExternalGoodsPropDetailParser;
import net.summerfarm.manage.application.service.product.mall.AppPopBiaoguoTopSaleSkuQueryService;
import net.summerfarm.manage.common.enums.products.ExternalProductMappingEnum;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.domain.product.entity.ExternalGoodsPropDetailEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;
import net.summerfarm.manage.domain.product.repository.AppPopBiaoguoProductsDfQueryRepository;
import net.summerfarm.manage.domain.product.repository.AppPopBiaoguoTopSaleSkuQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoTopSaleSkuQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopBiaoguoTopSaleSkuAssembler;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.repository.ExternalProductMappingQueryRepository;
import net.summerfarm.manage.facade.goods.PopBuyerFacade;
import net.summerfarm.manage.facade.goods.dto.PopBuyerInfoDTO;
import net.summerfarm.util.ExceptionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminId;

/**
*
* <AUTHOR>
* @date 2024-11-18 15:55:40
* @version 1.0
*
*/
@Service
public class AppPopBiaoguoTopSaleSkuQueryServiceImpl implements AppPopBiaoguoTopSaleSkuQueryService {

    @Resource
    private AppPopBiaoguoTopSaleSkuQueryRepository appPopBiaoguoTopSaleSkuQueryRepository;
    @Resource
    private ExternalProductHandler externalProductHandler;
    @Resource
    private ExternalProductMappingQueryRepository externalProductMappingQueryRepository;
    @Resource
    private AppPopBiaoguoProductsDfQueryRepository appPopBiaoguoProductsDfQueryRepository;

    @Override
    public PageInfo<AppPopBiaoguoTopSaleSkuEntity> getPage(AppPopBiaoguoTopSaleSkuQueryInput input) {
        AppPopBiaoguoTopSaleSkuQueryParam queryParam = AppPopBiaoguoTopSaleSkuAssembler.toAppPopBiaoguoTopSaleSkuQueryParam(input);
        return appPopBiaoguoTopSaleSkuQueryRepository.getPage(queryParam);
    }

    @Override
    public List<AppPopBiaoguoTopSaleSkuEntity> getList(AppPopBiaoguoTopSaleSkuQueryInput input) {
        AppPopBiaoguoTopSaleSkuQueryParam queryParam = AppPopBiaoguoTopSaleSkuAssembler.toAppPopBiaoguoTopSaleSkuQueryParam(input);
        List<AppPopBiaoguoTopSaleSkuEntity> topSaleSkuEntityList = appPopBiaoguoTopSaleSkuQueryRepository.selectByCondition(queryParam);
        if (CollectionUtils.isEmpty(topSaleSkuEntityList)) {
            return Lists.newArrayList();
        }
        if (input.isOnlyUnMapped()) {
            // 过滤已被绑定的sku
            List<String> externalSkuList = externalProductMappingQueryRepository.getListExternalValueList(ExternalProductMappingEnum.SKU.getValue());
            return topSaleSkuEntityList.stream().filter(topSaleSku -> !externalSkuList.contains(topSaleSku.getSkuCode())).collect(Collectors.toList());
        }
        return topSaleSkuEntityList;
    }

    @Override
    public List<AppPopBiaoguoCategoryEntity> getCategoryList(AppPopBiaoguoTopSaleSkuQueryInput input) {
        AppPopBiaoguoTopSaleSkuQueryParam queryParam = AppPopBiaoguoTopSaleSkuAssembler.toAppPopBiaoguoTopSaleSkuQueryParam(input);
        // 过滤已被绑定的sku
        List<String> externalSkuList = externalProductMappingQueryRepository.getListExternalValueList(ExternalProductMappingEnum.SKU.getValue());
        if (CollectionUtils.isNotEmpty(externalSkuList)) {
            queryParam.setNotInSkuCodeList(externalSkuList);
        }
        return appPopBiaoguoTopSaleSkuQueryRepository.selectCategory2List(queryParam);
    }

    @Override
    public AppPopBiaoguoTopSaleSkuEntity getDetail(Long id){
        return appPopBiaoguoTopSaleSkuQueryRepository.selectById(id);
    }

    @Override
    public AppPopBiaoguoTopSaleSkuEntity getOne(AppPopBiaoguoTopSaleSkuQueryInput input) {
        AppPopBiaoguoTopSaleSkuQueryParam queryParam = AppPopBiaoguoTopSaleSkuAssembler.toAppPopBiaoguoTopSaleSkuQueryParam(input);
        List<AppPopBiaoguoTopSaleSkuEntity> appPopBiaoguoTopSaleSkuEntityList = appPopBiaoguoTopSaleSkuQueryRepository.selectByCondition(queryParam);
        AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity = CollectionUtils.isNotEmpty(appPopBiaoguoTopSaleSkuEntityList) ? appPopBiaoguoTopSaleSkuEntityList.get(0) : null;
        if (Objects.isNull(appPopBiaoguoTopSaleSkuEntity)) {
            return null;
        }
        AppPopBiaoguoProductsDfQueryParam productsDfQueryParam = new AppPopBiaoguoProductsDfQueryParam();
        productsDfQueryParam.setDs(input.getDs());
        productsDfQueryParam.setSkuCode(appPopBiaoguoTopSaleSkuEntity.getSkuCode());
        List<AppPopBiaoguoProductsDfEntity> appPopBiaoguoProductsDfEntityList = appPopBiaoguoProductsDfQueryRepository.selectByCondition(productsDfQueryParam);
        if (CollectionUtils.isNotEmpty(appPopBiaoguoProductsDfEntityList)
                && Objects.nonNull(appPopBiaoguoProductsDfEntityList.get(0))) {
            AppPopBiaoguoProductsDfEntity productsDfEntity = appPopBiaoguoProductsDfEntityList.get(0);
            ExternalGoodsPropDetailEntity externalGoodsPropDetailEntity = ExternalGoodsPropDetailParser.parseGoodsPropDetail(productsDfEntity.getGoodsPropDetailList());
            if (Objects.nonNull(externalGoodsPropDetailEntity)) {
                // 级别、产地、果规
                appPopBiaoguoTopSaleSkuEntity.setOrigin(externalGoodsPropDetailEntity.getOrigin());
                appPopBiaoguoTopSaleSkuEntity.setLevel(externalGoodsPropDetailEntity.getLevel());
                appPopBiaoguoTopSaleSkuEntity.setFruitSize(externalGoodsPropDetailEntity.getFruitSize());
            }

        }

        appPopBiaoguoTopSaleSkuEntity.setTopMatchedXianmuSkuEntityList(externalProductHandler.parseTopMatchedXmSkuEntity(appPopBiaoguoTopSaleSkuEntity));
        return appPopBiaoguoTopSaleSkuEntity;
    }
}