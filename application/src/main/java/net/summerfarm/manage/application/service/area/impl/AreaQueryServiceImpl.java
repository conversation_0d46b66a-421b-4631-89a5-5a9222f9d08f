package net.summerfarm.manage.application.service.area.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.manage.application.inbound.controller.area.input.LargeAreaQueryInput;
import net.summerfarm.manage.application.inbound.controller.area.vo.AreaVO;
import net.summerfarm.manage.application.inbound.controller.area.vo.LargeAreaWithSubAreaVO;
import net.summerfarm.manage.application.service.area.AreaQueryService;
import net.summerfarm.manage.application.service.area.converter.AreaConverter;
import net.summerfarm.manage.application.service.area.dto.AreaSimpleDTO;
import net.summerfarm.manage.common.util.PageUtils;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.entity.LargeArea;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.area.service.AreaService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @author: <EMAIL>
 * @create: 2023/12/8
 */
@Slf4j
@Service
public class AreaQueryServiceImpl implements AreaQueryService {

    @Resource
    private AreaService areaService;

    @Resource
    private AreaQueryRepository areaQueryRepository;

    @Override
    public List<AreaSimpleDTO> batchQueryByAreaNos(List<Integer> areaNos) {
        List<AreaSimpleDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(areaNos)) {
            return list;
        }
        List<AreaSimpleEntity> entityList = areaService.batchQueryByAreaNos(areaNos);
        list = AreaConverter.toAreaSimpleDTOList(entityList);
        return list;
    }

    @Override
    public List<AreaSimpleDTO> batchQueryByLargeAreaNos(List<Integer> largeAreaNos) {
        List<AreaSimpleDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(largeAreaNos)) {
            return list;
        }
        List<AreaSimpleEntity> entityList = areaService.batchQueryByLargeAreaNos(largeAreaNos);
        list = AreaConverter.toAreaSimpleDTOList(entityList);
        return list;
    }

    @Override
    public PageInfo<LargeAreaWithSubAreaVO> queryAllLargeAreas(LargeAreaQueryInput input) {
        PageInfo<LargeArea> largeAreaList = PageUtils.startPage(input.getPageIndex(), input.getPageSize(),
          () -> areaQueryRepository.queryAllLargeArea(input.getStatus()));
        if (null == largeAreaList || CollectionUtils.isEmpty(largeAreaList.getList())) {
            return PageInfo.of(Collections.EMPTY_LIST);
        }
        Set<Integer> largeAreaNos = largeAreaList.getList().stream().map(LargeArea::getLargeAreaNo)
          .collect(Collectors.toSet());
        List<Area> areaList = areaQueryRepository.batchQueryAreaEntitiesByLargeAreaNos(largeAreaNos);
        List<LargeAreaWithSubAreaVO> withSubAreaVOList = AreaConverter.toLargeAreaVOList(largeAreaList.getList());
        Map<Integer, List<AreaVO>> areaVOListMap = areaList.stream().map(AreaConverter::convertToAreaVO)
          .collect(Collectors.groupingBy(AreaVO::getLargeAreaNo));
        withSubAreaVOList.stream().forEach(largeAreaWithSubAreaVO -> largeAreaWithSubAreaVO.setAreaList(
          areaVOListMap.get(largeAreaWithSubAreaVO.getLargeAreaNo())));
        return PageInfo.of(withSubAreaVOList);
    }

    @Override
    public List<AreaSimpleDTO> batchQueryLargeAreaInfoByLargeAreaNos(List<Integer> largeAreaNos) {
        List<AreaSimpleDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(largeAreaNos)) {
            return list;
        }
        List<AreaSimpleEntity> entityList = areaQueryRepository.batchQueryLargeAreaInfoByLargeAreaNos(largeAreaNos);
        list = AreaConverter.toAreaSimpleDTOList(entityList);
        return list;
    }
}
