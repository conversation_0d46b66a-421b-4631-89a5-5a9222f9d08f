package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.product.mall.ExternalProductMappingCommandService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.enums.products.ExternalProductMappingEnum;
import net.summerfarm.manage.common.enums.products.ProductsEnum;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;
import net.summerfarm.manage.domain.product.repository.*;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 定时清理失效POP绑定关系
 *  绑定关系清理逻辑：
 * 	    1、鲜沐sku被删除
 * 	    2、鲜沐sku在所有区域均下架
 * 	    3、外部sku失效(基础表无数据，注意未同步的情况)
 * @Date 2024/12/18 11:52
 * @<AUTHOR>
 */
@Component
@Slf4j
public class ExternalProductMappingClearProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private ExternalProductMappingQueryRepository externalProductMappingQueryRepository;
    @Resource
    private InventoryQueryRepository inventoryQueryRepository;
    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;
    @Resource
    private AppPopBiaoguoProductsDfQueryRepository appPopBiaoguoProductsDfQueryRepository;
    @Resource
    private ExternalProductMappingCommandRepository externalProductMappingCommandRepository;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    public static final String operateTypeClear = "外部品关系解绑";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        Integer pageIndex = 1;
        Integer pageSize = 50;
        boolean hasNextPage;

        // 前置校验外部基础数据是否生成，如果数据未同步，则告警
        AppPopBiaoguoProductsDfQueryParam appPopBiaoguoProductsDfQueryParam = new AppPopBiaoguoProductsDfQueryParam();
        appPopBiaoguoProductsDfQueryParam.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int externalProductCount = appPopBiaoguoProductsDfQueryRepository.count(appPopBiaoguoProductsDfQueryParam);
        if (externalProductCount < nacosPropertiesHolder.getExternalProductCount()) {
            log.error("外部品基础数据未生成或存在数据缺失，请及时确认 表名：app_pop_biaoguo_products_df ds:{} externalProductCount:{} \n", appPopBiaoguoProductsDfQueryParam.getDs(), externalProductCount);
            return new ProcessResult(Boolean.TRUE);
        }
        List<ExternalProductMappingRecordEntity> needClearMappingList = Lists.newArrayList();
        do {
            // 查询外部品映射关系
            ExternalProductMappingQueryParam queryParam = new ExternalProductMappingQueryParam();
            queryParam.setType(ExternalProductMappingEnum.SKU.getValue());
            queryParam.setPageIndex(pageIndex);
            queryParam.setPageSize(pageSize);
            PageInfo<ExternalProductMappingEntity> pageInfo = externalProductMappingQueryRepository.getPage(queryParam);
            if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
                break;
            }
            List<ExternalProductMappingEntity> externalProductMappingEntityList = pageInfo.getList();
            List<String> xmSkuList = externalProductMappingEntityList.stream()
                    .map(ExternalProductMappingEntity::getInternalValue).distinct().collect(Collectors.toList());
            List<String> externalSkuList = externalProductMappingEntityList.stream()
                    .map(ExternalProductMappingEntity::getExternalValue).distinct().collect(Collectors.toList());
            // 鲜沐商品信息查询
            List<InventoryEntity> inventoryEntityList = inventoryQueryRepository.listBySkus(xmSkuList);
            Map<String, InventoryEntity> inventoryEntityMap = inventoryEntityList.stream().collect(Collectors.toMap(InventoryEntity::getSku, Function.identity(), (a, b) -> a));
            // 鲜沐商品运营服务区信息查询
            List<AreaSkuEntity> areaSkuEntityList = areaSkuQueryRepository.queryListSkuPrice(xmSkuList, null, null);
            Map<String, List<AreaSkuEntity>> areaSkuMap = areaSkuEntityList.stream().collect(Collectors.groupingBy(AreaSkuEntity::getSku));
            // 外部商品查询
            AppPopBiaoguoProductsDfQueryParam productsDfQueryParam = new AppPopBiaoguoProductsDfQueryParam();
            productsDfQueryParam.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            productsDfQueryParam.setSkuCodeList(externalSkuList);
            List<AppPopBiaoguoProductsDfEntity> productsDfEntityList = appPopBiaoguoProductsDfQueryRepository.selectByCondition(productsDfQueryParam);
            Map<String, AppPopBiaoguoProductsDfEntity> productsDfEntityMap = productsDfEntityList.stream().collect(Collectors.toMap(AppPopBiaoguoProductsDfEntity::getSkuCode, Function.identity(), (a, b) -> a));

            externalProductMappingEntityList.forEach(externalProductMappingEntity -> {
                ExternalProductMappingRecordEntity recordEntity = ExternalProductMappingRecordEntity.builder()
                        .id(externalProductMappingEntity.getId())
                        .internalValue(externalProductMappingEntity.getInternalValue())
                        .externalValue(externalProductMappingEntity.getExternalValue())
                        .type(externalProductMappingEntity.getType()).build();

                String xmSkuCode = externalProductMappingEntity.getInternalValue();
                String externalSkuCode = externalProductMappingEntity.getExternalValue();
                InventoryEntity inventoryEntity = inventoryEntityMap.get(xmSkuCode);
                // 鲜沐商品被删除
                if (Objects.isNull(inventoryEntity)) {
                    recordEntity.setOperateType(operateTypeClear);
                    recordEntity.setOperateReason("鲜沐sku被物理删除");
                    needClearMappingList.add(recordEntity);
                    return;
                }
                // 鲜沐商品非有效状态
                if (!Objects.equals(inventoryEntity.getOutdated(), ProductsEnum.Outdated.VALID.getCode())) {
                    recordEntity.setOperateType(operateTypeClear);
                    recordEntity.setOperateReason("鲜沐sku非有效状态");
                    needClearMappingList.add(recordEntity);
                    return;
                }
                // 鲜沐商品未建立运营服务区
                List<AreaSkuEntity> areaSkuList = areaSkuMap.get(xmSkuCode);
                if (CollectionUtils.isEmpty(areaSkuList)) {
                    recordEntity.setOperateType(operateTypeClear);
                    recordEntity.setOperateReason("鲜沐sku未建立相关运营服务区");
                    needClearMappingList.add(recordEntity);
                    return;
                }
                // 鲜沐商品下架
                if (areaSkuList.stream().noneMatch(AreaSkuEntity::getOnSale)) {
                    recordEntity.setOperateType(operateTypeClear);
                    recordEntity.setOperateReason("鲜沐sku已在所有运营服务区下架");
                    needClearMappingList.add(recordEntity);
                    return;
                }
                // 外部商品失效
                AppPopBiaoguoProductsDfEntity appPopBiaoguoProductsDfEntity = productsDfEntityMap.get(externalSkuCode);
                if (Objects.isNull(appPopBiaoguoProductsDfEntity)) {
                    recordEntity.setOperateType(operateTypeClear);
                    recordEntity.setOperateReason("外部商品失效");
                    needClearMappingList.add(recordEntity);
                }

            });

            hasNextPage = pageInfo.isHasNextPage();
            pageIndex++;
        } while (hasNextPage);
        if (CollectionUtils.isEmpty(needClearMappingList)) {
            log.info("无需要清理的外部品映射关系");
            return new ProcessResult(Boolean.TRUE);
        }
        needClearMappingList.forEach(clearMapping -> {
            if (Objects.isNull(clearMapping) || Objects.isNull(clearMapping.getId())) {
                return;
            }
            externalProductMappingCommandRepository.remove(clearMapping.getId());
            // 暂先记录解绑日志，后续根据情况再决定是否增加日志操作表持久化
            log.info("解绑外部品映射关系成功，解绑信息 clearMapping：{}", JSON.toJSONString(clearMapping));
            }
        );
        return new ProcessResult(Boolean.TRUE);
    }
}
