package net.summerfarm.manage.application.inbound.controller.customization;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.customization.input.CustomizationRequestCommandInput;
import net.summerfarm.manage.application.inbound.controller.customization.input.CustomizationRequestQueryInput;
import net.summerfarm.manage.application.inbound.controller.customization.vo.CustomizationRequestVO;
import net.summerfarm.manage.application.service.customization.CustomizationRequestService;
import net.summerfarm.manage.common.constants.Global;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 定制需求控制器
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/customization-request")
public class CustomizationRequestController {

    @Resource
    private CustomizationRequestService customizationRequestService;


    /**
     * 分页查询定制需求列表
     *
     * @param input 查询条件
     * @return 分页结果
     */
    @RequestMapping(value = "/query/page", method = RequestMethod.POST)
    @RequiresPermissions(value = {"customization:select", Global.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<CustomizationRequestVO>> getPage(@RequestBody CustomizationRequestQueryInput input) {
        return CommonResult.ok(customizationRequestService.getPage(input));
    }

    /**
     * 根据ID查询定制需求详情
     *
     * @param input 查询条件
     * @return 定制需求详情
     */
    @RequestMapping(value = "/query/detail", method = RequestMethod.POST)
    @RequiresPermissions(value = {"customization:select", Global.SA}, logical = Logical.OR)
    public CommonResult<CustomizationRequestVO> getDetail(@RequestBody CustomizationRequestQueryInput input) {
        return CommonResult.ok(customizationRequestService.getDetail(input.getId()));
    }

    /**
     * 编辑备注or设计效果地址
     *
     * @param input 提交设计参数
     * @return 更新结果
     */
    @RequestMapping(value = "/submit-design", method = RequestMethod.POST)
    @RequiresPermissions(value = {"customization:update", Global.SA}, logical = Logical.OR)
    public CommonResult<Void> submitDesign(@Valid @RequestBody CustomizationRequestCommandInput input) {
        customizationRequestService.submitDesign(input.getId(), input.getDesignImage(), input.getDesignerRemark());
        return CommonResult.ok();
    }

}
