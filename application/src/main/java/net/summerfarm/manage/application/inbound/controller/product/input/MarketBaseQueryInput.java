package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.util.List;

/**
 * 商品相关查询类
 */
@Data
public class MarketBaseQueryInput extends BasePageInput implements Serializable {
    /**
     * spu name
     */
    private String spuTitleLike;
    /**
     * sku
     */
    private String itemCodeLike;
    /**
     * sku
     */
    private String itemCode;
    /**
     *  类型 0 自营 1 代仓
     */
    private Integer characters;
    /**
     * 买手ID
     */
    private Long buyerId;
    /**
     * 后台类目id
     */
    private List<Long> categoryIds;
    /**
     * 大客户id
     */
    private Long adminId;

    /**
     * pdId
     */
    private Long pdId;
}
