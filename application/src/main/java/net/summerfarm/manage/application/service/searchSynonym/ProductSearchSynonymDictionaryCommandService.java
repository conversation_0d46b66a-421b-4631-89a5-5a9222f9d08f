package net.summerfarm.manage.application.service.searchSynonym;

import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.command.ProductSearchSynonymDictionaryCommandInput;


/**
 * @date 2025-04-24 14:53:58
 * @version 1.0
 */
public interface ProductSearchSynonymDictionaryCommandService {

    /**
     * @description: 新增
     * @return ProductSearchSynonymDictionaryEntity
     **/
    ProductSearchSynonymDictionaryEntity insert(ProductSearchSynonymDictionaryCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(ProductSearchSynonymDictionaryCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Integer id);

}