package net.summerfarm.manage.application.inbound.controller.searchSynonym;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.assembler.ProductSearchSynonymDictionaryAssembler;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.command.ProductSearchSynonymDictionaryCommandInput;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query.ProductSearchSynonymDictionaryQueryInput;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.vo.ProductSearchSynonymDictionaryVO;
import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryCommandService;
import net.summerfarm.manage.application.service.searchSynonym.ProductSearchSynonymDictionaryQueryService;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @Title ES用到的同义词表，每一行是一组同义词，比如："葡萄,巨峰,巨峰葡萄,夏黑,夏黑葡萄,青提,晴王青提,阳光玫瑰"
 * @Description ES用到的同义词表，每一行是一组同义词，比如："葡萄,巨峰,巨峰葡萄,夏黑,夏黑葡萄,青提,晴王青提,阳光玫瑰"功能模块
 * @date 2025-04-24 14:53:58
 */
@RestController
@RequestMapping(value = "/synonyms")
public class ProductSearchSynonymDictionaryController {

    @Autowired
    private ProductSearchSynonymDictionaryCommandService productSearchSynonymDictionaryCommandService;
    @Autowired
    private ProductSearchSynonymDictionaryQueryService productSearchSynonymDictionaryQueryService;


    /**
     * ES用到的同义词表，每一行是一组同义词，比如："葡萄,巨峰,巨峰葡萄,夏黑,夏黑葡萄,青提,晴王青提,阳光玫瑰"列表
     *
     * @return ProductSearchSynonymDictionaryVO
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<ProductSearchSynonymDictionaryVO>> getPage(@RequestBody ProductSearchSynonymDictionaryQueryInput input) {
        PageInfo<ProductSearchSynonymDictionaryEntity> page = productSearchSynonymDictionaryQueryService.getPage(input);
        return CommonResult.ok(PageInfoConverter.toPageResp(page, ProductSearchSynonymDictionaryAssembler::toProductSearchSynonymDictionaryVO));
    }

    /**
     * 下载所有同义词记录为txt文件
     *
     * @return ResponseEntity<String> 包含同义词文本内容和下载头信息
     */
    @GetMapping(value = "/download/synonyms.txt", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> downloadSynonymsAsTxt() {
        // 如果没有，你需要先在 Service 层添加相应的方法
        ProductSearchSynonymDictionaryQueryInput input = new ProductSearchSynonymDictionaryQueryInput();
        input.setPageIndex(1);
        input.setPageSize(20000); // 这里限制一下大小，如果太大了，则只返回20000条记录
        PageInfo<ProductSearchSynonymDictionaryEntity> allSynonyms = productSearchSynonymDictionaryQueryService.getPage(input);

        String textContent = "无";
        if (allSynonyms != null && CollectionUtils.isNotEmpty(allSynonyms.getList())) {
			// 格式化为文本，每行一个同义词组
            textContent = allSynonyms.getList().stream()
                    // 假设实体类有 getSynonyms() 方法返回 "葡萄,巨峰,..." 格式的字符串
                    .map(ProductSearchSynonymDictionaryEntity::getSynonymTerms)
                    .filter(s -> s != null && !s.trim().isEmpty()) // 过滤空行
                    .collect(Collectors.joining("\n"));
        }


        // 设置HTTP头，指定为附件下载和文件名
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"synonyms.txt\"");
        // 明确指定UTF-8编码，防止中文乱码
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE + "; charset=UTF-8");

        return new ResponseEntity<>(textContent, headers, HttpStatus.OK);
    }

    /**
     * 获取详情
     *
     * @return ProductSearchSynonymDictionaryVO
     */
    @PostMapping(value = "/query/detail")
    public CommonResult<ProductSearchSynonymDictionaryVO> detail(@RequestBody ProductSearchSynonymDictionaryQueryInput input) {
        if (input == null || input.getId() == null) {
            throw new ParamsException("请求参数缺失:缺少id字段");
        }
        return CommonResult.ok(ProductSearchSynonymDictionaryAssembler.toProductSearchSynonymDictionaryVO(productSearchSynonymDictionaryQueryService.getDetail(input.getId())));
    }


    /**
     * 新增
     *
     * @return ProductSearchSynonymDictionaryVO
     */
    @PostMapping(value = "/upsert/insert")
    public CommonResult<ProductSearchSynonymDictionaryVO> insert(@RequestBody ProductSearchSynonymDictionaryCommandInput input) {
        String userName = UserInfoHolder.getAdminName();
        input.setCreatedBy(userName);
        input.setUpdatedBy(userName);
        return CommonResult.ok(ProductSearchSynonymDictionaryAssembler.toProductSearchSynonymDictionaryVO(productSearchSynonymDictionaryCommandService.insert(input)));
    }

    /**
     * 修改
     *
     * @return
     */
    @PostMapping(value = "/upsert/update")
    public CommonResult<Integer> update(@RequestBody ProductSearchSynonymDictionaryCommandInput input) {
        String userName = UserInfoHolder.getAdminName();
        input.setUpdatedBy(userName);
        return CommonResult.ok(productSearchSynonymDictionaryCommandService.update(input));
    }

    /**
     * 删除
     *
     * @return
     */
    @PostMapping(value = "/upsert/delete")
    public CommonResult<Integer> delete(@RequestBody ProductSearchSynonymDictionaryCommandInput input) {
        if (input == null || input.getId() == null) {
            throw new ParamsException("请求参数缺失:缺少id字段");
        }
        String userName = UserInfoHolder.getAdminName();
        input.setUpdatedBy(userName);
        return CommonResult.ok(productSearchSynonymDictionaryCommandService.delete(input.getId()));
    }


}

