package net.summerfarm.manage.infrastructure.model.admin;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-06-18 13:07:30
 * @version 1.0
 *
 */
@Data
public class Invitecode {
	/**
	 * 
	 */
	private Integer inviteId;

	/**
	 * 6位邀请码
	 */
	private String invitecode;

	/**
	 * 地推人员
	 */
	private Integer adminId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 是否可用
	 */
	private Integer status;



}