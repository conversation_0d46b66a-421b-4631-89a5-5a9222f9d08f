package net.summerfarm.manage.infrastructure.repository.major;


import net.summerfarm.manage.infrastructure.model.major.MajorPriceLog;
import net.summerfarm.manage.infrastructure.mapper.major.MajorPriceLogMapper;
import net.summerfarm.manage.infrastructure.converter.major.MajorPriceLogConverter;
import net.summerfarm.manage.domain.major.repository.MajorPriceLogQueryRepository;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-02-19 11:19:11
* @version 1.0
*
*/
@Repository
public class MajorPriceLogQueryRepositoryImpl implements MajorPriceLogQueryRepository {

    @Autowired
    private MajorPriceLogMapper majorPriceLogMapper;


    @Override
    public PageInfo<MajorPriceLogEntity> getPage(MajorPriceLogQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MajorPriceLogEntity> entities = majorPriceLogMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MajorPriceLogEntity selectById(Long id) {
        return MajorPriceLogConverter.toMajorPriceLogEntity(majorPriceLogMapper.selectById(id));
    }


    @Override
    public List<MajorPriceLogEntity> selectByCondition(MajorPriceLogQueryParam param) {
        return MajorPriceLogConverter.toMajorPriceLogEntityList(majorPriceLogMapper.selectByCondition(param));
    }

}