package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.ProductLabelValue;
import net.summerfarm.manage.infrastructure.mapper.product.ProductLabelValueMapper;
import net.summerfarm.manage.infrastructure.converter.product.ProductLabelValueConverter;
import net.summerfarm.manage.domain.product.repository.ProductLabelValueCommandRepository;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductLabelValueCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:46
* @version 1.0
*
*/
@Repository
public class ProductLabelValueCommandRepositoryImpl implements ProductLabelValueCommandRepository {

    @Autowired
    private ProductLabelValueMapper productLabelValueMapper;
    @Override
    public ProductLabelValueEntity insertSelective(ProductLabelValueCommandParam param) {
        ProductLabelValue productLabelValue = ProductLabelValueConverter.toProductLabelValue(param);
        productLabelValueMapper.insertSelective(productLabelValue);
        return ProductLabelValueConverter.toProductLabelValueEntity(productLabelValue);
    }

    @Override
    public int updateSelectiveById(ProductLabelValueCommandParam param){
        return productLabelValueMapper.updateSelectiveById(ProductLabelValueConverter.toProductLabelValue(param));
    }


    @Override
    public int remove(Long id) {
        return productLabelValueMapper.remove(id);
    }
}