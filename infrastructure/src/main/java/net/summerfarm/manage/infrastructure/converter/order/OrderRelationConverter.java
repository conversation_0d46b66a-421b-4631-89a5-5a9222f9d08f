package net.summerfarm.manage.infrastructure.converter.order;

import net.summerfarm.manage.infrastructure.model.order.OrderRelation;
import net.summerfarm.manage.domain.order.entity.OrderRelationEntity;
import net.summerfarm.manage.domain.order.param.command.OrderRelationCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-01-23 16:02:29
 * @version 1.0
 *
 */
public class OrderRelationConverter {

    private OrderRelationConverter() {
        // 无需实现
    }




    public static List<OrderRelationEntity> toOrderRelationEntityList(List<OrderRelation> orderRelationList) {
        if (orderRelationList == null) {
            return Collections.emptyList();
        }
        List<OrderRelationEntity> orderRelationEntityList = new ArrayList<>();
        for (OrderRelation orderRelation : orderRelationList) {
            orderRelationEntityList.add(toOrderRelationEntity(orderRelation));
        }
        return orderRelationEntityList;
}


    public static OrderRelationEntity toOrderRelationEntity(OrderRelation orderRelation) {
        if (orderRelation == null) {
             return null;
        }
        OrderRelationEntity orderRelationEntity = new OrderRelationEntity();
        orderRelationEntity.setId(orderRelation.getId());
        orderRelationEntity.setMasterOrderNo(orderRelation.getMasterOrderNo());
        orderRelationEntity.setOrderNo(orderRelation.getOrderNo());
        orderRelationEntity.setCreateTime(orderRelation.getCreateTime());
        orderRelationEntity.setUpdateTime(orderRelation.getUpdateTime());
        orderRelationEntity.setPrecisionDeliveryFee(orderRelation.getPrecisionDeliveryFee());
        return orderRelationEntity;
    }








    public static OrderRelation toOrderRelation(OrderRelationCommandParam param) {
        if (param == null) {
            return null;
        }
        OrderRelation orderRelation = new OrderRelation();
        orderRelation.setId(param.getId());
        orderRelation.setMasterOrderNo(param.getMasterOrderNo());
        orderRelation.setOrderNo(param.getOrderNo());
        orderRelation.setCreateTime(param.getCreateTime());
        orderRelation.setUpdateTime(param.getUpdateTime());
        orderRelation.setPrecisionDeliveryFee(param.getPrecisionDeliveryFee());
        return orderRelation;
    }
}
