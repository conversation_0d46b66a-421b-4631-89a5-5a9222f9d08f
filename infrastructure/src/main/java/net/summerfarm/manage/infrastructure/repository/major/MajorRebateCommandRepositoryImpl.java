package net.summerfarm.manage.infrastructure.repository.major;

import net.summerfarm.manage.infrastructure.model.major.MajorRebate;
import net.summerfarm.manage.infrastructure.mapper.major.MajorRebateMapper;
import net.summerfarm.manage.infrastructure.converter.major.MajorRebateConverter;
import net.summerfarm.manage.domain.major.repository.MajorRebateCommandRepository;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import net.summerfarm.manage.domain.major.param.command.MajorRebateCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-02-27 15:22:25
* @version 1.0
*
*/
@Repository
public class MajorRebateCommandRepositoryImpl implements MajorRebateCommandRepository {

    @Autowired
    private MajorRebateMapper majorRebateMapper;
    @Override
    public MajorRebateEntity insertSelective(MajorRebateCommandParam param) {
        MajorRebate majorRebate = MajorRebateConverter.toMajorRebate(param);
        majorRebateMapper.insertSelective(majorRebate);
        return MajorRebateConverter.toMajorRebateEntity(majorRebate);
    }

    @Override
    public int updateSelectiveById(MajorRebateCommandParam param){
        return majorRebateMapper.updateSelectiveById(MajorRebateConverter.toMajorRebate(param));
    }


    @Override
    public int remove(Long id) {
        return majorRebateMapper.remove(id);
    }
}