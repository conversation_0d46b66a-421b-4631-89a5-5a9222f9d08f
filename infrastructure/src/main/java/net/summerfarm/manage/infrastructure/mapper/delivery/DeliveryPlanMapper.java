package net.summerfarm.manage.infrastructure.mapper.delivery;

import net.summerfarm.manage.domain.delivery.entity.DeliveryPlanEntity;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;
import net.summerfarm.manage.infrastructure.model.delivery.DeliveryPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-01-18 14:33:12
 * @version 1.0
 *
 */
@Mapper
public interface DeliveryPlanMapper{

    /**
     * @Describe: 查询自动收货订单--当前日期-1天 根据当前日期
     * @param dateTime
     * @param pageIndex
     * @param pageSize
     * @return
     */
    List<DeliveryPlanFlatObject> getAutoConfirmOrder(@Param("dateTime") LocalDate dateTime, @Param("orderNo") String oldOrderNo,
                                                     @Param("pageStart") int pageIndex, @Param("pageSize") int pageSize);

    /**
     * @Describe: 根据订单号查询配送数量
     * @param orderNo
     * @return
     */
    Integer getDeliveryPlanQuantity(String orderNo);


    /**
     * @Describe: 根据订单号查询配送数量
     * @param orderNo
     * @return
     */
    List<DeliveryPlanEntity> getDeliveryPlanByOrderNo(String orderNo);


    /**
     * @Describe: 根据配送ID更新配送信息
     * @param deliveryPlan
     * @return
     */
    int updateInfoById(DeliveryPlan deliveryPlan);

    /**
     * @Describe: 查询需要配送提醒的订单信息
     * @param dateTime
     * @param orderNo
     * @param pageStart
     * @param pageSize
     * @return
     */
    List<DeliveryPlanFlatObject> noticeLists(@Param("deliveryTime") LocalDate dateTime, @Param("orderNo") String orderNo,
                                             @Param("pageStart") int pageStart, @Param("pageSize") int pageSize);

    /**
     * @Describe: 查询省心送订单需要配送提醒的订单信息
     * @param dateTime
     * @param orderNo
     * @param pageStart
     * @param pageSize
     * @return
     */
    List<DeliveryPlanFlatObject> timingNoticeLists(@Param("deliveryTime") LocalDate dateTime, @Param("orderNo") String orderNo,
                                                   @Param("pageStart") int pageStart, @Param("pageSize") int pageSize);

    /**
     * 查询城配仓省心送代销不入仓未冻结Sku信息
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<NoFreezeProxySaleNoWareNoSkuFlatObject> queryTimingOrderNoFreezeProxySaleNoWarehouse(@Param("startDate") LocalDate startDate,
                                                                                              @Param("endDate") LocalDate endDate,
                                                                                              @Param("storeNo") Integer storeNo);

    List<DeliveryPlanFlatObject> getWaitingDeliveryPlanQuantity(@Param("normalOrderNos") List<String> normalOrderNos);

    /**
     * 查询有效的配送计划订单详情信息
     * @param deliveryTime 配送日期
     * @param orderNoList 订单号
     * @return
     */
    List<OrderDeliveryPlanFlatObject> queryValidOrderDeliveryPlanDetail(@Param("deliveryTime")LocalDate deliveryTime, @Param("orderNoList") List<String> orderNoList);

    Integer getDeliveryPlanQuantityById(Integer id);
}

