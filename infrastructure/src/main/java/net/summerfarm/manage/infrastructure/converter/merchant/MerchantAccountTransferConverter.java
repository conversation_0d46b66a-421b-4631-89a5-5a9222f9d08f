package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantAccountTransfer;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantAccountTransferCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
public class MerchantAccountTransferConverter {

    private MerchantAccountTransferConverter() {
        // 无需实现
    }




    public static List<MerchantAccountTransferEntity> toMerchantAccountTransferEntityList(List<MerchantAccountTransfer> merchantAccountTransferList) {
        if (merchantAccountTransferList == null) {
            return Collections.emptyList();
        }
        List<MerchantAccountTransferEntity> merchantAccountTransferEntityList = new ArrayList<>();
        for (MerchantAccountTransfer merchantAccountTransfer : merchantAccountTransferList) {
            merchantAccountTransferEntityList.add(toMerchantAccountTransferEntity(merchantAccountTransfer));
        }
        return merchantAccountTransferEntityList;
}


    public static MerchantAccountTransferEntity toMerchantAccountTransferEntity(MerchantAccountTransfer merchantAccountTransfer) {
        if (merchantAccountTransfer == null) {
             return null;
        }
        MerchantAccountTransferEntity merchantAccountTransferEntity = new MerchantAccountTransferEntity();
        merchantAccountTransferEntity.setId(merchantAccountTransfer.getId());
        merchantAccountTransferEntity.setCreateTime(merchantAccountTransfer.getCreateTime());
        merchantAccountTransferEntity.setUpdateTime(merchantAccountTransfer.getUpdateTime());
        merchantAccountTransferEntity.setMId(merchantAccountTransfer.getMId());
        merchantAccountTransferEntity.setMname(merchantAccountTransfer.getMname());
        merchantAccountTransferEntity.setTransferMId(merchantAccountTransfer.getTransferMId());
        merchantAccountTransferEntity.setOperatorName(merchantAccountTransfer.getOperatorName());
        merchantAccountTransferEntity.setAreaNo(merchantAccountTransfer.getAreaNo());
        merchantAccountTransferEntity.setAreaName(merchantAccountTransfer.getAreaName());
        merchantAccountTransferEntity.setRemark(merchantAccountTransfer.getRemark());
        merchantAccountTransferEntity.setAddr(merchantAccountTransfer.getAddr());
        merchantAccountTransferEntity.setBdName(merchantAccountTransfer.getBdName());
        merchantAccountTransferEntity.setTransferMname(merchantAccountTransfer.getTransferMname());
        merchantAccountTransferEntity.setTransferBdName(merchantAccountTransfer.getTransferBdName());
        merchantAccountTransferEntity.setPhone(merchantAccountTransfer.getPhone());
        merchantAccountTransferEntity.setTransferPhone(merchantAccountTransfer.getTransferPhone());
        return merchantAccountTransferEntity;
    }








    public static MerchantAccountTransfer toMerchantAccountTransfer(MerchantAccountTransferCommandParam param) {
        if (param == null) {
            return null;
        }
        MerchantAccountTransfer merchantAccountTransfer = new MerchantAccountTransfer();
        merchantAccountTransfer.setId(param.getId());
        merchantAccountTransfer.setCreateTime(param.getCreateTime());
        merchantAccountTransfer.setUpdateTime(param.getUpdateTime());
        merchantAccountTransfer.setMId(param.getMId());
        merchantAccountTransfer.setMname(param.getMname());
        merchantAccountTransfer.setTransferMId(param.getTransferMId());
        merchantAccountTransfer.setOperatorName(param.getOperatorName());
        merchantAccountTransfer.setAreaNo(param.getAreaNo());
        merchantAccountTransfer.setAreaName(param.getAreaName());
        merchantAccountTransfer.setRemark(param.getRemark());
        merchantAccountTransfer.setAddr(param.getAddr());
        merchantAccountTransfer.setBdName(param.getBdName());
        merchantAccountTransfer.setTransferMname(param.getTransferMname());
        merchantAccountTransfer.setTransferBdName(param.getTransferBdName());
        merchantAccountTransfer.setPhone(param.getPhone());
        merchantAccountTransfer.setTransferPhone(param.getTransferPhone());
        return merchantAccountTransfer;
    }
}
