package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.ProductsPropertyMapping;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyMappingCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-07 16:18:00
 * @version 1.0
 *
 */
public class ProductsPropertyMappingConverter {

    private ProductsPropertyMappingConverter() {
        // 无需实现
    }




    public static List<ProductsPropertyMappingEntity> toProductsPropertyMappingEntityList(List<ProductsPropertyMapping> productsPropertyMappingList) {
        if (productsPropertyMappingList == null) {
            return Collections.emptyList();
        }
        List<ProductsPropertyMappingEntity> productsPropertyMappingEntityList = new ArrayList<>();
        for (ProductsPropertyMapping productsPropertyMapping : productsPropertyMappingList) {
            productsPropertyMappingEntityList.add(toProductsPropertyMappingEntity(productsPropertyMapping));
        }
        return productsPropertyMappingEntityList;
}


    public static ProductsPropertyMappingEntity toProductsPropertyMappingEntity(ProductsPropertyMapping productsPropertyMapping) {
        if (productsPropertyMapping == null) {
             return null;
        }
        ProductsPropertyMappingEntity productsPropertyMappingEntity = new ProductsPropertyMappingEntity();
        productsPropertyMappingEntity.setId(productsPropertyMapping.getId());
        productsPropertyMappingEntity.setType(productsPropertyMapping.getType());
        productsPropertyMappingEntity.setMappingId(productsPropertyMapping.getMappingId());
        productsPropertyMappingEntity.setProductsPropertyId(productsPropertyMapping.getProductsPropertyId());
        productsPropertyMappingEntity.setCreator(productsPropertyMapping.getCreator());
        productsPropertyMappingEntity.setCreateTime(productsPropertyMapping.getCreateTime());
        return productsPropertyMappingEntity;
    }








    public static ProductsPropertyMapping toProductsPropertyMapping(ProductsPropertyMappingCommandParam param) {
        if (param == null) {
            return null;
        }
        ProductsPropertyMapping productsPropertyMapping = new ProductsPropertyMapping();
        productsPropertyMapping.setId(param.getId());
        productsPropertyMapping.setType(param.getType());
        productsPropertyMapping.setMappingId(param.getMappingId());
        productsPropertyMapping.setProductsPropertyId(param.getProductsPropertyId());
        productsPropertyMapping.setCreator(param.getCreator());
        productsPropertyMapping.setCreateTime(param.getCreateTime());
        return productsPropertyMapping;
    }
}
