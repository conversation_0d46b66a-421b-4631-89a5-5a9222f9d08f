package net.summerfarm.manage.infrastructure.converter.activity;

import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuDetailCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-04-09 15:06:01
 * @version 1.0
 *
 */
public class ActivitySkuDetailConverter {

    private ActivitySkuDetailConverter() {
        // 无需实现
    }




    public static List<ActivitySkuDetailEntity> toActivitySkuDetailEntityList(List<ActivitySkuDetail> activitySkuDetailList) {
        if (activitySkuDetailList == null) {
            return Collections.emptyList();
        }
        List<ActivitySkuDetailEntity> activitySkuDetailEntityList = new ArrayList<>();
        for (ActivitySkuDetail activitySkuDetail : activitySkuDetailList) {
            activitySkuDetailEntityList.add(toActivitySkuDetailEntity(activitySkuDetail));
        }
        return activitySkuDetailEntityList;
}


    public static ActivitySkuDetailEntity toActivitySkuDetailEntity(ActivitySkuDetail activitySkuDetail) {
        if (activitySkuDetail == null) {
             return null;
        }
        ActivitySkuDetailEntity activitySkuDetailEntity = new ActivitySkuDetailEntity();
        activitySkuDetailEntity.setId(activitySkuDetail.getId());
        activitySkuDetailEntity.setItemConfigId(activitySkuDetail.getItemConfigId());
        activitySkuDetailEntity.setSku(activitySkuDetail.getSku());
        activitySkuDetailEntity.setRoundingMode(activitySkuDetail.getRoundingMode());
        activitySkuDetailEntity.setAdjustType(activitySkuDetail.getAdjustType());
        activitySkuDetailEntity.setAmount(activitySkuDetail.getAmount());
        activitySkuDetailEntity.setSort(activitySkuDetail.getSort());
        activitySkuDetailEntity.setPlanQuantity(activitySkuDetail.getPlanQuantity());
        activitySkuDetailEntity.setActualQuantity(activitySkuDetail.getActualQuantity());
        activitySkuDetailEntity.setLockQuantity(activitySkuDetail.getLockQuantity());
        activitySkuDetailEntity.setAccountLimit(activitySkuDetail.getAccountLimit());
        activitySkuDetailEntity.setLimitQuantity(activitySkuDetail.getLimitQuantity());
        activitySkuDetailEntity.setMinSaleNum(activitySkuDetail.getMinSaleNum());
        activitySkuDetailEntity.setSingleDeposit(activitySkuDetail.getSingleDeposit());
        activitySkuDetailEntity.setExpansionRatio(activitySkuDetail.getExpansionRatio());
        activitySkuDetailEntity.setHidePrice(activitySkuDetail.getHidePrice());
        activitySkuDetailEntity.setTimingConfig(activitySkuDetail.getTimingConfig());
        activitySkuDetailEntity.setDelFlag(activitySkuDetail.getDelFlag());
        activitySkuDetailEntity.setCreateTime(activitySkuDetail.getCreateTime());
        activitySkuDetailEntity.setUpdateTime(activitySkuDetail.getUpdateTime());
        activitySkuDetailEntity.setIsSupportTiming(activitySkuDetail.getIsSupportTiming());
        activitySkuDetailEntity.setAutoPrice(activitySkuDetail.getAutoPrice());
        activitySkuDetailEntity.setLadderConfig(activitySkuDetail.getLadderConfig());
        activitySkuDetailEntity.setDiscountLabel(activitySkuDetail.getDiscountLabel());
        return activitySkuDetailEntity;
    }








    public static ActivitySkuDetail toActivitySkuDetail(ActivitySkuDetailCommandParam param) {
        if (param == null) {
            return null;
        }
        ActivitySkuDetail activitySkuDetail = new ActivitySkuDetail();
        activitySkuDetail.setId(param.getId());
        activitySkuDetail.setItemConfigId(param.getItemConfigId());
        activitySkuDetail.setSku(param.getSku());
        activitySkuDetail.setRoundingMode(param.getRoundingMode());
        activitySkuDetail.setAdjustType(param.getAdjustType());
        activitySkuDetail.setAmount(param.getAmount());
        activitySkuDetail.setSort(param.getSort());
        activitySkuDetail.setPlanQuantity(param.getPlanQuantity());
        activitySkuDetail.setActualQuantity(param.getActualQuantity());
        activitySkuDetail.setLockQuantity(param.getLockQuantity());
        activitySkuDetail.setAccountLimit(param.getAccountLimit());
        activitySkuDetail.setLimitQuantity(param.getLimitQuantity());
        activitySkuDetail.setMinSaleNum(param.getMinSaleNum());
        activitySkuDetail.setSingleDeposit(param.getSingleDeposit());
        activitySkuDetail.setExpansionRatio(param.getExpansionRatio());
        activitySkuDetail.setHidePrice(param.getHidePrice());
        activitySkuDetail.setTimingConfig(param.getTimingConfig());
        activitySkuDetail.setDelFlag(param.getDelFlag());
        activitySkuDetail.setCreateTime(param.getCreateTime());
        activitySkuDetail.setUpdateTime(param.getUpdateTime());
        activitySkuDetail.setIsSupportTiming(param.getIsSupportTiming());
        activitySkuDetail.setAutoPrice(param.getAutoPrice());
        activitySkuDetail.setLadderConfig(param.getLadderConfig());
        activitySkuDetail.setDiscountLabel(param.getDiscountLabel());
        return activitySkuDetail;
    }
}
