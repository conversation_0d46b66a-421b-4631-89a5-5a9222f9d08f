package net.summerfarm.manage.infrastructure.mapper.timing;

import net.summerfarm.manage.infrastructure.model.timing.TimingRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 定期送规则 Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface TimingRuleMapper {

    /**
     * 根据区域和SKU查询定期送规则列表
     * 
     * @param areaNo 区域编号
     * @param skus 团购商品SKU
     * @return 定期送规则列表
     */
    List<TimingRule> selectByAreaAndSkus(@Param("areaNo") Integer areaNo, @Param("skus") Set<String> skus);

    /**
     * 批量新增定期送规则
     *
     * @param timingRuleList 定期送规则列表
     * @return 影响行数
     */
    int saveBatch(@Param("list") List<TimingRule> timingRuleList);
}
