package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.ProductsPropertyValue;
import net.summerfarm.manage.infrastructure.mapper.product.ProductsPropertyValueMapper;
import net.summerfarm.manage.infrastructure.converter.product.ProductsPropertyValueConverter;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyValueCommandRepository;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyValueCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
*
* <AUTHOR>
* @date 2024-05-06 16:02:28
* @version 1.0
*
*/
@Repository
public class ProductsPropertyValueCommandRepositoryImpl implements ProductsPropertyValueCommandRepository {

    @Autowired
    private ProductsPropertyValueMapper productsPropertyValueMapper;


    @Override
    public ProductsPropertyValueEntity insertSelective(ProductsPropertyValueCommandParam param) {
        ProductsPropertyValue productsPropertyValue = ProductsPropertyValueConverter.toProductsPropertyValue(param);
        productsPropertyValueMapper.insertSelective(productsPropertyValue);
        return ProductsPropertyValueConverter.toProductsPropertyValueEntity(productsPropertyValue);
    }

    @Override
    public int updateSelectiveById(ProductsPropertyValueCommandParam param){
        return productsPropertyValueMapper.updateSelectiveById(ProductsPropertyValueConverter.toProductsPropertyValue(param));
    }

    @Override
    public void addSalePropertyValue(List<ProductsPropertyValueCommandParam> commandParams) {

    }

    @Override
    public void deleteByPdId(Long pdId) {
        productsPropertyValueMapper.deleteByPdId(pdId);
    }

    @Override
    public void deleteByPdIdAndPropertyIds(Long pdId, Set<Integer> productsPropertyIds) {
        productsPropertyValueMapper.deleteByPdIdAndPropertyIds(pdId, productsPropertyIds);
    }
}