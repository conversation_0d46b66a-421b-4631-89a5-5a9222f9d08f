package net.summerfarm.manage.infrastructure.mapper.order;

import net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential;
import net.summerfarm.manage.domain.order.param.query.OrderItemPreferentialQueryParam;
import net.summerfarm.manage.domain.order.entity.OrderItemPreferentialEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-31 15:06:17
 * @version 1.0
 *
 */
@Mapper
public interface OrderItemPreferentialMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(OrderItemPreferential record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(OrderItemPreferential record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    OrderItemPreferential selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<OrderItemPreferential> selectByCondition(OrderItemPreferentialQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<OrderItemPreferentialEntity> getPage(OrderItemPreferentialQueryParam param);
}

