package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.AppPopTopMatchedCompetitorSkuList;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.domain.product.param.command.AppPopTopMatchedCompetitorSkuListCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
public class AppPopTopMatchedCompetitorSkuListConverter {

    private AppPopTopMatchedCompetitorSkuListConverter() {
        // 无需实现
    }




    public static List<AppPopTopMatchedCompetitorSkuListEntity> toAppPopTopMatchedCompetitorSkuListEntityList(List<AppPopTopMatchedCompetitorSkuList> appPopTopMatchedCompetitorSkuListList) {
        if (appPopTopMatchedCompetitorSkuListList == null) {
            return Collections.emptyList();
        }
        List<AppPopTopMatchedCompetitorSkuListEntity> appPopTopMatchedCompetitorSkuListEntityList = new ArrayList<>();
        for (AppPopTopMatchedCompetitorSkuList appPopTopMatchedCompetitorSkuList : appPopTopMatchedCompetitorSkuListList) {
            appPopTopMatchedCompetitorSkuListEntityList.add(toAppPopTopMatchedCompetitorSkuListEntity(appPopTopMatchedCompetitorSkuList));
        }
        return appPopTopMatchedCompetitorSkuListEntityList;
}


    public static AppPopTopMatchedCompetitorSkuListEntity toAppPopTopMatchedCompetitorSkuListEntity(AppPopTopMatchedCompetitorSkuList appPopTopMatchedCompetitorSkuList) {
        if (appPopTopMatchedCompetitorSkuList == null) {
             return null;
        }
        AppPopTopMatchedCompetitorSkuListEntity appPopTopMatchedCompetitorSkuListEntity = new AppPopTopMatchedCompetitorSkuListEntity();
        appPopTopMatchedCompetitorSkuListEntity.setId(appPopTopMatchedCompetitorSkuList.getId());
        appPopTopMatchedCompetitorSkuListEntity.setSku(appPopTopMatchedCompetitorSkuList.getSku());
        appPopTopMatchedCompetitorSkuListEntity.setWeight(appPopTopMatchedCompetitorSkuList.getWeight());
        appPopTopMatchedCompetitorSkuListEntity.setSkuName(appPopTopMatchedCompetitorSkuList.getSkuName());
        appPopTopMatchedCompetitorSkuListEntity.setPdName(appPopTopMatchedCompetitorSkuList.getPdName());
        appPopTopMatchedCompetitorSkuListEntity.setTopMatchedCompetitorSkuList(appPopTopMatchedCompetitorSkuList.getTopMatchedCompetitorSkuList());
        appPopTopMatchedCompetitorSkuListEntity.setCategory(appPopTopMatchedCompetitorSkuList.getCategory());
        appPopTopMatchedCompetitorSkuListEntity.setGmv(appPopTopMatchedCompetitorSkuList.getGmv());
        appPopTopMatchedCompetitorSkuListEntity.setOrderCnt(appPopTopMatchedCompetitorSkuList.getOrderCnt());
        appPopTopMatchedCompetitorSkuListEntity.setOrderQuantity(appPopTopMatchedCompetitorSkuList.getOrderQuantity());
        appPopTopMatchedCompetitorSkuListEntity.setLastOrderTime(appPopTopMatchedCompetitorSkuList.getLastOrderTime());
        appPopTopMatchedCompetitorSkuListEntity.setCreateTime(appPopTopMatchedCompetitorSkuList.getCreateTime());
        appPopTopMatchedCompetitorSkuListEntity.setDs(appPopTopMatchedCompetitorSkuList.getDs());
        return appPopTopMatchedCompetitorSkuListEntity;
    }








    public static AppPopTopMatchedCompetitorSkuList toAppPopTopMatchedCompetitorSkuList(AppPopTopMatchedCompetitorSkuListCommandParam param) {
        if (param == null) {
            return null;
        }
        AppPopTopMatchedCompetitorSkuList appPopTopMatchedCompetitorSkuList = new AppPopTopMatchedCompetitorSkuList();
        appPopTopMatchedCompetitorSkuList.setId(param.getId());
        appPopTopMatchedCompetitorSkuList.setSku(param.getSku());
        appPopTopMatchedCompetitorSkuList.setWeight(param.getWeight());
        appPopTopMatchedCompetitorSkuList.setSkuName(param.getSkuName());
        appPopTopMatchedCompetitorSkuList.setPdName(param.getPdName());
        appPopTopMatchedCompetitorSkuList.setTopMatchedCompetitorSkuList(param.getTopMatchedCompetitorSkuList());
        appPopTopMatchedCompetitorSkuList.setCategory(param.getCategory());
        appPopTopMatchedCompetitorSkuList.setGmv(param.getGmv());
        appPopTopMatchedCompetitorSkuList.setOrderCnt(param.getOrderCnt());
        appPopTopMatchedCompetitorSkuList.setOrderQuantity(param.getOrderQuantity());
        appPopTopMatchedCompetitorSkuList.setLastOrderTime(param.getLastOrderTime());
        appPopTopMatchedCompetitorSkuList.setCreateTime(param.getCreateTime());
        appPopTopMatchedCompetitorSkuList.setDs(param.getDs());
        return appPopTopMatchedCompetitorSkuList;
    }
}
