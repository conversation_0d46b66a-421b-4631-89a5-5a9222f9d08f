package net.summerfarm.manage.infrastructure.repository.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantCommandRepository;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantConverter;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantMapper;
import net.summerfarm.manage.infrastructure.model.merchant.Merchant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-19 10:55:24
* @version 1.0
*
*/
@Repository
public class MerchantCommandRepositoryImpl implements MerchantCommandRepository {

    @Autowired
    private MerchantMapper merchantMapper;


    @Override
    public MerchantEntity insertSelective(MerchantEntity record) {
        Merchant merchant = MerchantConverter.toMerchant(record);
        merchantMapper.insertSelective(merchant);
        return MerchantConverter.toMerchantEntity(merchant);
    }

    @Override
    public Boolean updateByPrimaryKeySelective(MerchantCommandParam merchantCommandParam) {
        Merchant merchant = MerchantConverter.toMerchant(merchantCommandParam);
        int update = merchantMapper.updateByPrimaryKeySelective(merchant);
        return update > 0;
    }

    @Override
    public int updateByPrimaryKeySelectiveBatch(List<MerchantCommandParam> list) {
        return merchantMapper.updateByPrimaryKeySelectiveBatch(list);
    }
}