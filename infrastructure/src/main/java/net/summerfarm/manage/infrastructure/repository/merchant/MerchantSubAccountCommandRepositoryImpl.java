package net.summerfarm.manage.infrastructure.repository.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantSubAccountEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantSubAccountCommandRepository;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantSubAccountConverter;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantSubAccountMapper;
import net.summerfarm.manage.infrastructure.model.merchant.MerchantSubAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-09-19 13:44:23
* @version 1.0
*
*/
@Repository
public class MerchantSubAccountCommandRepositoryImpl implements MerchantSubAccountCommandRepository {

    @Autowired
    private MerchantSubAccountMapper accountMapper;

    @Override
    public MerchantSubAccountEntity insertSelective(MerchantSubAccountEntity record) {
        MerchantSubAccount merchantSubAccount = MerchantSubAccountConverter.toMerchantSubAccount(record);
        accountMapper.insert(merchantSubAccount);
        return MerchantSubAccountConverter.toMerchantSubAccountEntity(merchantSubAccount);
    }

    @Override
    public Integer updateMain2Base(Long updateMid, Long mId) {
        return accountMapper.updateMain2Base(updateMid, mId);
    }
}