package net.summerfarm.manage.infrastructure.repository.price;

import net.summerfarm.manage.infrastructure.model.price.AreaSkuPriceMarkupConfig;
import net.summerfarm.manage.infrastructure.mapper.price.AreaSkuPriceMarkupConfigMapper;
import net.summerfarm.manage.infrastructure.converter.price.AreaSkuPriceMarkupConfigConverter;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigCommandRepository;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.command.AreaSkuPriceMarkupConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-03-26 13:59:07
* @version 1.0
*
*/
@Repository
public class AreaSkuPriceMarkupConfigCommandRepositoryImpl implements AreaSkuPriceMarkupConfigCommandRepository {

    @Autowired
    private AreaSkuPriceMarkupConfigMapper areaSkuPriceMarkupConfigMapper;
    @Override
    public AreaSkuPriceMarkupConfigEntity insertSelective(AreaSkuPriceMarkupConfigCommandParam param) {
        AreaSkuPriceMarkupConfig areaSkuPriceMarkupConfig = AreaSkuPriceMarkupConfigConverter.INSTANCE.toAreaSkuPriceMarkupConfig(param);
        areaSkuPriceMarkupConfigMapper.insertSelective(areaSkuPriceMarkupConfig);
        return AreaSkuPriceMarkupConfigConverter.INSTANCE.toAreaSkuPriceMarkupConfigEntity(areaSkuPriceMarkupConfig);
    }

    @Override
    public int updateSelectiveById(AreaSkuPriceMarkupConfigCommandParam param){
        return areaSkuPriceMarkupConfigMapper.updateSelectiveById(AreaSkuPriceMarkupConfigConverter.INSTANCE.toAreaSkuPriceMarkupConfig(param));
    }

    @Override
    public int updateMarkupValueByIds(BigDecimal markupValue, List<Long> ids) {
        return areaSkuPriceMarkupConfigMapper.updateMarkupValueByIds(markupValue, ids);
    }

    @Override
    public int batchInsert(List<AreaSkuPriceMarkupConfigCommandParam> paramList) {
        List<AreaSkuPriceMarkupConfig> areaSkuPriceMarkupConfigs = AreaSkuPriceMarkupConfigConverter.INSTANCE.toAreaSkuPriceMarkupConfigList(paramList);
        return areaSkuPriceMarkupConfigMapper.batchInsert(areaSkuPriceMarkupConfigs);
    }


    @Override
    public int remove(Long id) {
        return areaSkuPriceMarkupConfigMapper.remove(id);
    }
}