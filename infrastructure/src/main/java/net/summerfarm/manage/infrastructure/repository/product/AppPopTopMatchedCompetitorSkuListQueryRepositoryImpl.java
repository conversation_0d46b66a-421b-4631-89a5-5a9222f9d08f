package net.summerfarm.manage.infrastructure.repository.product;


import net.summerfarm.manage.infrastructure.offlinemapper.AppPopTopMatchedCompetitorSkuListMapper;
import net.summerfarm.manage.infrastructure.converter.product.AppPopTopMatchedCompetitorSkuListConverter;
import net.summerfarm.manage.domain.product.repository.AppPopTopMatchedCompetitorSkuListQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-11-18 15:55:40
* @version 1.0
*
*/
@Repository
public class AppPopTopMatchedCompetitorSkuListQueryRepositoryImpl implements AppPopTopMatchedCompetitorSkuListQueryRepository {

    @Autowired
    private AppPopTopMatchedCompetitorSkuListMapper appPopTopMatchedCompetitorSkuListMapper;


    @Override
    public PageInfo<AppPopTopMatchedCompetitorSkuListEntity> getPage(AppPopTopMatchedCompetitorSkuListQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AppPopTopMatchedCompetitorSkuListEntity> entities = appPopTopMatchedCompetitorSkuListMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AppPopTopMatchedCompetitorSkuListEntity selectById(Long id) {
        return AppPopTopMatchedCompetitorSkuListConverter.toAppPopTopMatchedCompetitorSkuListEntity(appPopTopMatchedCompetitorSkuListMapper.selectById(id));
    }


    @Override
    public List<AppPopTopMatchedCompetitorSkuListEntity> selectByCondition(AppPopTopMatchedCompetitorSkuListQueryParam param) {
        return AppPopTopMatchedCompetitorSkuListConverter.toAppPopTopMatchedCompetitorSkuListEntityList(appPopTopMatchedCompetitorSkuListMapper.selectByCondition(param));
    }

}