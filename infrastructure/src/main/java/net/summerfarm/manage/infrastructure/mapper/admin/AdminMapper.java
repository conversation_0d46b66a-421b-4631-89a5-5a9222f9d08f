package net.summerfarm.manage.infrastructure.mapper.admin;

import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminQueryParam;
import net.summerfarm.manage.domain.area.valueobject.LargeAreaValueObject;
import net.summerfarm.manage.infrastructure.model.admin.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-09-20 14:00:53
 * @version 1.0
 *
 */
@Mapper
public interface AdminMapper{

    int insertSelective(Admin record);

    int updateById(Admin record);

    int updateByCondition(Admin record);

    int remove(@Param("adminId") Long adminId);

    Admin selectByPrimaryKey(@Param("adminId") Long adminId);

    Admin selectOne(Admin record);

    List<Admin> selectByCondition(Admin record);

    List<Admin> selectByAdminIds(@Param("adminIds") List<Long> adminIds);


    List<Admin> selectByAdminRealNames(@Param("realNames") List<String> realNames);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<AdminEntity> getPage(AdminQueryParam param);

    List<LargeAreaValueObject> selectMajorCustomerCooperationLargeAreaNo(@Param("adminId") Long adminId);
}
