package net.summerfarm.manage.infrastructure.repository.activity;

import net.summerfarm.manage.infrastructure.model.activity.MarketRuleHistory;
import net.summerfarm.manage.infrastructure.mapper.activity.MarketRuleHistoryMapper;
import net.summerfarm.manage.infrastructure.converter.activity.MarketRuleHistoryConverter;
import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryCommandRepository;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.command.MarketRuleHistoryCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-31 17:39:38
* @version 1.0
*
*/
@Repository
public class MarketRuleHistoryCommandRepositoryImpl implements MarketRuleHistoryCommandRepository {

    @Autowired
    private MarketRuleHistoryMapper marketRuleHistoryMapper;
    @Override
    public MarketRuleHistoryEntity insertSelective(MarketRuleHistoryCommandParam param) {
        MarketRuleHistory marketRuleHistory = MarketRuleHistoryConverter.toMarketRuleHistory(param);
        marketRuleHistoryMapper.insertSelective(marketRuleHistory);
        return MarketRuleHistoryConverter.toMarketRuleHistoryEntity(marketRuleHistory);
    }

    @Override
    public int updateSelectiveById(MarketRuleHistoryCommandParam param){
        return marketRuleHistoryMapper.updateSelectiveById(MarketRuleHistoryConverter.toMarketRuleHistory(param));
    }


    @Override
    public int remove(Long id) {
        return marketRuleHistoryMapper.remove(id);
    }
}