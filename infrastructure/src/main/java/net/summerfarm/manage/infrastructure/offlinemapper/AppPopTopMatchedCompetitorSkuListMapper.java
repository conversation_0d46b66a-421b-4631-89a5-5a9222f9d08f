package net.summerfarm.manage.infrastructure.offlinemapper;

import net.summerfarm.manage.infrastructure.model.product.AppPopTopMatchedCompetitorSkuList;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
@Mapper
public interface AppPopTopMatchedCompetitorSkuListMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(AppPopTopMatchedCompetitorSkuList record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(AppPopTopMatchedCompetitorSkuList record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AppPopTopMatchedCompetitorSkuList selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<AppPopTopMatchedCompetitorSkuList> selectByCondition(AppPopTopMatchedCompetitorSkuListQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<AppPopTopMatchedCompetitorSkuListEntity> getPage(AppPopTopMatchedCompetitorSkuListQueryParam param);
}

