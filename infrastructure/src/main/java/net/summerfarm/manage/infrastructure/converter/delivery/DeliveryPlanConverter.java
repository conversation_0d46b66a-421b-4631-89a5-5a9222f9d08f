package net.summerfarm.manage.infrastructure.converter.delivery;

import net.summerfarm.manage.domain.delivery.param.command.DeliveryPlanCommandParam;
import net.summerfarm.manage.infrastructure.model.delivery.DeliveryPlan;

/**
 * @ClassName DeliveryPlanConverter
 * @Description TODO
 * <AUTHOR>
 * @Date 18:02 2024/1/18
 * @Version 1.0
 **/
public class DeliveryPlanConverter {

    public static DeliveryPlan toDeliveryPlan(DeliveryPlanCommandParam param) {
        if (param == null) {
            return null;
        }
        DeliveryPlan deliveryPlan = new DeliveryPlan();
        deliveryPlan.setId(param.getId());
        deliveryPlan.setStatus(param.getStatus());
        return deliveryPlan;
    }
}
