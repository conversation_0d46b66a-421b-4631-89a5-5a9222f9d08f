package net.summerfarm.manage.infrastructure.repository.merchant;


import net.summerfarm.manage.infrastructure.model.merchant.MerchantSkuOrderData;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantSkuOrderDataMapper;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantSkuOrderDataConverter;
import net.summerfarm.manage.domain.merchant.repository.MerchantSkuOrderDataQueryRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantSkuOrderDataEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantSkuOrderDataQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
@Repository
public class MerchantSkuOrderDataQueryRepositoryImpl implements MerchantSkuOrderDataQueryRepository {

    @Autowired
    private MerchantSkuOrderDataMapper merchantSkuOrderDataMapper;


    @Override
    public PageInfo<MerchantSkuOrderDataEntity> getPage(MerchantSkuOrderDataQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantSkuOrderDataEntity> entities = merchantSkuOrderDataMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MerchantSkuOrderDataEntity selectById(Long id) {
        return MerchantSkuOrderDataConverter.toMerchantSkuOrderDataEntity(merchantSkuOrderDataMapper.selectById(id));
    }


    @Override
    public List<MerchantSkuOrderDataEntity> selectByCondition(MerchantSkuOrderDataQueryParam param) {
        return MerchantSkuOrderDataConverter.toMerchantSkuOrderDataEntityList(merchantSkuOrderDataMapper.selectByCondition(param));
    }

}