package net.summerfarm.manage.infrastructure.repository.order;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;
import net.summerfarm.manage.domain.order.param.query.OrdersQueryParam;
import net.summerfarm.manage.domain.order.repository.OrdersQueryRepository;
import net.summerfarm.manage.infrastructure.converter.order.OrdersConverter;
import net.summerfarm.manage.infrastructure.mapper.order.OrdersMapper;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2024-01-18 15:49:06
* @version 1.0
*
*/
@Repository
public class OrdersQueryRepositoryImpl implements OrdersQueryRepository {

    @Autowired
    private OrdersMapper ordersMapper;


    @Override
    public PageInfo<OrdersEntity> getPage(OrdersQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<OrdersEntity> entities = ordersMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public OrdersEntity selectById(Long orderId) {
        return OrdersConverter.toOrdersEntity(ordersMapper.selectById(orderId));
    }


    @Override
    public List<OrdersEntity> selectByCondition(OrdersQueryParam param) {
        return OrdersConverter.toOrdersEntityList(ordersMapper.selectByCondition(param));
    }

    @Override
    public OrdersEntity selectByOrderNo(String orderNo) {
        return  OrdersConverter.toOrdersEntity(ordersMapper.selectByOrderyNo(orderNo));
    }

    @Override
    public BigDecimal selectTotalPriceByMonth(OrdersQueryParam ordersQueryParam) {
        return ordersMapper.selectTotalPriceByMonth(ordersQueryParam);
    }

    @Override
    public List<OrdersEntity> batchGetOrderNos(List<String> subOrderNos) {
        return OrdersConverter.toOrdersEntityList(ordersMapper.batchGetOrderNos(subOrderNos));
    }

    @Override
    public int selectCountByMId(Long mId) {
        return ordersMapper.selectCountByMId(mId);
    }

    @Override
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseSkuTotalNum(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuFlatObjects = ordersMapper.queryTimingOrderProxySaleNoWarehouseSkuTotalNum(storeNo);
        //过滤数量存在但是SKU和城配仓编号为空的数据
        return skuFlatObjects.stream().filter(item -> item.getQuantity() != null && item.getStoreNo() != null).collect(Collectors.toList());
    }

    @Override
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuFlatObjects = ordersMapper.queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum(storeNo);
        //过滤数量存在但是SKU和城配仓编号为空的数据
        return skuFlatObjects.stream().filter(item -> item.getQuantity() != null && item.getStoreNo() != null).collect(Collectors.toList());
    }

    @Override
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuFlatObjects = ordersMapper.queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum(storeNo);
        //过滤数量存在但是SKU和城配仓编号为空的数据
        return skuFlatObjects.stream().filter(item -> item.getQuantity() != null && item.getStoreNo() != null).collect(Collectors.toList());
    }

    @Override
    public List<OrdersEntity> getPendingOrders(String orderNo, int pageStart, int pageSize) {
        return OrdersConverter.toOrdersEntityList(ordersMapper.getPendingOrders(orderNo, pageStart, pageSize));
    }
}