package net.summerfarm.manage.infrastructure.repository.afterSale;

import net.summerfarm.manage.infrastructure.model.afterSale.AfterSaleDeliveryPath;
import net.summerfarm.manage.infrastructure.mapper.afterSale.AfterSaleDeliveryPathMapper;
import net.summerfarm.manage.infrastructure.converter.afterSale.AfterSaleDeliveryPathConverter;
import net.summerfarm.manage.domain.afterSale.repository.AfterSaleDeliveryPathCommandRepository;
import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import net.summerfarm.manage.domain.afterSale.param.command.AfterSaleDeliveryPathCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-12-31 14:32:57
* @version 1.0
*
*/
@Repository
public class AfterSaleDeliveryPathCommandRepositoryImpl implements AfterSaleDeliveryPathCommandRepository {

    @Autowired
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Override
    public AfterSaleDeliveryPathEntity insertSelective(AfterSaleDeliveryPathCommandParam param) {
        AfterSaleDeliveryPath afterSaleDeliveryPath = AfterSaleDeliveryPathConverter.toAfterSaleDeliveryPath(param);
        afterSaleDeliveryPathMapper.insertSelective(afterSaleDeliveryPath);
        return AfterSaleDeliveryPathConverter.toAfterSaleDeliveryPathEntity(afterSaleDeliveryPath);
    }

    @Override
    public int updateSelectiveById(AfterSaleDeliveryPathCommandParam param){
        return afterSaleDeliveryPathMapper.updateSelectiveById(AfterSaleDeliveryPathConverter.toAfterSaleDeliveryPath(param));
    }


    @Override
    public int remove(Long id) {
        return afterSaleDeliveryPathMapper.remove(id);
    }
}