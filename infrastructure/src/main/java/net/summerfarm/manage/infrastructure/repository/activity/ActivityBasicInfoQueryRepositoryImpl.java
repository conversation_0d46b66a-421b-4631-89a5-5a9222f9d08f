package net.summerfarm.manage.infrastructure.repository.activity;


import net.summerfarm.manage.domain.activity.param.query.ActivityScopeQueryParam;
import net.summerfarm.manage.domain.activity.valueObject.ActivityItemScopeValueObject;
import net.summerfarm.manage.infrastructure.model.activity.ActivityBasicInfo;
import net.summerfarm.manage.infrastructure.mapper.activity.ActivityBasicInfoMapper;
import net.summerfarm.manage.infrastructure.converter.activity.ActivityBasicInfoConverter;
import net.summerfarm.manage.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.summerfarm.manage.domain.activity.entity.ActivityBasicInfoEntity;
import net.summerfarm.manage.domain.activity.param.query.ActivityBasicInfoQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-04-09 15:06:01
* @version 1.0
*
*/
@Repository
public class ActivityBasicInfoQueryRepositoryImpl implements ActivityBasicInfoQueryRepository {

    @Autowired
    private ActivityBasicInfoMapper activityBasicInfoMapper;


    @Override
    public PageInfo<ActivityBasicInfoEntity> getPage(ActivityBasicInfoQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivityBasicInfoEntity> entities = activityBasicInfoMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivityBasicInfoEntity selectById(Long id) {
        return ActivityBasicInfoConverter.toActivityBasicInfoEntity(activityBasicInfoMapper.selectById(id));
    }


    @Override
    public List<ActivityBasicInfoEntity> selectByCondition(ActivityBasicInfoQueryParam param) {
        return ActivityBasicInfoConverter.toActivityBasicInfoEntityList(activityBasicInfoMapper.selectByCondition(param));
    }

    @Override
    public List<ActivityItemScopeValueObject> listByScope(List<ActivityScopeQueryParam> list, Integer type, Integer activityStatus) {
        return activityBasicInfoMapper.listByScope(list, type, activityStatus);
    }

}