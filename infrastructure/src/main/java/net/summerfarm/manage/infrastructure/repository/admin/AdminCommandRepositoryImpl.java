package net.summerfarm.manage.infrastructure.repository.admin;

import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminCommandRepository;
import net.summerfarm.manage.infrastructure.converter.admin.AdminConverter;
import net.summerfarm.manage.infrastructure.mapper.admin.AdminMapper;
import net.summerfarm.manage.infrastructure.model.admin.Admin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-09-20 11:38:45
* @version 1.0
*
*/
@Repository
public class AdminCommandRepositoryImpl implements AdminCommandRepository {

    @Autowired
    private AdminMapper adminMapper;

    @Override
    public AdminEntity insertSelective(AdminEntity entity) {
        Admin admin = AdminConverter.toAdmin(entity);
        adminMapper.insertSelective(admin);
        return AdminConverter.toAdminEntity(admin);
    }

    @Override
    public int updateById(AdminEntity entity) {
        return adminMapper.updateById(AdminConverter.toAdmin(entity));
    }

    @Override
    public int updateByCondition(AdminEntity entity) {
        return adminMapper.updateByCondition(AdminConverter.toAdmin(entity));
    }

    @Override
    public int remove(Long adminId) {
        return adminMapper.remove(adminId);
    }
}