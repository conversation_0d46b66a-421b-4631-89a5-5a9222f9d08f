package net.summerfarm.manage.infrastructure.repository.product;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.query.ProductLabelValueQueryParam;
import net.summerfarm.manage.domain.product.repository.ProductLabelValueQueryRepository;
import net.summerfarm.manage.infrastructure.converter.product.ProductLabelValueConverter;
import net.summerfarm.manage.infrastructure.mapper.product.ProductLabelValueMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:46
* @version 1.0
*
*/
@Repository
public class ProductLabelValueQueryRepositoryImpl implements ProductLabelValueQueryRepository {

    @Autowired
    private ProductLabelValueMapper productLabelValueMapper;


    @Override
    public PageInfo<ProductLabelValueEntity> getPage(ProductLabelValueQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ProductLabelValueEntity> entities = productLabelValueMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ProductLabelValueEntity selectById(Long id) {
        return ProductLabelValueConverter.toProductLabelValueEntity(productLabelValueMapper.selectById(id));
    }


    @Override
    public List<ProductLabelValueEntity> selectByCondition(ProductLabelValueQueryParam param) {
        return ProductLabelValueConverter.toProductLabelValueEntityList(productLabelValueMapper.selectByCondition(param));
    }

    @Override
    public List<ProductLabelValueEntity> selectBySku(String sku) {
        return productLabelValueMapper.selectBySku(sku);
    }
}