package net.summerfarm.manage.infrastructure.repository.customization;

import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;
import net.summerfarm.manage.domain.customization.repository.CustomizationRequestSkuMappingRepository;
import net.summerfarm.manage.infrastructure.converter.CustomizationRequestSkuMappingConverter;
import net.summerfarm.manage.infrastructure.mapper.customization.CustomizationRequestSkuMappingMapper;
import net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定制需求sku关联仓储实现
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Repository
public class CustomizationRequestSkuMappingRepositoryImpl implements CustomizationRequestSkuMappingRepository {

    @Autowired
    private CustomizationRequestSkuMappingMapper customizationRequestSkuMappingMapper;

    @Override
    public CustomizationRequestSkuMappingEntity save(CustomizationRequestSkuMappingEntity entity) {
        CustomizationRequestSkuMapping model = CustomizationRequestSkuMappingConverter.entityToModel(entity);
        customizationRequestSkuMappingMapper.insert(model);
        entity.setId(model.getId());
        return entity;
    }

    @Override
    public int batchSave(List<CustomizationRequestSkuMappingEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }
        
        List<CustomizationRequestSkuMapping> models = entities.stream()
                .map(CustomizationRequestSkuMappingConverter::entityToModel)
                .collect(Collectors.toList());
        
        return customizationRequestSkuMappingMapper.batchInsert(models);
    }

    @Override
    public boolean deleteById(Long id) {
        return customizationRequestSkuMappingMapper.deleteById(id) > 0;
    }

    @Override
    public int deleteByCustomizationRequestId(Long customizationRequestId) {
        return customizationRequestSkuMappingMapper.deleteByCustomizationRequestId(customizationRequestId);
    }

    @Override
    public int deleteByIds(List<Long> ids) {
        return customizationRequestSkuMappingMapper.deleteByIds(ids);
    }

    @Override
    public CustomizationRequestSkuMappingEntity update(CustomizationRequestSkuMappingEntity entity) {
        CustomizationRequestSkuMapping model = CustomizationRequestSkuMappingConverter.entityToModel(entity);
        customizationRequestSkuMappingMapper.updateById(model);
        return entity;
    }

    @Override
    public CustomizationRequestSkuMappingEntity findById(Long id) {
        CustomizationRequestSkuMapping model = customizationRequestSkuMappingMapper.selectById(id);
        return model != null ? CustomizationRequestSkuMappingConverter.modelToEntity(model) : null;
    }

    @Override
    public List<CustomizationRequestSkuMappingEntity> findByCustomizationRequestId(Long customizationRequestId) {
        List<CustomizationRequestSkuMapping> models = customizationRequestSkuMappingMapper.selectByCustomizationRequestId(customizationRequestId);
        return models.stream()
                .map(CustomizationRequestSkuMappingConverter::modelToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomizationRequestSkuMappingEntity> findBySku(String sku) {
        List<CustomizationRequestSkuMapping> models = customizationRequestSkuMappingMapper.selectBySku(sku);
        return models.stream()
                .map(CustomizationRequestSkuMappingConverter::modelToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomizationRequestSkuMappingEntity> findBySourceSku(String sourceSku) {
        List<CustomizationRequestSkuMapping> models = customizationRequestSkuMappingMapper.selectBySourceSku(sourceSku);
        return models.stream()
                .map(CustomizationRequestSkuMappingConverter::modelToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public long count(CustomizationRequestSkuMappingEntity entity) {
        CustomizationRequestSkuMapping queryModel = CustomizationRequestSkuMappingConverter.entityToModel(entity);
        return customizationRequestSkuMappingMapper.selectCount(queryModel);
    }

    @Override
    public List<CustomizationRequestSkuMappingEntity> findByCreateTimeBefore(LocalDateTime beforeTime) {
        List<CustomizationRequestSkuMapping> models = customizationRequestSkuMappingMapper.selectByCreateTimeBefore(beforeTime);
        return models.stream()
                .map(CustomizationRequestSkuMappingConverter::modelToEntity)
                .collect(Collectors.toList());
    }
}
