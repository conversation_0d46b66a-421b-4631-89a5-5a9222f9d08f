package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSku;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
public class MerchantFrequentlyBuyingSkuConverter {

    private MerchantFrequentlyBuyingSkuConverter() {
        // 无需实现
    }




    public static List<MerchantFrequentlyBuyingSkuEntity> toMerchantFrequentlyBuyingSkuEntityList(List<MerchantFrequentlyBuyingSku> merchantFrequentlyBuyingSkuList) {
        if (merchantFrequentlyBuyingSkuList == null) {
            return Collections.emptyList();
        }
        List<MerchantFrequentlyBuyingSkuEntity> merchantFrequentlyBuyingSkuEntityList = new ArrayList<>();
        for (MerchantFrequentlyBuyingSku merchantFrequentlyBuyingSku : merchantFrequentlyBuyingSkuList) {
            merchantFrequentlyBuyingSkuEntityList.add(toMerchantFrequentlyBuyingSkuEntity(merchantFrequentlyBuyingSku));
        }
        return merchantFrequentlyBuyingSkuEntityList;
}


    public static MerchantFrequentlyBuyingSkuEntity toMerchantFrequentlyBuyingSkuEntity(MerchantFrequentlyBuyingSku merchantFrequentlyBuyingSku) {
        if (merchantFrequentlyBuyingSku == null) {
             return null;
        }
        MerchantFrequentlyBuyingSkuEntity merchantFrequentlyBuyingSkuEntity = new MerchantFrequentlyBuyingSkuEntity();
        merchantFrequentlyBuyingSkuEntity.setId(merchantFrequentlyBuyingSku.getId());
        merchantFrequentlyBuyingSkuEntity.setCreateTime(merchantFrequentlyBuyingSku.getCreateTime());
        merchantFrequentlyBuyingSkuEntity.setUpdateTime(merchantFrequentlyBuyingSku.getUpdateTime());
        merchantFrequentlyBuyingSkuEntity.setMId(merchantFrequentlyBuyingSku.getMId());
        merchantFrequentlyBuyingSkuEntity.setSku(merchantFrequentlyBuyingSku.getSku());
        merchantFrequentlyBuyingSkuEntity.setStatus(merchantFrequentlyBuyingSku.getStatus());
        merchantFrequentlyBuyingSkuEntity.setTop(merchantFrequentlyBuyingSku.getTop());
        merchantFrequentlyBuyingSkuEntity.setSource(merchantFrequentlyBuyingSku.getSource());
        merchantFrequentlyBuyingSkuEntity.setRecentDeleteTime(merchantFrequentlyBuyingSku.getRecentDeleteTime());
        return merchantFrequentlyBuyingSkuEntity;
    }








    public static MerchantFrequentlyBuyingSku toMerchantFrequentlyBuyingSku(MerchantFrequentlyBuyingSkuCommandParam param) {
        if (param == null) {
            return null;
        }
        MerchantFrequentlyBuyingSku merchantFrequentlyBuyingSku = new MerchantFrequentlyBuyingSku();
        merchantFrequentlyBuyingSku.setId(param.getId());
        merchantFrequentlyBuyingSku.setCreateTime(param.getCreateTime());
        merchantFrequentlyBuyingSku.setUpdateTime(param.getUpdateTime());
        merchantFrequentlyBuyingSku.setMId(param.getMId());
        merchantFrequentlyBuyingSku.setSku(param.getSku());
        merchantFrequentlyBuyingSku.setStatus(param.getStatus());
        merchantFrequentlyBuyingSku.setTop(param.getTop());
        merchantFrequentlyBuyingSku.setSource(param.getSource());
        merchantFrequentlyBuyingSku.setRecentDeleteTime(param.getRecentDeleteTime());
        return merchantFrequentlyBuyingSku;
    }
}
