package net.summerfarm.manage.infrastructure.converter.coupon;

import net.summerfarm.manage.infrastructure.model.coupon.Coupon;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.CouponCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-12-19 13:49:12
 * @version 1.0
 *
 */
public class CouponConverter {

    private CouponConverter() {
        // 无需实现
    }




    public static List<CouponEntity> toCouponEntityList(List<Coupon> couponList) {
        if (couponList == null) {
            return Collections.emptyList();
        }
        List<CouponEntity> couponEntityList = new ArrayList<>();
        for (Coupon coupon : couponList) {
            couponEntityList.add(toCouponEntity(coupon));
        }
        return couponEntityList;
}


    public static CouponEntity toCouponEntity(Coupon coupon) {
        if (coupon == null) {
             return null;
        }
        CouponEntity couponEntity = new CouponEntity();
        couponEntity.setId(coupon.getId());
        couponEntity.setName(coupon.getName());
        couponEntity.setCode(coupon.getCode());
        couponEntity.setMoney(coupon.getMoney());
        couponEntity.setThreshold(coupon.getThreshold());
        couponEntity.setType(coupon.getType());
        couponEntity.setVaildDate(coupon.getVaildDate());
        couponEntity.setVaildTime(coupon.getVaildTime());
        couponEntity.setGrouping(coupon.getGrouping());
        couponEntity.setNewHand(coupon.getNewHand());
        couponEntity.setCategoryId(coupon.getCategoryId());
        couponEntity.setSku(coupon.getSku());
        couponEntity.setReamrk(coupon.getReamrk());
        couponEntity.setAddTime(coupon.getAddTime());
        couponEntity.setStatus(coupon.getStatus());
        couponEntity.setAgioType(coupon.getAgioType());
        couponEntity.setStartDate(coupon.getStartDate());
        couponEntity.setStartTime(coupon.getStartTime());
        couponEntity.setUpdateTime(coupon.getUpdateTime());
        couponEntity.setLimitFlag(coupon.getLimitFlag());
        couponEntity.setActivityScope(coupon.getActivityScope());
        couponEntity.setTaskTag(coupon.getTaskTag());
        couponEntity.setDeleteTag(coupon.getDeleteTag());
        couponEntity.setAutoCreated(coupon.getAutoCreated());
        couponEntity.setQuantityClaimed(coupon.getQuantityClaimed());
        couponEntity.setGrantAmount(coupon.getGrantAmount());
        couponEntity.setGrantLimit(coupon.getGrantLimit());
        couponEntity.setCreator(coupon.getCreator());
        couponEntity.setSkuScopeDesc(coupon.getSkuScopeDesc());
        couponEntity.setSkuScope(coupon.getSkuScope());
        return couponEntity;
    }








    public static Coupon toCoupon(CouponCommandParam param) {
        if (param == null) {
            return null;
        }
        Coupon coupon = new Coupon();
        coupon.setId(param.getId());
        coupon.setName(param.getName());
        coupon.setCode(param.getCode());
        coupon.setMoney(param.getMoney());
        coupon.setThreshold(param.getThreshold());
        coupon.setType(param.getType());
        coupon.setVaildDate(param.getVaildDate());
        coupon.setVaildTime(param.getVaildTime());
        coupon.setGrouping(param.getGrouping());
        coupon.setNewHand(param.getNewHand());
        coupon.setCategoryId(param.getCategoryId());
        coupon.setSku(param.getSku());
        coupon.setReamrk(param.getReamrk());
        coupon.setAddTime(param.getAddTime());
        coupon.setStatus(param.getStatus());
        coupon.setAgioType(param.getAgioType());
        coupon.setStartDate(param.getStartDate());
        coupon.setStartTime(param.getStartTime());
        coupon.setUpdateTime(param.getUpdateTime());
        coupon.setLimitFlag(param.getLimitFlag());
        coupon.setActivityScope(param.getActivityScope());
        coupon.setTaskTag(param.getTaskTag());
        coupon.setDeleteTag(param.getDeleteTag());
        coupon.setAutoCreated(param.getAutoCreated());
        coupon.setQuantityClaimed(param.getQuantityClaimed());
        coupon.setGrantAmount(param.getGrantAmount());
        coupon.setGrantLimit(param.getGrantLimit());
        coupon.setCreator(param.getCreator());
        coupon.setSkuScopeDesc(param.getSkuScopeDesc());
        coupon.setSkuScope(param.getSkuScope());
        return coupon;
    }
}
