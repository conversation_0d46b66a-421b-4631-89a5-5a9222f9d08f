package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord;
import net.summerfarm.manage.infrastructure.mapper.order.WxShippingInfoUploadRecordMapper;
import net.summerfarm.manage.infrastructure.converter.order.WxShippingInfoUploadRecordConverter;
import net.summerfarm.manage.domain.order.repository.WxShippingInfoUploadRecordCommandRepository;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import net.summerfarm.manage.domain.order.param.command.WxShippingInfoUploadRecordCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-10-15 17:49:41
* @version 1.0
*
*/
@Repository
public class WxShippingInfoUploadRecordCommandRepositoryImpl implements WxShippingInfoUploadRecordCommandRepository {

    @Autowired
    private WxShippingInfoUploadRecordMapper wxShippingInfoUploadRecordMapper;
    @Override
    public WxShippingInfoUploadRecordEntity insertSelective(WxShippingInfoUploadRecordCommandParam param) {
        WxShippingInfoUploadRecord wxShippingInfoUploadRecord = WxShippingInfoUploadRecordConverter.toWxShippingInfoUploadRecord(param);
        wxShippingInfoUploadRecordMapper.insertSelective(wxShippingInfoUploadRecord);
        return WxShippingInfoUploadRecordConverter.toWxShippingInfoUploadRecordEntity(wxShippingInfoUploadRecord);
    }

    @Override
    public int updateSelectiveById(WxShippingInfoUploadRecordCommandParam param){
        return wxShippingInfoUploadRecordMapper.updateSelectiveById(WxShippingInfoUploadRecordConverter.toWxShippingInfoUploadRecord(param));
    }


    @Override
    public int remove(Long id) {
        return wxShippingInfoUploadRecordMapper.remove(id);
    }
}