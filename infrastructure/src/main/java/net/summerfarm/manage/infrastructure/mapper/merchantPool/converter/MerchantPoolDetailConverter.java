package net.summerfarm.manage.infrastructure.mapper.merchantPool.converter;


import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolDetailEntity;
import net.summerfarm.manage.infrastructure.model.merchantpool.MerchantPoolDetail;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024-12-16 15:42:54
 * @version 1.0
 *
 */
public class MerchantPoolDetailConverter {

    private MerchantPoolDetailConverter() {
        // 无需实现
    }




    public static List<MerchantPoolDetailEntity> toMerchantPoolDetailEntityList(List<MerchantPoolDetail> merchantPoolDetailList) {
        if (merchantPoolDetailList == null) {
            return Collections.emptyList();
        }
        List<MerchantPoolDetailEntity> merchantPoolDetailEntityList = new ArrayList<>();
        for (MerchantPoolDetail merchantPoolDetail : merchantPoolDetailList) {
            merchantPoolDetailEntityList.add(toMerchantPoolDetailEntity(merchantPoolDetail));
        }
        return merchantPoolDetailEntityList;
    }

    public static MerchantPoolDetailEntity toMerchantPoolDetailEntity(MerchantPoolDetail merchantPoolDetail) {
        if (merchantPoolDetail == null) {
            return null;
        }
        MerchantPoolDetailEntity merchantPoolDetailEntity = new MerchantPoolDetailEntity();
        merchantPoolDetailEntity.setId(merchantPoolDetail.getId());
        merchantPoolDetailEntity.setPoolInfoId(merchantPoolDetail.getPoolInfoId());
        merchantPoolDetailEntity.setMId(merchantPoolDetail.getMId());
        merchantPoolDetailEntity.setSize(merchantPoolDetail.getSize());
        merchantPoolDetailEntity.setAreaNo(merchantPoolDetail.getAreaNo());
        merchantPoolDetailEntity.setVersion(merchantPoolDetail.getVersion());
        merchantPoolDetailEntity.setCreateTime(merchantPoolDetail.getCreateTime());
        merchantPoolDetailEntity.setUpdateTime(merchantPoolDetail.getUpdateTime());
        return merchantPoolDetailEntity;
    }
}
