package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.infrastructure.model.order.OrderItem;
import net.summerfarm.manage.infrastructure.mapper.order.OrderItemMapper;
import net.summerfarm.manage.infrastructure.converter.order.OrderItemConverter;
import net.summerfarm.manage.domain.order.repository.OrderItemCommandRepository;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import net.summerfarm.manage.domain.order.param.command.OrderItemCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-01-18 15:49:07
* @version 1.0
*
*/
@Repository
public class OrderItemCommandRepositoryImpl implements OrderItemCommandRepository {

    @Autowired
    private OrderItemMapper orderItemMapper;
    @Override
    public OrderItemEntity insertSelective(OrderItemCommandParam param) {
        OrderItem orderItem = OrderItemConverter.toOrderItem(param);
        orderItemMapper.insertSelective(orderItem);
        return OrderItemConverter.toOrderItemEntity(orderItem);
    }

    @Override
    public int updateSelectiveById(OrderItemCommandParam param){
        return orderItemMapper.updateSelectiveById(OrderItemConverter.toOrderItem(param));
    }


    @Override
    public int remove(Long id) {
        return orderItemMapper.remove(id);
    }
}