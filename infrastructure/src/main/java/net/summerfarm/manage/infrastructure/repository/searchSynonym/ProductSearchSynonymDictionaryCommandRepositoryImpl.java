package net.summerfarm.manage.infrastructure.repository.searchSynonym;

import net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary;
import net.summerfarm.manage.infrastructure.mapper.searchSynonym.ProductSearchSynonymDictionaryMapper;
import net.summerfarm.manage.infrastructure.converter.searchSynonym.ProductSearchSynonymDictionaryConverter;
import net.summerfarm.manage.domain.searchSynonym.repository.ProductSearchSynonymDictionaryCommandRepository;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.command.ProductSearchSynonymDictionaryCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-04-24 14:53:58
* @version 1.0
*
*/
@Repository
public class ProductSearchSynonymDictionaryCommandRepositoryImpl implements ProductSearchSynonymDictionaryCommandRepository {

    @Autowired
    private ProductSearchSynonymDictionaryMapper productSearchSynonymDictionaryMapper;
    @Override
    public ProductSearchSynonymDictionaryEntity insertSelective(ProductSearchSynonymDictionaryCommandParam param) {
        ProductSearchSynonymDictionary productSearchSynonymDictionary = ProductSearchSynonymDictionaryConverter.toProductSearchSynonymDictionary(param);
        productSearchSynonymDictionaryMapper.insertSelective(productSearchSynonymDictionary);
        return ProductSearchSynonymDictionaryConverter.toProductSearchSynonymDictionaryEntity(productSearchSynonymDictionary);
    }

    @Override
    public int updateSelectiveById(ProductSearchSynonymDictionaryCommandParam param){
        return productSearchSynonymDictionaryMapper.updateSelectiveById(ProductSearchSynonymDictionaryConverter.toProductSearchSynonymDictionary(param));
    }


    @Override
    public int remove(Integer id) {
        return productSearchSynonymDictionaryMapper.remove(id);
    }
}