package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import net.summerfarm.manage.infrastructure.model.product.InventoryRecord;
import net.summerfarm.manage.infrastructure.mapper.product.InventoryRecordMapper;
import net.summerfarm.manage.infrastructure.converter.product.InventoryRecordConverter;
import net.summerfarm.manage.domain.product.repository.InventoryRecordCommandRepository;
import net.summerfarm.manage.domain.product.entity.InventoryRecordEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryRecordCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-05-06 15:10:53
* @version 1.0
*
*/
@Repository
public class InventoryRecordCommandRepositoryImpl implements InventoryRecordCommandRepository {

    @Autowired
    private InventoryRecordMapper inventoryRecordMapper;
    @Override
    public InventoryRecordEntity insertSelective(InventoryRecordCommandParam param) {
        InventoryRecord inventoryRecord = InventoryRecordConverter.toInventoryRecord(param);
        inventoryRecordMapper.insertSelective(inventoryRecord);
        return InventoryRecordConverter.toInventoryRecordEntity(inventoryRecord);
    }

    @Override
    public int updateSelectiveById(InventoryRecordCommandParam param){
        return inventoryRecordMapper.updateSelectiveById(InventoryRecordConverter.toInventoryRecord(param));
    }


    @Override
    public int remove(Long id) {
        return inventoryRecordMapper.remove(id);
    }

    @Override
    public void saveBatch(List<InventoryRecordCommandParam> params) {
        List<InventoryRecord> inventoryRecords = InventoryRecordConverter.toInventoryRecords(params);
        inventoryRecordMapper.saveBatch(inventoryRecords);
    }
}