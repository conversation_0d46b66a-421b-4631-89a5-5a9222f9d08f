package net.summerfarm.manage.infrastructure.converter.order;

import net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import net.summerfarm.manage.domain.order.param.command.WxShippingInfoUploadRecordCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-10-15 17:49:41
 * @version 1.0
 *
 */
public class WxShippingInfoUploadRecordConverter {

    private WxShippingInfoUploadRecordConverter() {
        // 无需实现
    }




    public static List<WxShippingInfoUploadRecordEntity> toWxShippingInfoUploadRecordEntityList(List<WxShippingInfoUploadRecord> wxShippingInfoUploadRecordList) {
        if (wxShippingInfoUploadRecordList == null) {
            return Collections.emptyList();
        }
        List<WxShippingInfoUploadRecordEntity> wxShippingInfoUploadRecordEntityList = new ArrayList<>();
        for (WxShippingInfoUploadRecord wxShippingInfoUploadRecord : wxShippingInfoUploadRecordList) {
            wxShippingInfoUploadRecordEntityList.add(toWxShippingInfoUploadRecordEntity(wxShippingInfoUploadRecord));
        }
        return wxShippingInfoUploadRecordEntityList;
}


    public static WxShippingInfoUploadRecordEntity toWxShippingInfoUploadRecordEntity(WxShippingInfoUploadRecord wxShippingInfoUploadRecord) {
        if (wxShippingInfoUploadRecord == null) {
             return null;
        }
        WxShippingInfoUploadRecordEntity wxShippingInfoUploadRecordEntity = new WxShippingInfoUploadRecordEntity();
        wxShippingInfoUploadRecordEntity.setId(wxShippingInfoUploadRecord.getId());
        wxShippingInfoUploadRecordEntity.setMasterOrderNo(wxShippingInfoUploadRecord.getMasterOrderNo());
        wxShippingInfoUploadRecordEntity.setTransactionId(wxShippingInfoUploadRecord.getTransactionId());
        wxShippingInfoUploadRecordEntity.setStatus(wxShippingInfoUploadRecord.getStatus());
        wxShippingInfoUploadRecordEntity.setCreateTime(wxShippingInfoUploadRecord.getCreateTime());
        wxShippingInfoUploadRecordEntity.setUpdateTime(wxShippingInfoUploadRecord.getUpdateTime());
        return wxShippingInfoUploadRecordEntity;
    }








    public static WxShippingInfoUploadRecord toWxShippingInfoUploadRecord(WxShippingInfoUploadRecordCommandParam param) {
        if (param == null) {
            return null;
        }
        WxShippingInfoUploadRecord wxShippingInfoUploadRecord = new WxShippingInfoUploadRecord();
        wxShippingInfoUploadRecord.setId(param.getId());
        wxShippingInfoUploadRecord.setMasterOrderNo(param.getMasterOrderNo());
        wxShippingInfoUploadRecord.setTransactionId(param.getTransactionId());
        wxShippingInfoUploadRecord.setStatus(param.getStatus());
        wxShippingInfoUploadRecord.setCreateTime(param.getCreateTime());
        wxShippingInfoUploadRecord.setUpdateTime(param.getUpdateTime());
        return wxShippingInfoUploadRecord;
    }
}
