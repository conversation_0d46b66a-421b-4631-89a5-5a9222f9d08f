package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.MajorPrice;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-04-15 14:50:22
 * @version 1.0
 *
 */
public class MajorPriceConverter {

    private MajorPriceConverter() {
        // 无需实现
    }




    public static List<MajorPriceEntity> toMajorPriceEntityList(List<MajorPrice> majorPriceList) {
        if (majorPriceList == null) {
            return Collections.emptyList();
        }
        List<MajorPriceEntity> majorPriceEntityList = new ArrayList<>();
        for (MajorPrice majorPrice : majorPriceList) {
            majorPriceEntityList.add(toMajorPriceEntity(majorPrice));
        }
        return majorPriceEntityList;
}


    public static MajorPriceEntity toMajorPriceEntity(MajorPrice majorPrice) {
        if (majorPrice == null) {
             return null;
        }
        MajorPriceEntity majorPriceEntity = new MajorPriceEntity();
        majorPriceEntity.setId(majorPrice.getId());
        majorPriceEntity.setSku(majorPrice.getSku());
        majorPriceEntity.setPdName(majorPrice.getPdName());
        majorPriceEntity.setWeight(majorPrice.getWeight());
        majorPriceEntity.setAreaNo(majorPrice.getAreaNo());
        majorPriceEntity.setAdminId(majorPrice.getAdminId());
        majorPriceEntity.setPrice(majorPrice.getPrice());
        majorPriceEntity.setAreaName(majorPrice.getAreaName());
        majorPriceEntity.setDirect(majorPrice.getDirect());
        majorPriceEntity.setPayMethod(majorPrice.getPayMethod());
        majorPriceEntity.setValidTime(majorPrice.getValidTime());
        majorPriceEntity.setInvalidTime(majorPrice.getInvalidTime());
        majorPriceEntity.setMallShow(majorPrice.getMallShow());
        majorPriceEntity.setPriceType(majorPrice.getPriceType());
        majorPriceEntity.setCost(majorPrice.getCost());
        majorPriceEntity.setInterestRate(majorPrice.getInterestRate());
        majorPriceEntity.setFixedPrice(majorPrice.getFixedPrice());
        majorPriceEntity.setOriginalPrice(majorPrice.getOriginalPrice());
        majorPriceEntity.setUpdateTime(majorPrice.getUpdateTime());
        majorPriceEntity.setLowPriceUpdateTime(majorPrice.getLowPriceUpdateTime());
        majorPriceEntity.setLargeAreaNo(majorPrice.getLargeAreaNo());
        majorPriceEntity.setPriceAdjustmentValue(majorPrice.getPriceAdjustmentValue());
        majorPriceEntity.setRemark(majorPrice.getRemark());
        majorPriceEntity.setStatus(majorPrice.getStatus());
        return majorPriceEntity;
    }








    public static MajorPrice toMajorPrice(MajorPriceCommandParam param) {
        if (param == null) {
            return null;
        }
        MajorPrice majorPrice = new MajorPrice();
        majorPrice.setId(param.getId());
        majorPrice.setSku(param.getSku());
        majorPrice.setPdName(param.getPdName());
        majorPrice.setWeight(param.getWeight());
        majorPrice.setAreaNo(param.getAreaNo());
        majorPrice.setAdminId(param.getAdminId());
        majorPrice.setPrice(param.getPrice());
        majorPrice.setAreaName(param.getAreaName());
        majorPrice.setDirect(param.getDirect());
        majorPrice.setPayMethod(param.getPayMethod());
        majorPrice.setValidTime(param.getValidTime());
        majorPrice.setInvalidTime(param.getInvalidTime());
        majorPrice.setMallShow(param.getMallShow());
        majorPrice.setPriceType(param.getPriceType());
        majorPrice.setCost(param.getCost());
        majorPrice.setInterestRate(param.getInterestRate());
        majorPrice.setFixedPrice(param.getFixedPrice());
        majorPrice.setOriginalPrice(param.getOriginalPrice());
        majorPrice.setUpdateTime(param.getUpdateTime());
        majorPrice.setLowPriceUpdateTime(param.getLowPriceUpdateTime());
        majorPrice.setLargeAreaNo(param.getLargeAreaNo());
        majorPrice.setPriceAdjustmentValue(param.getPriceAdjustmentValue());
        majorPrice.setRemark(param.getRemark());
        majorPrice.setStatus(param.getStatus());
        return majorPrice;
    }
}
