package net.summerfarm.manage.infrastructure.converter.coupon;

import net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon;
import net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.MerchantCouponCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-05-31 15:37:46
 * @version 1.0
 *
 */
public class MerchantCouponConverter {

    private MerchantCouponConverter() {
        // 无需实现
    }




    public static List<MerchantCouponEntity> toMerchantCouponEntityList(List<MerchantCoupon> merchantCouponList) {
        if (merchantCouponList == null) {
            return Collections.emptyList();
        }
        List<MerchantCouponEntity> merchantCouponEntityList = new ArrayList<>();
        for (MerchantCoupon merchantCoupon : merchantCouponList) {
            merchantCouponEntityList.add(toMerchantCouponEntity(merchantCoupon));
        }
        return merchantCouponEntityList;
}


    public static MerchantCouponEntity toMerchantCouponEntity(MerchantCoupon merchantCoupon) {
        if (merchantCoupon == null) {
             return null;
        }
        MerchantCouponEntity merchantCouponEntity = new MerchantCouponEntity();
        merchantCouponEntity.setId(merchantCoupon.getId());
        merchantCouponEntity.setMId(merchantCoupon.getMId());
        merchantCouponEntity.setCouponId(merchantCoupon.getCouponId());
        merchantCouponEntity.setVaildDate(merchantCoupon.getVaildDate());
        merchantCouponEntity.setSender(merchantCoupon.getSender());
        merchantCouponEntity.setUsed(merchantCoupon.getUsed());
        merchantCouponEntity.setAddTime(merchantCoupon.getAddTime());
        merchantCouponEntity.setView(merchantCoupon.getView());
        merchantCouponEntity.setOrderNo(merchantCoupon.getOrderNo());
        merchantCouponEntity.setReceiveType(merchantCoupon.getReceiveType());
        merchantCouponEntity.setStartTime(merchantCoupon.getStartTime());
        merchantCouponEntity.setUpdateTime(merchantCoupon.getUpdateTime());
        merchantCouponEntity.setSendId(merchantCoupon.getSendId());
        return merchantCouponEntity;
    }








    public static MerchantCoupon toMerchantCoupon(MerchantCouponCommandParam param) {
        if (param == null) {
            return null;
        }
        MerchantCoupon merchantCoupon = new MerchantCoupon();
        merchantCoupon.setId(param.getId());
        merchantCoupon.setMId(param.getMId());
        merchantCoupon.setCouponId(param.getCouponId());
        merchantCoupon.setVaildDate(param.getVaildDate());
        merchantCoupon.setSender(param.getSender());
        merchantCoupon.setUsed(param.getUsed());
        merchantCoupon.setAddTime(param.getAddTime());
        merchantCoupon.setView(param.getView());
        merchantCoupon.setOrderNo(param.getOrderNo());
        merchantCoupon.setReceiveType(param.getReceiveType());
        merchantCoupon.setStartTime(param.getStartTime());
        merchantCoupon.setUpdateTime(param.getUpdateTime());
        merchantCoupon.setSendId(param.getSendId());
        merchantCoupon.setRelatedId(param.getRelatedId());
        return merchantCoupon;
    }
}
