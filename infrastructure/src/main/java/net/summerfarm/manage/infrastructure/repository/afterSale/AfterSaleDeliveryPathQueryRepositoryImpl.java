package net.summerfarm.manage.infrastructure.repository.afterSale;


import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;
import net.summerfarm.manage.infrastructure.mapper.afterSale.AfterSaleDeliveryPathMapper;
import net.summerfarm.manage.infrastructure.converter.afterSale.AfterSaleDeliveryPathConverter;
import net.summerfarm.manage.domain.afterSale.repository.AfterSaleDeliveryPathQueryRepository;
import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import net.summerfarm.manage.domain.afterSale.param.query.AfterSaleDeliveryPathQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-12-31 14:32:57
* @version 1.0
*
*/
@Repository
public class AfterSaleDeliveryPathQueryRepositoryImpl implements AfterSaleDeliveryPathQueryRepository {

    @Autowired
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;


    @Override
    public PageInfo<AfterSaleDeliveryPathEntity> getPage(AfterSaleDeliveryPathQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AfterSaleDeliveryPathEntity> entities = afterSaleDeliveryPathMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AfterSaleDeliveryPathEntity selectById(Long id) {
        return AfterSaleDeliveryPathConverter.toAfterSaleDeliveryPathEntity(afterSaleDeliveryPathMapper.selectById(id));
    }


    @Override
    public List<AfterSaleDeliveryPathEntity> selectByCondition(AfterSaleDeliveryPathQueryParam param) {
        return AfterSaleDeliveryPathConverter.toAfterSaleDeliveryPathEntityList(afterSaleDeliveryPathMapper.selectByCondition(param));
    }

    @Override
    public List<AfterSaleDeliveryPathFlatObject> queryValidAfterSaleDeliveryPathDetail(List<String> afterSaleNoList) {
        if(CollectionUtils.isEmpty(afterSaleNoList)){
            return Collections.emptyList();
        }
        return afterSaleDeliveryPathMapper.queryValidAfterSaleDeliveryPathDetail(afterSaleNoList);
    }

}