package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.infrastructure.model.order.OrderRelation;
import net.summerfarm.manage.infrastructure.mapper.order.OrderRelationMapper;
import net.summerfarm.manage.infrastructure.converter.order.OrderRelationConverter;
import net.summerfarm.manage.domain.order.repository.OrderRelationCommandRepository;
import net.summerfarm.manage.domain.order.entity.OrderRelationEntity;
import net.summerfarm.manage.domain.order.param.command.OrderRelationCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-01-23 16:02:29
* @version 1.0
*
*/
@Repository
public class OrderRelationCommandRepositoryImpl implements OrderRelationCommandRepository {

    @Autowired
    private OrderRelationMapper orderRelationMapper;
    @Override
    public OrderRelationEntity insertSelective(OrderRelationCommandParam param) {
        OrderRelation orderRelation = OrderRelationConverter.toOrderRelation(param);
        orderRelationMapper.insertSelective(orderRelation);
        return OrderRelationConverter.toOrderRelationEntity(orderRelation);
    }

    @Override
    public int updateSelectiveById(OrderRelationCommandParam param){
        return orderRelationMapper.updateSelectiveById(OrderRelationConverter.toOrderRelation(param));
    }


    @Override
    public int remove(Long id) {
        return orderRelationMapper.remove(id);
    }
}