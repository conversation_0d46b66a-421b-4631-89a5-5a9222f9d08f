package net.summerfarm.manage.infrastructure.repository.product;

import com.aliyun.odps.utils.StringUtils;
import net.summerfarm.manage.infrastructure.mapper.product.ExternalProductMappingMapper;
import net.summerfarm.util.ExceptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import net.summerfarm.manage.domain.product.repository.ExternalProductMappingCommandRepository;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ExternalProductMappingCommandParam;
import net.summerfarm.manage.infrastructure.converter.product.ExternalProductMappingConverter;
import net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping;

/**
*
* <AUTHOR>
* @date 2024-11-15 14:13:27
* @version 1.0
*
*/
@Repository
public class ExternalProductMappingCommandRepositoryImpl implements ExternalProductMappingCommandRepository {

    @Autowired
    private ExternalProductMappingMapper externalProductMappingMapper;
    @Override
    public ExternalProductMappingEntity insertSelective(ExternalProductMappingCommandParam param) {
        ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(param.getInternalValue())
                && StringUtils.isNotBlank(param.getExternalValue()), "商品编码不能为空");
        ExternalProductMapping externalProductMapping = ExternalProductMappingConverter.toExternalProductMapping(param);
        externalProductMappingMapper.insertSelective(externalProductMapping);
        return ExternalProductMappingConverter.toExternalProductMappingEntity(externalProductMapping);
    }

    @Override
    public int updateSelectiveById(ExternalProductMappingCommandParam param){
        return externalProductMappingMapper.updateSelectiveById(ExternalProductMappingConverter.toExternalProductMapping(param));
    }


    @Override
    public int remove(Long id) {
        return externalProductMappingMapper.remove(id);
    }
}