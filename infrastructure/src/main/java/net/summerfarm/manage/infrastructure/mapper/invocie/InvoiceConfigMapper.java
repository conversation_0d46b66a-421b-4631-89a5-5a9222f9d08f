package net.summerfarm.manage.infrastructure.mapper.invocie;

import net.summerfarm.manage.common.input.invoiceConfig.InvoiceConfigVO;
import net.summerfarm.manage.domain.invoice.entity.InvoiceConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface InvoiceConfigMapper {
    /**
     * 根据mid type查询发票信息
     *
     * @param invoiceConfigVO
     * @return
     */
    List<InvoiceConfig> selectByAdminIdsType(InvoiceConfigVO invoiceConfigVO);



    List<InvoiceConfig> selectByMajorAdminId(@Param("adminId")Long adminId);

    List<InvoiceConfig> selectByMajorByMids(@Param("mIds")List<Long> mIds);
}
