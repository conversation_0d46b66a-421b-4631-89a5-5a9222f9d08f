package net.summerfarm.manage.infrastructure.model.sampleApply;

import java.time.LocalDateTime;
import java.time.LocalDate;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-01-02 14:00:39
 * @version 1.0
 *
 */
@Data
public class SampleApply {
	/**
	 * 
	 */
	private Integer sampleId;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人id
	 */
	private Integer createId;

	/**
	 * 创建人名称
	 */
	private String createName;

	/**
	 * 式样用户id
	 */
	private Integer mId;

	/**
	 * 式样用户名称
	 */
	private String mName;

	/**
	 * 会员等级
	 */
	private Integer grade;

	/**
	 * 用户类型 单店，大客户
	 */
	private String mSize;

	/**
	 * 手机号
	 */
	private String mPhone;

	/**
	 * 联系人
	 */
	private String mContact;

	/**
	 * 式样用户收货地址id
	 */
	private Integer contactId;

	/**
	 * 客户所属用户bdid
	 */
	private Integer bdId;

	/**
	 * 客户归属bd名称
	 */
	private String bdName;

	/**
	 * 状态 0 待反馈 1 已反馈
	 */
	private Integer status;

	/**
	 * 客户满意度 0 未评价 1,2,3,4
	 */
	private Integer satisfaction;

	/**
	 * 客户购买意向
	 */
	private Integer purchaseIntention;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 所属城市编号
	 */
	private Integer areaNo;

	/**
	 * 样品配送时间
	 */
	private LocalDate deliveryTime;

	/**
	 * 配送仓编号
	 */
	private Integer storeNo;

	/**
	 * 风险识别 0正常 1风控
	 */
	private Integer riskLevel;



}