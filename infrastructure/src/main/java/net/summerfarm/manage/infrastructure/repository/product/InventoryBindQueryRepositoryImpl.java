package net.summerfarm.manage.infrastructure.repository.product;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam;
import net.summerfarm.manage.domain.product.repository.InventoryBindQueryRepository;
import net.summerfarm.manage.infrastructure.converter.product.InventoryBindConverter;
import net.summerfarm.manage.infrastructure.mapper.product.InventoryBindMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-06 13:48:40
* @version 1.0
*
*/
@Repository
public class InventoryBindQueryRepositoryImpl implements InventoryBindQueryRepository {

    @Autowired
    private InventoryBindMapper inventoryBindMapper;


    @Override
    public PageInfo<InventoryBindEntity> getPage(InventoryBindQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<InventoryBindEntity> entities = inventoryBindMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public InventoryBindEntity selectById(Long id) {
        return InventoryBindConverter.toInventoryBindEntity(inventoryBindMapper.selectById(id));
    }


    @Override
    public List<InventoryBindEntity> selectByCondition(InventoryBindQueryParam param) {
        return InventoryBindConverter.toInventoryBindEntityList(inventoryBindMapper.selectByCondition(param));
    }

    @Override
    public InventoryBindEntity selectOneByCondition(InventoryBindQueryParam bindQueryParam) {
        return InventoryBindConverter.toInventoryBindEntity(inventoryBindMapper.selectOneByCondition(bindQueryParam));
    }

    @Override
    public InventoryBindEntity selectByBindSkuAndExtType(InventoryBindQueryParam bindQueryParam) {
        return InventoryBindConverter.toInventoryBindEntity(inventoryBindMapper.selectByBindSkuAndExtType(bindQueryParam));
    }

    @Override
    public List<InventoryBindEntity> selectByPdIdAndExtType(InventoryBindQueryParam bindQueryParam) {
        return InventoryBindConverter.toInventoryBindEntityList(inventoryBindMapper.selectByPdIdAndExtType(bindQueryParam));
    }
}