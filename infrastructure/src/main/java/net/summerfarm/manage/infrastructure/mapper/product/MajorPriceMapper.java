package net.summerfarm.manage.infrastructure.mapper.product;

import java.util.List;
import java.util.Set;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceFlatObject;
import net.summerfarm.manage.domain.major.flatobject.MajorPriceItemFlatObject;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.entity.MajorPriceLowRemainder;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import net.summerfarm.manage.domain.product.param.query.MajorPricePageQueryParam;
import net.summerfarm.manage.domain.product.param.query.MajorPriceQueryParam;
import net.summerfarm.manage.infrastructure.model.product.MajorPrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Mapper
public interface MajorPriceMapper {

    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MajorPrice record);
    void insertBatch(List<MajorPrice> list);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(MajorPrice record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MajorPrice selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<MajorPrice> selectByCondition(MajorPriceQueryParam param);







    /**
     * 查询报价单价格区间
     * @param adminId adminId
     * @param sku sku
     * @return 价格区间
     */
    List<MajorPriceEntity> queryListMajorPrice(@Param("adminId") Integer adminId, @Param("sku") String sku, @Param("areaNos") List<Integer> areaNos);
    List<MajorPriceEntity> queryListMajorPriceWithoutTime(@Param("direct")Integer direct,@Param("adminId")Long adminId, @Param("skus") Set<String> skus, @Param("areaNos") Set<Integer> areaNos);

    /**
     * 查询大客户指定城市是否有低价数据
     * @param adminId
     * @param areaNo
     * @param sku
     * @return
     */
    List<MajorPriceLowRemainder> selectLowPriceRemainderSku(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("sku") String sku);

    /**
     * 根据类型查询大客户报价单
     * @param adminId
     * @param direct
     * @param areaNo
     * @param sku
     * @return
     */
    MajorPriceEntity selectMajorPrice(@Param("adminId")Long adminId,@Param("direct") Integer direct, @Param("areaNo")Integer areaNo,@Param("sku") String sku);

    /**
     * 根据id批量更新mallshow
     * @param ids
     */
    void updateMallShowByIds(@Param("ids")List<Integer> ids,@Param("mallShow")Integer mallShow);

    void updateBatch(List<MajorPriceCommandParam> list);

    void removeByIds(@Param("ids")List<Integer> ids);

    List<MajorPriceEntity> queryListMajorPriceByIds(@Param("ids")List<Long> ids);

    MajorPriceEntity selectLastCommitMajorPrice(@Param("adminId")Long adminId);

    List<MajorPriceFlatObject> selectMajorPriceList(MajorPricePageQueryParam param);

    List<MajorPriceItemFlatObject> selectMajorPriceCityList(MajorPricePageQueryParam param);

    List<MajorPriceItemFlatObject> selectMajorPriceDownloadList(MajorPricePageQueryParam param);

    void commitBatch(@Param("ids")List<Long> ids);

    void deleteBySku(@Param("sku")String sku, @Param("adminId")Integer adminId, @Param("direct")Integer direct);
}
