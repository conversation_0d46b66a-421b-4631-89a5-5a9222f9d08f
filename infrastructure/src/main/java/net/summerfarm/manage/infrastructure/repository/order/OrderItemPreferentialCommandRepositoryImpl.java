package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential;
import net.summerfarm.manage.infrastructure.mapper.order.OrderItemPreferentialMapper;
import net.summerfarm.manage.infrastructure.converter.order.OrderItemPreferentialConverter;
import net.summerfarm.manage.domain.order.repository.OrderItemPreferentialCommandRepository;
import net.summerfarm.manage.domain.order.entity.OrderItemPreferentialEntity;
import net.summerfarm.manage.domain.order.param.command.OrderItemPreferentialCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-31 15:06:17
* @version 1.0
*
*/
@Repository
public class OrderItemPreferentialCommandRepositoryImpl implements OrderItemPreferentialCommandRepository {

    @Autowired
    private OrderItemPreferentialMapper orderItemPreferentialMapper;
    @Override
    public OrderItemPreferentialEntity insertSelective(OrderItemPreferentialCommandParam param) {
        OrderItemPreferential orderItemPreferential = OrderItemPreferentialConverter.toOrderItemPreferential(param);
        orderItemPreferentialMapper.insertSelective(orderItemPreferential);
        return OrderItemPreferentialConverter.toOrderItemPreferentialEntity(orderItemPreferential);
    }

    @Override
    public int updateSelectiveById(OrderItemPreferentialCommandParam param){
        return orderItemPreferentialMapper.updateSelectiveById(OrderItemPreferentialConverter.toOrderItemPreferential(param));
    }


    @Override
    public int remove(Long id) {
        return orderItemPreferentialMapper.remove(id);
    }
}