package net.summerfarm.manage.infrastructure.repository.product;


import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.infrastructure.converter.product.AppPopBiaoguoProductsDfConverter;
import net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoProductsDf;
import net.summerfarm.manage.domain.product.repository.AppPopBiaoguoProductsDfQueryRepository;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.infrastructure.offlinemapper.AppPopBiaoguoProductsDfMapper;
import net.summerfarm.util.ExceptionUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2024-12-12 11:19:19
* @version 1.0
*
*/
@Repository
public class AppPopBiaoguoProductsDfQueryRepositoryImpl implements AppPopBiaoguoProductsDfQueryRepository {

    @Resource
    private AppPopBiaoguoProductsDfMapper appPopBiaoguoProductsDfMapper;


    @Override
    public PageInfo<AppPopBiaoguoProductsDfEntity> getPage(AppPopBiaoguoProductsDfQueryParam param) {
        ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(param.getDs()), "请传递日期分区参数");
        ExceptionUtil.checkAndThrow(Objects.nonNull(param.getPageIndex())
                && Objects.nonNull(param.getPageSize()), "分页参数不能为空");
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AppPopBiaoguoProductsDfEntity> entities = appPopBiaoguoProductsDfMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public AppPopBiaoguoProductsDfEntity selectById(Long id) {
        return AppPopBiaoguoProductsDfConverter.toAppPopBiaoguoProductsDfEntity(appPopBiaoguoProductsDfMapper.selectById(id));
    }


    @Override
    public List<AppPopBiaoguoProductsDfEntity> selectByCondition(AppPopBiaoguoProductsDfQueryParam param) {
        ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(param.getDs()), "请传递日期分区参数");
        return AppPopBiaoguoProductsDfConverter.toAppPopBiaoguoProductsDfEntityList(appPopBiaoguoProductsDfMapper.selectByCondition(param));
    }

    @Override
    public boolean exist(AppPopBiaoguoProductsDfQueryParam productsDfQueryParam) {
        return appPopBiaoguoProductsDfMapper.exist(productsDfQueryParam) == 1;
    }

    @Override
    public int count(AppPopBiaoguoProductsDfQueryParam productsDfQueryParam) {
        if (StringUtils.isBlank(productsDfQueryParam.getDs())) {
            return 0;
        }
        return appPopBiaoguoProductsDfMapper.count(productsDfQueryParam);
    }

}