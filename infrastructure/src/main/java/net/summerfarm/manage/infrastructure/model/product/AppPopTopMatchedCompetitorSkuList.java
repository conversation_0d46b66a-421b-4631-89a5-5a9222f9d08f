package net.summerfarm.manage.infrastructure.model.product;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
@Data
public class AppPopTopMatchedCompetitorSkuList {
	/**
	 * Unique ID (auto increment starting from 10000)
	 */
	private Long id;

	/**
	 * POP SKU编码
	 */
	private String sku;

	/**
	 * POP商品规格
	 */
	private String weight;

	/**
	 * 当前商品的SKU名称(pd_name)
	 */
	private String skuName;

	/**
	 * 商品的产品名称
	 */
	private String pdName;

	/**
	 * 与当前商品匹配的竞争对手SKU列表（JSONArray格式）
	 */
	private String topMatchedCompetitorSkuList;

	/**
	 * 商品所属类目
	 */
	private String category;

	/**
	 * 商品的总成交金额（GMV）
	 */
	private BigDecimal gmv;

	/**
	 * 商品的订单数量
	 */
	private Long orderCnt;

	/**
	 * 商品的总销售件数
	 */
	private Long orderQuantity;

	/**
	 * 商品的最近下单时间
	 */
	private LocalDateTime lastOrderTime;

	/**
	 * 数据创建时间，格式为字符串
	 */
	private String createTime;

	/**
	 * 数据分区字段，表示日期（例如20240101）
	 */
	private String ds;



}