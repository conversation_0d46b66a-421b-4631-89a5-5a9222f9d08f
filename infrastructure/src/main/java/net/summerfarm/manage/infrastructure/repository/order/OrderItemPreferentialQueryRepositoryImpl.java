package net.summerfarm.manage.infrastructure.repository.order;


import net.summerfarm.manage.infrastructure.model.order.OrderItemPreferential;
import net.summerfarm.manage.infrastructure.mapper.order.OrderItemPreferentialMapper;
import net.summerfarm.manage.infrastructure.converter.order.OrderItemPreferentialConverter;
import net.summerfarm.manage.domain.order.repository.OrderItemPreferentialQueryRepository;
import net.summerfarm.manage.domain.order.entity.OrderItemPreferentialEntity;
import net.summerfarm.manage.domain.order.param.query.OrderItemPreferentialQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-31 15:06:17
* @version 1.0
*
*/
@Repository
public class OrderItemPreferentialQueryRepositoryImpl implements OrderItemPreferentialQueryRepository {

    @Autowired
    private OrderItemPreferentialMapper orderItemPreferentialMapper;


    @Override
    public PageInfo<OrderItemPreferentialEntity> getPage(OrderItemPreferentialQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<OrderItemPreferentialEntity> entities = orderItemPreferentialMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public OrderItemPreferentialEntity selectById(Long id) {
        return OrderItemPreferentialConverter.toOrderItemPreferentialEntity(orderItemPreferentialMapper.selectById(id));
    }


    @Override
    public List<OrderItemPreferentialEntity> selectByCondition(OrderItemPreferentialQueryParam param) {
        return OrderItemPreferentialConverter.toOrderItemPreferentialEntityList(orderItemPreferentialMapper.selectByCondition(param));
    }

}