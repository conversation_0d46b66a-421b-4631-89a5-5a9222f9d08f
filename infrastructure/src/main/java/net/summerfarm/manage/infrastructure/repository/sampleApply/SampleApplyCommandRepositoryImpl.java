package net.summerfarm.manage.infrastructure.repository.sampleApply;

import net.summerfarm.manage.infrastructure.model.sampleApply.SampleApply;
import net.summerfarm.manage.infrastructure.mapper.sampleApply.SampleApplyMapper;
import net.summerfarm.manage.infrastructure.converter.sampleApply.SampleApplyConverter;
import net.summerfarm.manage.domain.sampleApply.repository.SampleApplyCommandRepository;
import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import net.summerfarm.manage.domain.sampleApply.param.command.SampleApplyCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-01-02 14:00:39
* @version 1.0
*
*/
@Repository
public class SampleApplyCommandRepositoryImpl implements SampleApplyCommandRepository {

    @Autowired
    private SampleApplyMapper sampleApplyMapper;
    @Override
    public SampleApplyEntity insertSelective(SampleApplyCommandParam param) {
        SampleApply sampleApply = SampleApplyConverter.toSampleApply(param);
        sampleApplyMapper.insertSelective(sampleApply);
        return SampleApplyConverter.toSampleApplyEntity(sampleApply);
    }

    @Override
    public int updateSelectiveById(SampleApplyCommandParam param){
        return sampleApplyMapper.updateSelectiveById(SampleApplyConverter.toSampleApply(param));
    }


    @Override
    public int remove(Long sampleId) {
        return sampleApplyMapper.remove(sampleId);
    }
}