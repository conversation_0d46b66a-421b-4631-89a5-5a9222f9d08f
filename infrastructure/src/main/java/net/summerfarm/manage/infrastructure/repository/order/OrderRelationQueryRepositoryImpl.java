package net.summerfarm.manage.infrastructure.repository.order;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.order.entity.OrderRelationEntity;
import net.summerfarm.manage.domain.order.param.query.OrderRelationQueryParam;
import net.summerfarm.manage.domain.order.repository.OrderRelationQueryRepository;
import net.summerfarm.manage.infrastructure.converter.order.OrderRelationConverter;
import net.summerfarm.manage.infrastructure.mapper.order.OrderRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-01-23 16:02:29
* @version 1.0
*
*/
@Repository
public class OrderRelationQueryRepositoryImpl implements OrderRelationQueryRepository {

    @Autowired
    private OrderRelationMapper orderRelationMapper;


    @Override
    public PageInfo<OrderRelationEntity> getPage(OrderRelationQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<OrderRelationEntity> entities = orderRelationMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public OrderRelationEntity selectById(Long id) {
        return OrderRelationConverter.toOrderRelationEntity(orderRelationMapper.selectById(id));
    }


    @Override
    public List<OrderRelationEntity> selectByCondition(OrderRelationQueryParam param) {
        return OrderRelationConverter.toOrderRelationEntityList(orderRelationMapper.selectByCondition(param));
    }

    @Override
    public List<OrderRelationEntity> selectByOrderNoBatch(List<String> orders) {
        return OrderRelationConverter.toOrderRelationEntityList(orderRelationMapper.selectByOrderNoBatch(orders));
    }

    @Override
    public List<String> selectOrderNoByMasterOrderNo(String masterOrderNo) {
        return orderRelationMapper.selectOrderNoByMasterOrderNo(masterOrderNo);
    }
}