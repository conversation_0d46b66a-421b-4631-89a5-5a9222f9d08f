package net.summerfarm.manage.infrastructure.repository.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantAccountTransfer;
import net.summerfarm.manage.infrastructure.mapper.merchant.MerchantAccountTransferMapper;
import net.summerfarm.manage.infrastructure.converter.merchant.MerchantAccountTransferConverter;
import net.summerfarm.manage.domain.merchant.repository.MerchantAccountTransferCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantAccountTransferCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-01-10 14:07:22
* @version 1.0
*
*/
@Repository
public class MerchantAccountTransferCommandRepositoryImpl implements MerchantAccountTransferCommandRepository {

    @Autowired
    private MerchantAccountTransferMapper merchantAccountTransferMapper;
    @Override
    public MerchantAccountTransferEntity insertSelective(MerchantAccountTransferCommandParam param) {
        MerchantAccountTransfer merchantAccountTransfer = MerchantAccountTransferConverter.toMerchantAccountTransfer(param);
        merchantAccountTransferMapper.insertSelective(merchantAccountTransfer);
        return MerchantAccountTransferConverter.toMerchantAccountTransferEntity(merchantAccountTransfer);
    }

}