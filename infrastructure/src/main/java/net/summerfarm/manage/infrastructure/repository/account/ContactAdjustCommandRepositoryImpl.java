package net.summerfarm.manage.infrastructure.repository.account;

import net.summerfarm.manage.infrastructure.model.account.ContactAdjust;
import net.summerfarm.manage.infrastructure.mapper.account.ContactAdjustMapper;
import net.summerfarm.manage.infrastructure.converter.account.ContactAdjustConverter;
import net.summerfarm.manage.domain.account.repository.ContactAdjustCommandRepository;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:20
* @version 1.0
*
*/
@Repository
public class ContactAdjustCommandRepositoryImpl implements ContactAdjustCommandRepository {

    @Autowired
    private ContactAdjustMapper contactAdjustMapper;

    @Override
    public ContactAdjustEntity insertSelective(ContactAdjustEntity entity) {
        ContactAdjust contactAdjust = ContactAdjustConverter.toContactAdjust(entity);
        contactAdjustMapper.insertSelective(contactAdjust);
        return ContactAdjustConverter.toContactAdjustEntity(contactAdjust);
    }

    @Override
    public int updateByIdSelective(ContactAdjustEntity entity){
        return contactAdjustMapper.updateByIdSelective(ContactAdjustConverter.toContactAdjust(entity));
    }


    @Override
    public int remove(Long id) {
        return contactAdjustMapper.remove(id);
    }

}