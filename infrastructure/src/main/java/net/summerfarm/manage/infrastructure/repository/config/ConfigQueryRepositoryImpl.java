package net.summerfarm.manage.infrastructure.repository.config;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.infrastructure.model.config.Config;
import net.summerfarm.manage.infrastructure.mapper.config.ConfigMapper;
import net.summerfarm.manage.infrastructure.converter.config.ConfigConverter;
import net.summerfarm.manage.domain.config.repository.ConfigQueryRepository;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.param.query.ConfigQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-18 23:48:54
 */
@Repository
@Slf4j
public class ConfigQueryRepositoryImpl implements ConfigQueryRepository {

    @Autowired
    private ConfigMapper configMapper;


    @Override
    public PageInfo<ConfigEntity> getPage(ConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ConfigEntity> entities = configMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ConfigEntity selectById(Long id) {
        return ConfigConverter.toConfigEntity(configMapper.selectById(id));
    }

    @Override
    public ConfigEntity selectByKey(String key) {
        ConfigQueryParam params = new ConfigQueryParam();
        params.setKey(key);
        List<ConfigEntity> entityList = selectByCondition(params);
        if (CollectionUtils.isEmpty(entityList)) {
            log.warn("找不到配置项:{}", key);
            return null;
        }
        return entityList.get(0);
    }


    @Override
    public List<ConfigEntity> selectByCondition(ConfigQueryParam param) {
        log.info("统计config表的使用记录:{}", param);
        return ConfigConverter.toConfigEntityList(configMapper.selectByCondition(param));
    }

}