package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.command.AreaSkuOnSaleParam;
import net.summerfarm.manage.domain.product.param.command.AreaSkuPriceCommandParam;
import net.summerfarm.manage.domain.product.param.query.AreaSkuQueryParam;
import net.summerfarm.manage.infrastructure.model.product.AreaSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.Set;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Mapper
public interface AreaSkuMapper {

    /**
     * 插入区域SKU
     */
    int insertSelective(AreaSku areaSku);

    List<AreaSku> queryListSkuPrice(@Param("skus") List<String> skus, @Param("areaNos") List<Integer> areaNos, @Param("onsale") Boolean onsale);

    AreaSkuEntity selectValidAndOnSale(@Param("sku") String sku, @Param("areaNo") Integer areaNo);
    List<AreaSkuEntity> selectVOList(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    Integer updateAreaSkuPrice(@Param("list") List<AreaSkuPriceCommandParam> areaSkuPriceS);

    /**
     * pop商品更新上下架
     * @param areaSkuOnSaleParam
     * @return
     */
    Integer updateAreaSkuPopOnSale(@Param("param") AreaSkuOnSaleParam areaSkuOnSaleParam);

    void offSaleByIds(@Param("ids")List<Integer> ids);

    void updateAreaSkuOnSaleBatch(@Param("list")List<AreaSkuOnSaleParam> areaSkuOnSaleParams);

    List<AreaSku> queryAreaSkuBySkuAndAreaNoList(@Param("list") List<AreaSkuQueryParam> param);

    /**
     * 分页查询指定区域的上架商品ID
     *
     * @param areaNos 区域编号集合
     * @return 上架商品ID列表
     */
    List<Integer> pageOnsaleIdsByAreaNo(@Param("areaNos") Set<Integer> areaNos);

    void updateAreaSkuOnSaleBatchBySku(@Param("onSale")Boolean onSale, @Param("skus") Collection<String> skus);

    void offSaleBySkus(@Param("skus")List<String> skus);
}
