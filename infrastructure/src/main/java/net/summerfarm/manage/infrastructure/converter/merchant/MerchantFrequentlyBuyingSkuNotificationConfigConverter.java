package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuNotificationConfigCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
public class MerchantFrequentlyBuyingSkuNotificationConfigConverter {

    private MerchantFrequentlyBuyingSkuNotificationConfigConverter() {
        // 无需实现
    }




    public static List<MerchantFrequentlyBuyingSkuNotificationConfigEntity> toMerchantFrequentlyBuyingSkuNotificationConfigEntityList(List<MerchantFrequentlyBuyingSkuNotificationConfig> merchantFrequentlyBuyingSkuNotificationConfigList) {
        if (merchantFrequentlyBuyingSkuNotificationConfigList == null) {
            return Collections.emptyList();
        }
        List<MerchantFrequentlyBuyingSkuNotificationConfigEntity> merchantFrequentlyBuyingSkuNotificationConfigEntityList = new ArrayList<>();
        for (MerchantFrequentlyBuyingSkuNotificationConfig merchantFrequentlyBuyingSkuNotificationConfig : merchantFrequentlyBuyingSkuNotificationConfigList) {
            merchantFrequentlyBuyingSkuNotificationConfigEntityList.add(toMerchantFrequentlyBuyingSkuNotificationConfigEntity(merchantFrequentlyBuyingSkuNotificationConfig));
        }
        return merchantFrequentlyBuyingSkuNotificationConfigEntityList;
}


    public static MerchantFrequentlyBuyingSkuNotificationConfigEntity toMerchantFrequentlyBuyingSkuNotificationConfigEntity(MerchantFrequentlyBuyingSkuNotificationConfig merchantFrequentlyBuyingSkuNotificationConfig) {
        if (merchantFrequentlyBuyingSkuNotificationConfig == null) {
             return null;
        }
        MerchantFrequentlyBuyingSkuNotificationConfigEntity merchantFrequentlyBuyingSkuNotificationConfigEntity = new MerchantFrequentlyBuyingSkuNotificationConfigEntity();
        merchantFrequentlyBuyingSkuNotificationConfigEntity.setId(merchantFrequentlyBuyingSkuNotificationConfig.getId());
        merchantFrequentlyBuyingSkuNotificationConfigEntity.setCreateTime(merchantFrequentlyBuyingSkuNotificationConfig.getCreateTime());
        merchantFrequentlyBuyingSkuNotificationConfigEntity.setUpdateTime(merchantFrequentlyBuyingSkuNotificationConfig.getUpdateTime());
        merchantFrequentlyBuyingSkuNotificationConfigEntity.setMId(merchantFrequentlyBuyingSkuNotificationConfig.getMId());
        merchantFrequentlyBuyingSkuNotificationConfigEntity.setSpecialOffer(merchantFrequentlyBuyingSkuNotificationConfig.getSpecialOffer());
        merchantFrequentlyBuyingSkuNotificationConfigEntity.setGoodsArrived(merchantFrequentlyBuyingSkuNotificationConfig.getGoodsArrived());
        return merchantFrequentlyBuyingSkuNotificationConfigEntity;
    }








    public static MerchantFrequentlyBuyingSkuNotificationConfig toMerchantFrequentlyBuyingSkuNotificationConfig(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        MerchantFrequentlyBuyingSkuNotificationConfig merchantFrequentlyBuyingSkuNotificationConfig = new MerchantFrequentlyBuyingSkuNotificationConfig();
        merchantFrequentlyBuyingSkuNotificationConfig.setId(param.getId());
        merchantFrequentlyBuyingSkuNotificationConfig.setCreateTime(param.getCreateTime());
        merchantFrequentlyBuyingSkuNotificationConfig.setUpdateTime(param.getUpdateTime());
        merchantFrequentlyBuyingSkuNotificationConfig.setMId(param.getMId());
        merchantFrequentlyBuyingSkuNotificationConfig.setSpecialOffer(param.getSpecialOffer());
        merchantFrequentlyBuyingSkuNotificationConfig.setGoodsArrived(param.getGoodsArrived());
        return merchantFrequentlyBuyingSkuNotificationConfig;
    }
}
