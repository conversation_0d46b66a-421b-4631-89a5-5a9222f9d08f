package net.summerfarm.manage.infrastructure.mapper.account;

import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.infrastructure.model.account.AccountChange;
import net.summerfarm.manage.common.input.account.AccountChangeQueryInput;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-10-26 16:20:19
 * @version 1.0
 *
 */
@Mapper
public interface AccountChangeMapper{

    int insertSelective(AccountChange record);

    int updateByIdSelective(AccountChange record);

    int remove(@Param("id") Long id);

    AccountChange selectById(@Param("id") Long id);

    List<AccountChange> selectByCondition(MerchantQueryInput record);


    AccountChange selectOne(AccountChange record);

}
