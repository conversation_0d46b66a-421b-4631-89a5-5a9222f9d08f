package net.summerfarm.manage.infrastructure.mapper.order;

import net.summerfarm.manage.infrastructure.model.order.AfterSaleProof;
import net.summerfarm.manage.domain.order.param.query.AfterSaleProofQueryParam;
import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-01-18 16:27:13
 * @version 1.0
 *
 */
@Mapper
public interface AfterSaleProofMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(AfterSaleProof record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(AfterSaleProof record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AfterSaleProof selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<AfterSaleProof> selectByCondition(AfterSaleProofQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<AfterSaleProofEntity> getPage(AfterSaleProofQueryParam param);
}

