package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.domain.product.param.query.ExternalProductMappingQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import net.summerfarm.manage.infrastructure.model.product.ExternalProductMapping;

/**
 *
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Mapper
public interface ExternalProductMappingMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ExternalProductMapping record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ExternalProductMapping record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ExternalProductMapping selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<ExternalProductMapping> selectByCondition(ExternalProductMappingQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ExternalProductMappingEntity> getPage(ExternalProductMappingQueryParam param);

    /**
     * 查询未被绑定的鲜沐pop商品
     *  1、sku类型为pop
     *  2、当前买手商品
     *  3、生效sku
     *  4、任一区域上架
     *
     * <AUTHOR>
     * @date 2024/11/19 15:18
     */
    List<ExternalProductMappingEntity> getPageUnmapped(ExternalProductMappingQueryParam queryParam);

    /**
     * 查询未被绑定的鲜沐pop商品类目
     *
     * <AUTHOR>
     * @date 2024/11/20 15:45
     */
    List<CategoryEntity> getListUnmappedCategory(ExternalProductMappingQueryParam queryParam);

    /**
     * 查询已被绑定的鲜沐pop商品
     *
     * <AUTHOR>
     * @date 2024/11/19 15:18
     */
    List<ExternalProductMappingEntity> getPageMapped(ExternalProductMappingQueryParam queryParam);

    /**
     * 查询已被绑定的鲜沐pop商品类目
     *
     * <AUTHOR>
     * @date 2024/11/20 15:45
     */
    List<CategoryEntity> getListMappedCategory(ExternalProductMappingQueryParam queryParam);

    /**
     * 查询已绑定sku列表
     *
     * <AUTHOR>
     * @date 2024/12/17 15:38
     * @param type 类型
     * @return java.util.List<java.lang.String> sku列表
     */
    List<String> getListExternalValueList(@Param("type") Integer type);
}

