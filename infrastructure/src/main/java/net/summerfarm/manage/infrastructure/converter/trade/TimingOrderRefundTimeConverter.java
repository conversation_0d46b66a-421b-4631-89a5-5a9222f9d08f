package net.summerfarm.manage.infrastructure.converter.trade;

import net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import net.summerfarm.manage.domain.trade.param.command.TimingOrderRefundTimeCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-01-22 14:45:54
 * @version 1.0
 *
 */
public class TimingOrderRefundTimeConverter {

    private TimingOrderRefundTimeConverter() {
        // 无需实现
    }




    public static List<TimingOrderRefundTimeEntity> toTimingOrderRefundTimeEntityList(List<TimingOrderRefundTime> timingOrderRefundTimeList) {
        if (timingOrderRefundTimeList == null) {
            return Collections.emptyList();
        }
        List<TimingOrderRefundTimeEntity> timingOrderRefundTimeEntityList = new ArrayList<>();
        for (TimingOrderRefundTime timingOrderRefundTime : timingOrderRefundTimeList) {
            timingOrderRefundTimeEntityList.add(toTimingOrderRefundTimeEntity(timingOrderRefundTime));
        }
        return timingOrderRefundTimeEntityList;
}


    public static TimingOrderRefundTimeEntity toTimingOrderRefundTimeEntity(TimingOrderRefundTime timingOrderRefundTime) {
        if (timingOrderRefundTime == null) {
             return null;
        }
        TimingOrderRefundTimeEntity timingOrderRefundTimeEntity = new TimingOrderRefundTimeEntity();
        timingOrderRefundTimeEntity.setId(timingOrderRefundTime.getId());
        timingOrderRefundTimeEntity.setOrderNo(timingOrderRefundTime.getOrderNo());
        timingOrderRefundTimeEntity.setRefundTime(timingOrderRefundTime.getRefundTime());
        timingOrderRefundTimeEntity.setMId(timingOrderRefundTime.getMId());
        timingOrderRefundTimeEntity.setCreateTime(timingOrderRefundTime.getCreateTime());
        timingOrderRefundTimeEntity.setUpdateTime(timingOrderRefundTime.getUpdateTime());
        return timingOrderRefundTimeEntity;
    }








    public static TimingOrderRefundTime toTimingOrderRefundTime(TimingOrderRefundTimeCommandParam param) {
        if (param == null) {
            return null;
        }
        TimingOrderRefundTime timingOrderRefundTime = new TimingOrderRefundTime();
        timingOrderRefundTime.setId(param.getId());
        timingOrderRefundTime.setOrderNo(param.getOrderNo());
        timingOrderRefundTime.setRefundTime(param.getRefundTime());
        timingOrderRefundTime.setMId(param.getMId());
        timingOrderRefundTime.setCreateTime(param.getCreateTime());
        timingOrderRefundTime.setUpdateTime(param.getUpdateTime());
        return timingOrderRefundTime;
    }
}
