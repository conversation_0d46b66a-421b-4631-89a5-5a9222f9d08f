package net.summerfarm.manage.infrastructure.mapper.marketItem;

import net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt;
import net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
@Mapper
public interface MarketItemAiExtMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MarketItemAiExt record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(MarketItemAiExt record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MarketItemAiExt selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<MarketItemAiExt> selectByCondition(MarketItemAiExtQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<MarketItemAiExtEntity> getPage(MarketItemAiExtQueryParam param);
}

