package net.summerfarm.manage.infrastructure.offlinemapper;

import net.summerfarm.manage.domain.offline.entity.CustGradeEntity;
import net.summerfarm.manage.infrastructure.model.offline.CustGrade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-10-24 11:26:16
 * @version 1.0
 *
 */
@Mapper
public interface CustGradeMapper{


    /**
     * @Describe: 查询待处理的门店数据
     * @param param
     * @return
     */
    List<CustGradeEntity> selectTaskDataList(@Param("dateTag") String dateTag, @Param("startId") Long startId, @Param("offset") int offset);
}

