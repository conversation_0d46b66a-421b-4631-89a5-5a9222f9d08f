package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.FrontCategory;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.domain.product.param.command.FrontCategoryCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 *
 */
public class FrontCategoryConverter {

    private FrontCategoryConverter() {
        // 无需实现
    }




    public static List<FrontCategoryEntity> toFrontCategoryEntityList(List<FrontCategory> frontCategoryList) {
        if (frontCategoryList == null) {
            return Collections.emptyList();
        }
        List<FrontCategoryEntity> frontCategoryEntityList = new ArrayList<>();
        for (FrontCategory frontCategory : frontCategoryList) {
            frontCategoryEntityList.add(toFrontCategoryEntity(frontCategory));
        }
        return frontCategoryEntityList;
}


    public static FrontCategoryEntity toFrontCategoryEntity(FrontCategory frontCategory) {
        if (frontCategory == null) {
             return null;
        }
        FrontCategoryEntity frontCategoryEntity = new FrontCategoryEntity();
        frontCategoryEntity.setId(frontCategory.getId());
        frontCategoryEntity.setParentId(frontCategory.getParentId());
        frontCategoryEntity.setName(frontCategory.getName());
        frontCategoryEntity.setOutdated(frontCategory.getOutdated());
        frontCategoryEntity.setIcon(frontCategory.getIcon());
        frontCategoryEntity.setUpdater(frontCategory.getUpdater());
        frontCategoryEntity.setUpdateTime(frontCategory.getUpdateTime());
        frontCategoryEntity.setCreator(frontCategory.getCreator());
        frontCategoryEntity.setCreateTime(frontCategory.getCreateTime());
        frontCategoryEntity.setFCategoryType(frontCategory.getFCategoryType());
        return frontCategoryEntity;
    }








    public static FrontCategory toFrontCategory(FrontCategoryCommandParam param) {
        if (param == null) {
            return null;
        }
        FrontCategory frontCategory = new FrontCategory();
        frontCategory.setId(param.getId());
        frontCategory.setParentId(param.getParentId());
        frontCategory.setName(param.getName());
        frontCategory.setOutdated(param.getOutdated());
        frontCategory.setIcon(param.getIcon());
        frontCategory.setUpdater(param.getUpdater());
        frontCategory.setUpdateTime(param.getUpdateTime());
        frontCategory.setCreator(param.getCreator());
        frontCategory.setCreateTime(param.getCreateTime());
        frontCategory.setFCategoryType(param.getFCategoryType());
        return frontCategory;
    }
}
