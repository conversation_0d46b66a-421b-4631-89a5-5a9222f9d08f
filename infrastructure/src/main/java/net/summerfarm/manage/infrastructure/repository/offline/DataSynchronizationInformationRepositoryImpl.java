package net.summerfarm.manage.infrastructure.repository.offline;

import net.summerfarm.manage.domain.offline.entity.DataSynchronizationInformationEntity;
import net.summerfarm.manage.domain.offline.repository.DataSynchronizationInformationRepository;
import net.summerfarm.manage.infrastructure.offlinemapper.DataSynchronizationInformationMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class DataSynchronizationInformationRepositoryImpl  implements DataSynchronizationInformationRepository {
   @Resource
   private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Override
    public DataSynchronizationInformationEntity selectByTableName(String tableName) {
        return dataSynchronizationInformationMapper.selectByTableName(tableName);
    }
}
