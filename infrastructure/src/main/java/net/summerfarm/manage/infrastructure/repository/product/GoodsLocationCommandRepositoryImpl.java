package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.GoodsLocation;
import net.summerfarm.manage.infrastructure.mapper.product.GoodsLocationMapper;
import net.summerfarm.manage.infrastructure.converter.product.GoodsLocationConverter;
import net.summerfarm.manage.domain.product.repository.GoodsLocationCommandRepository;
import net.summerfarm.manage.domain.product.entity.GoodsLocationEntity;
import net.summerfarm.manage.domain.product.param.command.GoodsLocationCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-05-07 14:36:00
* @version 1.0
*
*/
@Repository
public class GoodsLocationCommandRepositoryImpl implements GoodsLocationCommandRepository {

    @Autowired
    private GoodsLocationMapper goodsLocationMapper;
    @Override
    public GoodsLocationEntity insertSelective(GoodsLocationCommandParam param) {
        GoodsLocation goodsLocation = GoodsLocationConverter.toGoodsLocation(param);
        goodsLocationMapper.insertSelective(goodsLocation);
        return GoodsLocationConverter.toGoodsLocationEntity(goodsLocation);
    }

    @Override
    public int updateSelectiveById(GoodsLocationCommandParam param){
        return goodsLocationMapper.updateSelectiveById(GoodsLocationConverter.toGoodsLocation(param));
    }


    @Override
    public int remove(Long id) {
        return goodsLocationMapper.remove(id);
    }
}