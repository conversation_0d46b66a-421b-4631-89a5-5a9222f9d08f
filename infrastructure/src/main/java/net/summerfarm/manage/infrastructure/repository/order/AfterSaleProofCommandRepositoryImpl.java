package net.summerfarm.manage.infrastructure.repository.order;

import net.summerfarm.manage.infrastructure.model.order.AfterSaleProof;
import net.summerfarm.manage.infrastructure.mapper.order.AfterSaleProofMapper;
import net.summerfarm.manage.infrastructure.converter.order.AfterSaleProofConverter;
import net.summerfarm.manage.domain.order.repository.AfterSaleProofCommandRepository;
import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import net.summerfarm.manage.domain.order.param.command.AfterSaleProofCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-01-18 16:27:13
* @version 1.0
*
*/
@Repository
public class AfterSaleProofCommandRepositoryImpl implements AfterSaleProofCommandRepository {

    @Autowired
    private AfterSaleProofMapper afterSaleProofMapper;
    @Override
    public AfterSaleProofEntity insertSelective(AfterSaleProofCommandParam param) {
        AfterSaleProof afterSaleProof = AfterSaleProofConverter.toAfterSaleProof(param);
        afterSaleProofMapper.insertSelective(afterSaleProof);
        return AfterSaleProofConverter.toAfterSaleProofEntity(afterSaleProof);
    }

    @Override
    public int updateSelectiveById(AfterSaleProofCommandParam param){
        return afterSaleProofMapper.updateSelectiveById(AfterSaleProofConverter.toAfterSaleProof(param));
    }


    @Override
    public int remove(Long id) {
        return afterSaleProofMapper.remove(id);
    }
}