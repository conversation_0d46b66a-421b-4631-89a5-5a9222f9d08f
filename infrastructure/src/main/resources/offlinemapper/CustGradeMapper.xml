<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.offlinemapper.CustGradeMapper">
    <!-- 结果集映射 -->
    <resultMap id="custGradeResultMap" type="net.summerfarm.manage.infrastructure.model.offline.CustGrade">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="cust_id" property="custId" jdbcType="NUMERIC"/>
		<result column="cust_name" property="custName" jdbcType="VARCHAR"/>
		<result column="dlv_real_amt" property="dlvRealAmt" jdbcType="DOUBLE"/>
		<result column="grade" property="grade" jdbcType="INTEGER"/>
		<result column="next_grade" property="nextGrade" jdbcType="INTEGER"/>
		<result column="date_tag" property="dateTag" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap id="custGradeEntityResultMap" type="net.summerfarm.manage.infrastructure.model.offline.CustGrade">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="cust_id" property="custId" jdbcType="NUMERIC"/>
        <result column="cust_name" property="custName" jdbcType="VARCHAR"/>
        <result column="dlv_real_amt" property="dlvRealAmt" jdbcType="DOUBLE"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="next_grade" property="nextGrade" jdbcType="INTEGER"/>
        <result column="date_tag" property="dateTag" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="custGradeColumns">
          t.id,
          t.cust_id,
          t.cust_name,
          t.dlv_real_amt,
          t.grade,
          t.next_grade,
          t.date_tag
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="custId != null">
                AND t.cust_id = #{custId}
            </if>
			<if test="custName != null and custName !=''">
                AND t.cust_name = #{custName}
            </if>
			<if test="dlvRealAmt != null">
                AND t.dlv_real_amt = #{dlvRealAmt}
            </if>
			<if test="grade != null">
                AND t.grade = #{grade}
            </if>
			<if test="nextGrade != null">
                AND t.next_grade = #{nextGrade}
            </if>
			<if test="dateTag != null and dateTag !=''">
                AND t.date_tag = #{dateTag}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="custId != null">
                    t.cust_id = #{custId},
                </if>
                <if test="custName != null">
                    t.cust_name = #{custName},
                </if>
                <if test="dlvRealAmt != null">
                    t.dlv_real_amt = #{dlvRealAmt},
                </if>
                <if test="grade != null">
                    t.grade = #{grade},
                </if>
                <if test="nextGrade != null">
                    t.next_grade = #{nextGrade},
                </if>
                <if test="dateTag != null">
                    t.date_tag = #{dateTag},
                </if>
        </trim>
    </sql>

    <select id="selectTaskDataList" resultType="net.summerfarm.manage.domain.offline.entity.CustGradeEntity" >
        select             t.id id,
                           t.cust_id custId,
                           t.dlv_real_amt dlvRealAmt,
                           t.grade grade,
                           t.next_grade nextGrade,
                           t.date_tag dateTag
        from cust_grade t where t.date_tag = #{dateTag} and  t.cust_id > #{startId} order by t.date_tag,t.cust_id limit #{offset}
    </select>



</mapper>