<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.config.ConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="configResultMap" type="net.summerfarm.manage.infrastructure.model.config.Config">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="key" property="key" jdbcType="VARCHAR"/>
		<result column="value" property="value" jdbcType="LONGVARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="configColumns">
          t.id,
          t.key,
          t.value,
          t.remark
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="key != null and key !=''">
                AND t.key = #{key}
            </if>
			<if test="value != null and value !=''">
                AND t.value = #{value}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="key != null">
                    t.key = #{key},
                </if>
                <if test="value != null">
                    t.value = #{value},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="configResultMap" >
        SELECT <include refid="configColumns" />
        FROM config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.config.param.query.ConfigQueryParam"  resultType="net.summerfarm.manage.domain.config.entity.ConfigEntity" >
        SELECT
            t.id id,
            t.key key,
            t.value value,
            t.remark remark
        FROM config t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.config.param.query.ConfigQueryParam" resultMap="configResultMap" >
        SELECT <include refid="configColumns" />
        FROM config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.config.Config" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="key != null">
				  key,
              </if>
              <if test="value != null">
				  value,
              </if>
              <if test="remark != null">
				  remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="key != null">
				#{key,jdbcType=VARCHAR},
              </if>
              <if test="value != null">
				#{value,jdbcType=LONGVARCHAR},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.config.Config" >
        UPDATE config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.config.Config" >
        DELETE FROM config
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>