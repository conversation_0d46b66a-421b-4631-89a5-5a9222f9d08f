<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantFrequentlyBuyingSkuNotificationConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantFrequentlyBuyingSkuNotificationConfigResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="special_offer" property="specialOffer" jdbcType="INTEGER"/>
		<result column="goods_arrived" property="goodsArrived" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantFrequentlyBuyingSkuNotificationConfigColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.m_id,
          t.special_offer,
          t.goods_arrived
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="specialOffer != null">
                AND t.special_offer = #{specialOffer}
            </if>
			<if test="goodsArrived != null">
                AND t.goods_arrived = #{goodsArrived}
            </if>
            <if test="mIds != null and mIds.size() > 0">
                AND t.m_id IN
                <foreach collection="mIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="specialOffer != null">
                    t.special_offer = #{specialOffer},
                </if>
                <if test="goodsArrived != null">
                    t.goods_arrived = #{goodsArrived},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="merchantFrequentlyBuyingSkuNotificationConfigResultMap" >
        SELECT <include refid="merchantFrequentlyBuyingSkuNotificationConfigColumns" />
        FROM merchant_frequently_buying_sku_notification_config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam"  resultType="net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.m_id mId,
            t.special_offer specialOffer,
            t.goods_arrived goodsArrived
        FROM merchant_frequently_buying_sku_notification_config t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuNotificationConfigQueryParam" resultMap="merchantFrequentlyBuyingSkuNotificationConfigResultMap" >
        SELECT <include refid="merchantFrequentlyBuyingSkuNotificationConfigColumns" />
        FROM merchant_frequently_buying_sku_notification_config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO merchant_frequently_buying_sku_notification_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="specialOffer != null">
				  special_offer,
              </if>
              <if test="goodsArrived != null">
				  goods_arrived,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="specialOffer != null">
				#{specialOffer,jdbcType=INTEGER},
              </if>
              <if test="goodsArrived != null">
				#{goodsArrived,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig" >
        UPDATE merchant_frequently_buying_sku_notification_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSkuNotificationConfig" >
        DELETE FROM merchant_frequently_buying_sku_notification_config t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>