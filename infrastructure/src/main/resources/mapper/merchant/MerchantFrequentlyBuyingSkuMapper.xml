<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantFrequentlyBuyingSkuMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantFrequentlyBuyingSkuResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSku">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="top" property="top" jdbcType="INTEGER"/>
		<result column="source" property="source" jdbcType="INTEGER"/>
		<result column="recent_delete_time" property="recentDeleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantFrequentlyBuyingSkuColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.m_id,
          t.sku,
          t.status,
          t.top,
          t.source,
          t.recent_delete_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="top != null">
                AND t.top = #{top}
            </if>
			<if test="source != null">
                AND t.source = #{source}
            </if>
			<if test="recentDeleteTime != null">
                AND t.recent_delete_time = #{recentDeleteTime}
            </if>
            <if test="mIds != null and mIds.size() > 0">
                AND t.m_id IN
                <foreach item="item" collection="mIds" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="skus != null and skus.size() > 0">
                AND t.sku IN
                <foreach item="item" collection="skus" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="top != null">
                    t.top = #{top},
                </if>
                <if test="source != null">
                    t.source = #{source},
                </if>
                <if test="recentDeleteTime != null">
                    t.recent_delete_time = #{recentDeleteTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="merchantFrequentlyBuyingSkuResultMap" >
        SELECT <include refid="merchantFrequentlyBuyingSkuColumns" />
        FROM merchant_frequently_buying_sku t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuQueryParam"  resultType="net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.m_id mId,
            t.sku sku,
            t.status status,
            t.top top,
            t.source source,
            t.recent_delete_time recentDeleteTime
        FROM merchant_frequently_buying_sku t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.merchant.param.query.MerchantFrequentlyBuyingSkuQueryParam" resultMap="merchantFrequentlyBuyingSkuResultMap" >
        SELECT <include refid="merchantFrequentlyBuyingSkuColumns" />
        FROM merchant_frequently_buying_sku t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSku" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO merchant_frequently_buying_sku
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="top != null">
				  top,
              </if>
              <if test="source != null">
				  source,
              </if>
              <if test="recentDeleteTime != null">
				  recent_delete_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="top != null">
				#{top,jdbcType=INTEGER},
              </if>
              <if test="source != null">
				#{source,jdbcType=INTEGER},
              </if>
              <if test="recentDeleteTime != null">
				#{recentDeleteTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSku" >
        UPDATE merchant_frequently_buying_sku t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.merchant.MerchantFrequentlyBuyingSku" >
        DELETE FROM merchant_frequently_buying_sku t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>