<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantOuterMapper">

    <sql id="base_column">
        id, m_id mId, outer_no outerNo, remark, gmt_modified gmtModified, gmt_create gmtCreate,outer_platform_id outerPlatformId
    </sql>



    <select id="selectByMIdList" resultType="net.summerfarm.manage.domain.merchant.entity.MerchantOuterEntity">
        SELECT <include refid="base_column"/>
        FROM merchant_outer
        <where>
            <if test="mIdList != null ">
                m_id in
                <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
                    #{mId}
                </foreach>
            </if>
        </where>
    </select>


</mapper>