<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.GoodsLocationMapper">
    <!-- 结果集映射 -->
    <resultMap id="goodsLocationResultMap" type="net.summerfarm.manage.infrastructure.model.product.GoodsLocation">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="store_no" property="storeNo" jdbcType="INTEGER"/>
		<result column="gl_no" property="glNo" jdbcType="VARCHAR"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="temperature" property="temperature" jdbcType="INTEGER"/>
		<result column="passageway" property="passageway" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="goodsLocationColumns">
          t.id,
          t.store_no,
          t.gl_no,
          t.add_time,
          t.update_time,
          t.temperature,
          t.passageway,
          t.type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="storeNo != null">
                AND t.store_no = #{storeNo}
            </if>
			<if test="glNo != null and glNo !=''">
                AND t.gl_no = #{glNo}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="temperature != null">
                AND t.temperature = #{temperature}
            </if>
			<if test="passageway != null and passageway !=''">
                AND t.passageway = #{passageway}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="storeNo != null">
                    t.store_no = #{storeNo},
                </if>
                <if test="glNo != null">
                    t.gl_no = #{glNo},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="temperature != null">
                    t.temperature = #{temperature},
                </if>
                <if test="passageway != null">
                    t.passageway = #{passageway},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="goodsLocationResultMap" >
        SELECT <include refid="goodsLocationColumns" />
        FROM goods_location t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.GoodsLocationQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.GoodsLocationEntity" >
        SELECT
            t.id id,
            t.store_no storeNo,
            t.gl_no glNo,
            t.add_time addTime,
            t.update_time updateTime,
            t.temperature temperature,
            t.passageway passageway,
            t.type type
        FROM goods_location t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.GoodsLocationQueryParam" resultMap="goodsLocationResultMap" >
        SELECT <include refid="goodsLocationColumns" />
        FROM goods_location t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectByGlNo" resultMap="goodsLocationResultMap" >
        SELECT <include refid="goodsLocationColumns" />
        from goods_location t where t.store_no = #{storeNo} and t.gl_no = #{glNo}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.GoodsLocation" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO goods_location
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="storeNo != null">
				  store_no,
              </if>
              <if test="glNo != null">
				  gl_no,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="temperature != null">
				  temperature,
              </if>
              <if test="passageway != null">
				  passageway,
              </if>
              <if test="type != null">
				  type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="storeNo != null">
				#{storeNo,jdbcType=INTEGER},
              </if>
              <if test="glNo != null">
				#{glNo,jdbcType=VARCHAR},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="temperature != null">
				#{temperature,jdbcType=INTEGER},
              </if>
              <if test="passageway != null">
				#{passageway,jdbcType=VARCHAR},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.GoodsLocation" >
        UPDATE goods_location t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.GoodsLocation" >
        DELETE FROM goods_location t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>