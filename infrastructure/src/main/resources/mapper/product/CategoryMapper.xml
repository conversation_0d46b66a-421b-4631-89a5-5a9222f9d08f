<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.CategoryMapper">
  <select id="listCategoryLevel" resultType="net.summerfarm.manage.domain.product.entity.CategoryLevelEntity">
    SELECT c1.id as firstLevel,c2.id as secondLevel,c3.id as thirdLevel
    from category c1
    left join category c2 on c1.id = c2.parent_id and c2.outdated = 0
    left join category c3 on c2.id = c3.parent_id and c3.outdated = 0
    where c1.outdated = 0 and c1.parent_id is null
    and (c1.id in
    <foreach collection="list" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
    or c2.id in
    <foreach collection="list" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
    or c3.id in
    <foreach collection="list" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
    )
  </select>
  <select id="selectInfoByPdId" resultType="net.summerfarm.manage.domain.product.entity.CategoryEntity">
    select
    c.category,
    c.type,
    c.outdated,
    c.parent_id parentId
    from products p left join category c on p.category_id = c.id where p.pd_id = #{pdId}
  </select>


  <select id="selectTreeNodes" resultType="net.summerfarm.manage.domain.product.entity.CategoryEntity">
    SELECT c.id, c.parent_id parentId, c.category,c.type
    FROM category c
    WHERE outdated=0
  </select>

  <select id="selectCategoryAllPath" resultType="net.summerfarm.manage.domain.product.entity.CategoryAllPathEntity">
    SELECT
        t1.id as categoryId,
        t1.category as categoryName,
        CONCAT_WS('/', t3.category, t2.category, t1.category) as allPathCategoryName
    FROM
        category AS t1
            left JOIN category AS t2 ON t1.parent_id = t2.id
            LEFT JOIN `category` as t3 on t2.parent_id = t3.id
    where
        t1.id IN
          <foreach item="categoryId" collection="categoryIdList" open="(" close=")" separator=",">
            #{categoryId}
          </foreach>
    ORDER BY
        t1.id desc;

  </select>

  <select id="selectCategoryIdsByFrontId" resultType="java.lang.Long">
    select
    fctc.category_id
    from front_category fc
    left join front_category fcc on fc.id = fcc.parent_id
    left join front_category_to_category fctc on fcc.id = fctc.front_category_id
    where (fc.id = #{frontId}) or (fcc.id = #{frontId})
  </select>

  <select id="selectById" resultType="net.summerfarm.manage.domain.product.entity.CategoryEntity">
    SELECT c.id,
           c.parent_id as parentId,
           c.category,
           c.type
    FROM category c
    where c.id = #{categoryId,jdbcType=BIGINT}
    </select>
</mapper>

