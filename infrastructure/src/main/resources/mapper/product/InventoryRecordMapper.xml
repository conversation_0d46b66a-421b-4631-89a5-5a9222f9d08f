<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.InventoryRecordMapper">
    <!-- 结果集映射 -->
    <resultMap id="inventoryRecordResultMap" type="net.summerfarm.manage.infrastructure.model.product.InventoryRecord">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="change_field" property="changeField" jdbcType="VARCHAR"/>
		<result column="old_value" property="oldValue" jdbcType="VARCHAR"/>
		<result column="new_value" property="newValue" jdbcType="VARCHAR"/>
		<result column="recorder" property="recorder" jdbcType="VARCHAR"/>
		<result column="addtime" property="addtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="inventoryRecordColumns">
          t.id,
          t.sku,
          t.change_field,
          t.old_value,
          t.new_value,
          t.recorder,
          t.addtime
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="changeField != null and changeField !=''">
                AND t.change_field = #{changeField}
            </if>
			<if test="oldValue != null and oldValue !=''">
                AND t.old_value = #{oldValue}
            </if>
			<if test="newValue != null and newValue !=''">
                AND t.new_value = #{newValue}
            </if>
			<if test="recorder != null and recorder !=''">
                AND t.recorder = #{recorder}
            </if>
			<if test="addtime != null">
                AND t.addtime = #{addtime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="changeField != null">
                    t.change_field = #{changeField},
                </if>
                <if test="oldValue != null">
                    t.old_value = #{oldValue},
                </if>
                <if test="newValue != null">
                    t.new_value = #{newValue},
                </if>
                <if test="recorder != null">
                    t.recorder = #{recorder},
                </if>
                <if test="addtime != null">
                    t.addtime = #{addtime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="inventoryRecordResultMap" >
        SELECT <include refid="inventoryRecordColumns" />
        FROM inventory_record t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryRecordQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.InventoryRecordEntity" >
        SELECT
            t.id id,
            t.sku sku,
            t.change_field changeField,
            t.old_value oldValue,
            t.new_value newValue,
            t.recorder recorder,
            t.addtime addtime
        FROM inventory_record t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.InventoryRecordQueryParam" resultMap="inventoryRecordResultMap" >
        SELECT <include refid="inventoryRecordColumns" />
        FROM inventory_record t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.InventoryRecord" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO inventory_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="changeField != null">
				  change_field,
              </if>
              <if test="oldValue != null">
				  old_value,
              </if>
              <if test="newValue != null">
				  new_value,
              </if>
              <if test="recorder != null">
				  recorder,
              </if>
              <if test="addtime != null">
				  addtime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="changeField != null">
				#{changeField,jdbcType=VARCHAR},
              </if>
              <if test="oldValue != null">
				#{oldValue,jdbcType=VARCHAR},
              </if>
              <if test="newValue != null">
				#{newValue,jdbcType=VARCHAR},
              </if>
              <if test="recorder != null">
				#{recorder,jdbcType=VARCHAR},
              </if>
              <if test="addtime != null">
				#{addtime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>
    <insert id="saveBatch">
        INSERT INTO inventory_record(sku,change_field,old_value,new_value,recorder,addtime) VALUES
        <foreach collection="inventoryRecords" item="item" index="index" separator=",">
            (#{item.sku},#{item.changeField},#{item.oldValue},#{item.newValue},#{item.recorder},#{item.addtime})
        </foreach>
    </insert>

    <!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.InventoryRecord" >
        UPDATE inventory_record t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.InventoryRecord" >
        DELETE FROM inventory_record t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>