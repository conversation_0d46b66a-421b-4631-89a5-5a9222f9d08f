<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.ProductsMapper">
    <!-- 结果集映射 -->
    <resultMap id="productsResultMap" type="net.summerfarm.manage.infrastructure.model.product.Products">
		<id column="pd_id" property="pdId" jdbcType="NUMERIC"/>
		<result column="category_id" property="categoryId" jdbcType="INTEGER"/>
		<result column="brand_id" property="brandId" jdbcType="INTEGER"/>
		<result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
		<result column="pddetail" property="pddetail" jdbcType="LONGVARCHAR"/>
		<result column="detail_picture" property="detailPicture" jdbcType="VARCHAR"/>
		<result column="view_count" property="viewCount" jdbcType="NUMERIC"/>
		<result column="priority" property="priority" jdbcType="INTEGER"/>
		<result column="after_sale_time" property="afterSaleTime" jdbcType="INTEGER"/>
		<result column="after_sale_type" property="afterSaleType" jdbcType="VARCHAR"/>
		<result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
		<result column="outdated" property="outdated" jdbcType="INTEGER"/>
		<result column="storage_location" property="storageLocation" jdbcType="TINYINT"/>
		<result column="pd_no" property="pdNo" jdbcType="VARCHAR"/>
		<result column="origin" property="origin" jdbcType="INTEGER"/>
		<result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
		<result column="slogan" property="slogan" jdbcType="VARCHAR"/>
		<result column="other_slogan" property="otherSlogan" jdbcType="VARCHAR"/>
		<result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
		<result column="refund_type" property="refundType" jdbcType="VARCHAR"/>
		<result column="quality_time" property="qualityTime" jdbcType="INTEGER"/>
		<result column="quality_time_unit" property="qualityTimeUnit" jdbcType="VARCHAR"/>
		<result column="warn_time" property="warnTime" jdbcType="INTEGER"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="creator" property="creator" jdbcType="INTEGER"/>
		<result column="create_type" property="createType" jdbcType="INTEGER"/>
		<result column="real_name" property="realName" jdbcType="VARCHAR"/>
		<result column="create_remark" property="createRemark" jdbcType="VARCHAR"/>
		<result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
		<result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
		<result column="product_introduction" property="productIntroduction" jdbcType="VARCHAR"/>
		<result column="auditor" property="auditor" jdbcType="INTEGER"/>
		<result column="quality_time_type" property="qualityTimeType" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="productsColumns">
          t.pd_id,
          t.category_id,
          t.brand_id,
          t.pd_name,
          t.pddetail,
          t.detail_picture,
          t.view_count,
          t.priority,
          t.after_sale_time,
          t.after_sale_type,
          t.after_sale_unit,
          t.create_time,
          t.expire_time,
          t.outdated,
          t.storage_location,
          t.pd_no,
          t.origin,
          t.storage_method,
          t.slogan,
          t.other_slogan,
          t.picture_path,
          t.refund_type,
          t.quality_time,
          t.quality_time_unit,
          t.warn_time,
          t.add_time,
          t.update_time,
          t.creator,
          t.create_type,
          t.real_name,
          t.create_remark,
          t.audit_status,
          t.audit_time,
          t.product_introduction,
          t.auditor,
          t.quality_time_type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="pdId != null">
                AND t.pd_id = #{pdId}
            </if>
			<if test="categoryId != null">
                AND t.category_id = #{categoryId}
            </if>
			<if test="brandId != null">
                AND t.brand_id = #{brandId}
            </if>
			<if test="pdName != null and pdName !=''">
                AND t.pd_name = #{pdName}
            </if>
			<if test="pddetail != null and pddetail !=''">
                AND t.pddetail = #{pddetail}
            </if>
			<if test="detailPicture != null and detailPicture !=''">
                AND t.detail_picture = #{detailPicture}
            </if>
			<if test="viewCount != null">
                AND t.view_count = #{viewCount}
            </if>
			<if test="priority != null">
                AND t.priority = #{priority}
            </if>
			<if test="afterSaleTime != null">
                AND t.after_sale_time = #{afterSaleTime}
            </if>
			<if test="afterSaleType != null and afterSaleType !=''">
                AND t.after_sale_type = #{afterSaleType}
            </if>
			<if test="afterSaleUnit != null and afterSaleUnit !=''">
                AND t.after_sale_unit = #{afterSaleUnit}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="expireTime != null">
                AND t.expire_time = #{expireTime}
            </if>
			<if test="outdated != null">
                AND t.outdated = #{outdated}
            </if>
			<if test="storageLocation != null">
                AND t.storage_location = #{storageLocation}
            </if>
			<if test="pdNo != null and pdNo !=''">
                AND t.pd_no = #{pdNo}
            </if>
			<if test="origin != null">
                AND t.origin = #{origin}
            </if>
			<if test="storageMethod != null and storageMethod !=''">
                AND t.storage_method = #{storageMethod}
            </if>
			<if test="slogan != null and slogan !=''">
                AND t.slogan = #{slogan}
            </if>
			<if test="otherSlogan != null and otherSlogan !=''">
                AND t.other_slogan = #{otherSlogan}
            </if>
			<if test="picturePath != null and picturePath !=''">
                AND t.picture_path = #{picturePath}
            </if>
			<if test="refundType != null and refundType !=''">
                AND t.refund_type = #{refundType}
            </if>
			<if test="qualityTime != null">
                AND t.quality_time = #{qualityTime}
            </if>
			<if test="qualityTimeUnit != null and qualityTimeUnit !=''">
                AND t.quality_time_unit = #{qualityTimeUnit}
            </if>
			<if test="warnTime != null">
                AND t.warn_time = #{warnTime}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="creator != null">
                AND t.creator = #{creator}
            </if>
			<if test="createType != null">
                AND t.create_type = #{createType}
            </if>
			<if test="realName != null and realName !=''">
                AND t.real_name = #{realName}
            </if>
			<if test="createRemark != null and createRemark !=''">
                AND t.create_remark = #{createRemark}
            </if>
			<if test="auditStatus != null">
                AND t.audit_status = #{auditStatus}
            </if>
			<if test="auditTime != null">
                AND t.audit_time = #{auditTime}
            </if>
			<if test="productIntroduction != null and productIntroduction !=''">
                AND t.product_introduction = #{productIntroduction}
            </if>
			<if test="auditor != null">
                AND t.auditor = #{auditor}
            </if>
			<if test="qualityTimeType != null">
                AND t.quality_time_type = #{qualityTimeType}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="categoryId != null">
                    t.category_id = #{categoryId},
                </if>
                <if test="brandId != null">
                    t.brand_id = #{brandId},
                </if>
                <if test="pdName != null">
                    t.pd_name = #{pdName},
                </if>
                <if test="pddetail != null">
                    t.pddetail = #{pddetail},
                </if>
                <if test="detailPicture != null">
                    t.detail_picture = #{detailPicture},
                </if>
                <if test="viewCount != null">
                    t.view_count = #{viewCount},
                </if>
                <if test="priority != null">
                    t.priority = #{priority},
                </if>
                <if test="afterSaleTime != null">
                    t.after_sale_time = #{afterSaleTime},
                </if>
                <if test="afterSaleType != null">
                    t.after_sale_type = #{afterSaleType},
                </if>
                <if test="afterSaleUnit != null">
                    t.after_sale_unit = #{afterSaleUnit},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="expireTime != null">
                    t.expire_time = #{expireTime},
                </if>
                <if test="outdated != null">
                    t.outdated = #{outdated},
                </if>
                <if test="storageLocation != null">
                    t.storage_location = #{storageLocation},
                </if>
                <if test="pdNo != null">
                    t.pd_no = #{pdNo},
                </if>
                <if test="origin != null">
                    t.origin = #{origin},
                </if>
                <if test="storageMethod != null">
                    t.storage_method = #{storageMethod},
                </if>
                <if test="slogan != null">
                    t.slogan = #{slogan},
                </if>
                <if test="otherSlogan != null">
                    t.other_slogan = #{otherSlogan},
                </if>
                <if test="picturePath != null">
                    t.picture_path = #{picturePath},
                </if>
                <if test="refundType != null">
                    t.refund_type = #{refundType},
                </if>
                <if test="qualityTime != null">
                    t.quality_time = #{qualityTime},
                </if>
                <if test="qualityTimeUnit != null">
                    t.quality_time_unit = #{qualityTimeUnit},
                </if>
                <if test="warnTime != null">
                    t.warn_time = #{warnTime},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createType != null">
                    t.create_type = #{createType},
                </if>
                <if test="realName != null">
                    t.real_name = #{realName},
                </if>
                <if test="createRemark != null">
                    t.create_remark = #{createRemark},
                </if>
                <if test="auditStatus != null">
                    t.audit_status = #{auditStatus},
                </if>
                <if test="auditTime != null">
                    t.audit_time = #{auditTime},
                </if>
                <if test="productIntroduction != null">
                    t.product_introduction = #{productIntroduction},
                </if>
                <if test="auditor != null">
                    t.auditor = #{auditor},
                </if>
                <if test="qualityTimeType != null">
                    t.quality_time_type = #{qualityTimeType},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="productsResultMap" >
        SELECT <include refid="productsColumns" />
        FROM products t
		WHERE t.pd_id = #{pdId}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.ProductsQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.ProductsEntity" >
        SELECT
            t.pd_id pdId,
            t.category_id categoryId,
            t.brand_id brandId,
            t.pd_name pdName,
            t.pddetail pddetail,
            t.detail_picture detailPicture,
            t.view_count viewCount,
            t.priority priority,
            t.after_sale_time afterSaleTime,
            t.after_sale_type afterSaleType,
            t.after_sale_unit afterSaleUnit,
            t.create_time createTime,
            t.expire_time expireTime,
            t.outdated outdated,
            t.storage_location storageLocation,
            t.pd_no pdNo,
            t.origin origin,
            t.storage_method storageMethod,
            t.slogan slogan,
            t.other_slogan otherSlogan,
            t.picture_path picturePath,
            t.refund_type refundType,
            t.quality_time qualityTime,
            t.quality_time_unit qualityTimeUnit,
            t.warn_time warnTime,
            t.add_time addTime,
            t.update_time updateTime,
            t.creator creator,
            t.create_type createType,
            t.real_name realName,
            t.create_remark createRemark,
            t.audit_status auditStatus,
            t.audit_time auditTime,
            t.product_introduction productIntroduction,
            t.auditor auditor,
            t.quality_time_type qualityTimeType
        FROM products t
        <include refid="whereColumnBySelect" />
            ORDER BY t.pd_id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.ProductsQueryParam" resultMap="productsResultMap" >
        SELECT <include refid="productsColumns" />
        FROM products t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectByPdNameCount" resultType="java.lang.Integer">
        select count(1) from products
        where pd_name = #{pdName}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.Products" keyProperty="pdId" useGeneratedKeys="true">
        INSERT INTO products
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="pdId != null">
				  pd_id,
              </if>
              <if test="categoryId != null">
				  category_id,
              </if>
              <if test="brandId != null">
				  brand_id,
              </if>
              <if test="pdName != null">
				  pd_name,
              </if>
              <if test="pddetail != null">
				  pddetail,
              </if>
              <if test="detailPicture != null">
				  detail_picture,
              </if>
              <if test="viewCount != null">
				  view_count,
              </if>
              <if test="priority != null">
				  priority,
              </if>
              <if test="afterSaleTime != null">
				  after_sale_time,
              </if>
              <if test="afterSaleType != null">
				  after_sale_type,
              </if>
              <if test="afterSaleUnit != null">
				  after_sale_unit,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="expireTime != null">
				  expire_time,
              </if>
              <if test="outdated != null">
				  outdated,
              </if>
              <if test="storageLocation != null">
				  storage_location,
              </if>
              <if test="pdNo != null">
				  pd_no,
              </if>
              <if test="origin != null">
				  origin,
              </if>
              <if test="storageMethod != null">
				  storage_method,
              </if>
              <if test="slogan != null">
				  slogan,
              </if>
              <if test="otherSlogan != null">
				  other_slogan,
              </if>
              <if test="picturePath != null">
				  picture_path,
              </if>
              <if test="refundType != null">
				  refund_type,
              </if>
              <if test="qualityTime != null">
				  quality_time,
              </if>
              <if test="qualityTimeUnit != null">
				  quality_time_unit,
              </if>
              <if test="warnTime != null">
				  warn_time,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createType != null">
				  create_type,
              </if>
              <if test="realName != null">
				  real_name,
              </if>
              <if test="createRemark != null">
				  create_remark,
              </if>
              <if test="auditStatus != null">
				  audit_status,
              </if>
              <if test="auditTime != null">
				  audit_time,
              </if>
              <if test="productIntroduction != null">
				  product_introduction,
              </if>
              <if test="auditor != null">
				  auditor,
              </if>
              <if test="qualityTimeType != null">
				  quality_time_type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="pdId != null">
				#{pdId,jdbcType=NUMERIC},
              </if>
              <if test="categoryId != null">
				#{categoryId,jdbcType=INTEGER},
              </if>
              <if test="brandId != null">
				#{brandId,jdbcType=INTEGER},
              </if>
              <if test="pdName != null">
				#{pdName,jdbcType=VARCHAR},
              </if>
              <if test="pddetail != null">
				#{pddetail,jdbcType=LONGVARCHAR},
              </if>
              <if test="detailPicture != null">
				#{detailPicture,jdbcType=VARCHAR},
              </if>
              <if test="viewCount != null">
				#{viewCount,jdbcType=NUMERIC},
              </if>
              <if test="priority != null">
				#{priority,jdbcType=INTEGER},
              </if>
              <if test="afterSaleTime != null">
				#{afterSaleTime,jdbcType=INTEGER},
              </if>
              <if test="afterSaleType != null">
				#{afterSaleType,jdbcType=VARCHAR},
              </if>
              <if test="afterSaleUnit != null">
				#{afterSaleUnit,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="expireTime != null">
				#{expireTime,jdbcType=TIMESTAMP},
              </if>
              <if test="outdated != null">
				#{outdated,jdbcType=INTEGER},
              </if>
              <if test="storageLocation != null">
				#{storageLocation,jdbcType=TINYINT},
              </if>
              <if test="pdNo != null">
				#{pdNo,jdbcType=VARCHAR},
              </if>
              <if test="origin != null">
				#{origin,jdbcType=INTEGER},
              </if>
              <if test="storageMethod != null">
				#{storageMethod,jdbcType=VARCHAR},
              </if>
              <if test="slogan != null">
				#{slogan,jdbcType=VARCHAR},
              </if>
              <if test="otherSlogan != null">
				#{otherSlogan,jdbcType=VARCHAR},
              </if>
              <if test="picturePath != null">
				#{picturePath,jdbcType=VARCHAR},
              </if>
              <if test="refundType != null">
				#{refundType,jdbcType=VARCHAR},
              </if>
              <if test="qualityTime != null">
				#{qualityTime,jdbcType=INTEGER},
              </if>
              <if test="qualityTimeUnit != null">
				#{qualityTimeUnit,jdbcType=VARCHAR},
              </if>
              <if test="warnTime != null">
				#{warnTime,jdbcType=INTEGER},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=INTEGER},
              </if>
              <if test="createType != null">
				#{createType,jdbcType=INTEGER},
              </if>
              <if test="realName != null">
				#{realName,jdbcType=VARCHAR},
              </if>
              <if test="createRemark != null">
				#{createRemark,jdbcType=VARCHAR},
              </if>
              <if test="auditStatus != null">
				#{auditStatus,jdbcType=INTEGER},
              </if>
              <if test="auditTime != null">
				#{auditTime,jdbcType=TIMESTAMP},
              </if>
              <if test="productIntroduction != null">
				#{productIntroduction,jdbcType=VARCHAR},
              </if>
              <if test="auditor != null">
				#{auditor,jdbcType=INTEGER},
              </if>
              <if test="qualityTimeType != null">
				#{qualityTimeType,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.Products" >
        UPDATE products t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.pd_id = #{pdId,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.Products" >
        DELETE FROM products t
		WHERE t.pd_id = #{pdId,jdbcType=NUMERIC}
    </delete>

    <select id="selectListByPdNo" resultMap="productsResultMap">
        select <include refid="productsColumns" />
        from products t
        where t.pd_no in
        <foreach close=")" collection="pdNoList" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>