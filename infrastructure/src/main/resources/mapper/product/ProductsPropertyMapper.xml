<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.ProductsPropertyMapper">
    <!-- 结果集映射 -->
    <resultMap id="productsPropertyResultMap" type="net.summerfarm.manage.infrastructure.model.product.ProductsProperty">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="format_type" property="formatType" jdbcType="INTEGER"/>
		<result column="format_str" property="formatStr" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="productsPropertyColumns">
          t.id,
          t.name,
          t.type,
          t.format_type,
          t.format_str,
          t.status,
          t.creator,
          t.create_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="name != null and name !=''">
                AND t.name = #{name}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="formatType != null">
                AND t.format_type = #{formatType}
            </if>
			<if test="formatStr != null and formatStr !=''">
                AND t.format_str = #{formatStr}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="name != null">
                    t.name = #{name},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="formatType != null">
                    t.format_type = #{formatType},
                </if>
                <if test="formatStr != null">
                    t.format_str = #{formatStr},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="productsPropertyResultMap" >
        SELECT <include refid="productsPropertyColumns" />
        FROM products_property t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.product.param.query.ProductsPropertyQueryParam"  resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity" >
        SELECT
            t.id id,
            t.name name,
            t.type type,
            t.format_type formatType,
            t.format_str formatStr,
            t.status status,
            t.creator creator,
            t.create_time createTime
        FROM products_property t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.product.param.query.ProductsPropertyQueryParam" resultMap="productsPropertyResultMap" >
        SELECT <include refid="productsPropertyColumns" />
        FROM products_property t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsProperty" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO products_property
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="name != null">
				  name,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="formatType != null">
				  format_type,
              </if>
              <if test="formatStr != null">
				  format_str,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="name != null">
				#{name,jdbcType=VARCHAR},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="formatType != null">
				#{formatType,jdbcType=INTEGER},
              </if>
              <if test="formatStr != null">
				#{formatStr,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsProperty" >
        UPDATE products_property t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>

    <select id="batchByIds" parameterType="java.util.List" resultMap="productsPropertyResultMap">
        SELECT <include refid="productsPropertyColumns" />
        FROM products_property t
        where t.id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectOneConditions"
            resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity">
        select
            t.id,
            t.name,
            t.type,
            t.format_type formatType,
            t.format_str formatStr
        from products_property t
        where t.status = 1 and t.`name` = #{name} and t.`type` = #{type}
    </select>

    <select id="selectEffectKeyPropertyByCategoryId"
            resultType="net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity">
        select pp.id,
               pp.name,
               pp.type,
               pp.format_type formatType,
               pp.format_str formatStr,
               pp.status,
               pp.creator,
               pp.create_time createTime
        from products_property pp
                 left join products_property_mapping ppm on pp.id = ppm.products_property_id
        where pp.status = 1 and ppm.type = #{type} and ppm.mapping_id = #{mappingId}
    </select>


    <!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.product.ProductsProperty" >
        DELETE FROM products_property t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>