<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.account.AccountChangeMapper">
    <!-- 结果集映射 -->
    <resultMap id="accountChangeResultMap" type="net.summerfarm.manage.infrastructure.model.account.AccountChange">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="account_id" property="accountId" jdbcType="NUMERIC"/>
		<result column="old_phone" property="oldPhone" jdbcType="VARCHAR"/>
		<result column="old_contact" property="oldContact" jdbcType="VARCHAR"/>
		<result column="new_phone" property="newPhone" jdbcType="VARCHAR"/>
		<result column="new_contact" property="newContact" jdbcType="VARCHAR"/>
		<result column="mname" property="mname" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="openid" property="openid" jdbcType="VARCHAR"/>
		<result column="unionid" property="unionid" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="accountChangeColumns">
          t.id,
          t.m_id,
          t.account_id,
          t.old_phone,
          t.old_contact,
          t.new_phone,
          t.new_contact,
          t.mname,
          t.remark,
          t.status,
          t.openid,
          t.unionid,
          t.create_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="accountId != null">
                AND t.account_id = #{accountId}
            </if>
			<if test="oldPhone != null and oldPhone !=''">
                AND t.old_phone = #{oldPhone}
            </if>
			<if test="oldContact != null and oldContact !=''">
                AND t.old_contact = #{oldContact}
            </if>
			<if test="newPhone != null and newPhone !=''">
                AND t.new_phone = #{newPhone}
            </if>
			<if test="newContact != null and newContact !=''">
                AND t.new_contact = #{newContact}
            </if>
			<if test="mname != null and mname !=''">
                AND t.mname = #{mname}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="openid != null and openid !=''">
                AND t.openid = #{openid}
            </if>
			<if test="unionid != null and unionid !=''">
                AND t.unionid = #{unionid}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="accountId != null">
                    t.account_id = #{accountId},
                </if>
                <if test="oldPhone != null">
                    t.old_phone = #{oldPhone},
                </if>
                <if test="oldContact != null">
                    t.old_contact = #{oldContact},
                </if>
                <if test="newPhone != null">
                    t.new_phone = #{newPhone},
                </if>
                <if test="newContact != null">
                    t.new_contact = #{newContact},
                </if>
                <if test="mname != null">
                    t.mname = #{mname},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="openid != null">
                    t.openid = #{openid},
                </if>
                <if test="unionid != null">
                    t.unionid = #{unionid},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="accountChangeResultMap" >
        SELECT <include refid="accountChangeColumns" />
        FROM account_change t
		WHERE t.id = #{id}
    </select>



    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.common.input.merchant.MerchantQueryInput" resultMap="accountChangeResultMap" >
        SELECT <include refid="accountChangeColumns" />
        FROM account_change t
        where 1=1
        <if test="accountChangeStatus != null">
            AND t.status = #{accountChangeStatus}
        </if>
        <if test="accountChangeId != null">
            AND t.id = #{accountChangeId}
        </if>
        order by t.id desc
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectOne" parameterType="net.summerfarm.manage.infrastructure.model.account.AccountChange" resultMap="accountChangeResultMap" >
        SELECT <include refid="accountChangeColumns" />
        FROM account_change t
        <include refid="whereColumnBySelect"></include>
    </select>

	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.account.AccountChange" >
        INSERT INTO account_change
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="accountId != null">
				  account_id,
              </if>
              <if test="oldPhone != null">
				  old_phone,
              </if>
              <if test="oldContact != null">
				  old_contact,
              </if>
              <if test="newPhone != null">
				  new_phone,
              </if>
              <if test="newContact != null">
				  new_contact,
              </if>
              <if test="mname != null">
				  mname,
              </if>
              <if test="remark != null">
				  remark,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="openid != null">
				  openid,
              </if>
              <if test="unionid != null">
				  unionid,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="accountId != null">
				#{accountId,jdbcType=NUMERIC},
              </if>
              <if test="oldPhone != null">
				#{oldPhone,jdbcType=VARCHAR},
              </if>
              <if test="oldContact != null">
				#{oldContact,jdbcType=VARCHAR},
              </if>
              <if test="newPhone != null">
				#{newPhone,jdbcType=VARCHAR},
              </if>
              <if test="newContact != null">
				#{newContact,jdbcType=VARCHAR},
              </if>
              <if test="mname != null">
				#{mname,jdbcType=VARCHAR},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="openid != null">
				#{openid,jdbcType=VARCHAR},
              </if>
              <if test="unionid != null">
				#{unionid,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateByIdSelective" parameterType="net.summerfarm.manage.infrastructure.model.account.AccountChange" >
        UPDATE account_change t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>


	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.account.AccountChange" >
        DELETE FROM account_change t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>

	<!-- 根据主键ID进行批量物理删除 -->
	<delete id="batchRemove" parameterType="java.util.List" >
        DELETE FROM account_change t
		WHERE t.id IN
        <foreach item="item" collection="list" index="index" open="("
                 separator="," close=")">
			#{item}
        </foreach>
    </delete>



</mapper>