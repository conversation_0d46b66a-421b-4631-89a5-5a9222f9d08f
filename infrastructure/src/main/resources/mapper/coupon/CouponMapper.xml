<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.coupon.CouponMapper">
    <!-- 结果集映射 -->
    <resultMap id="couponResultMap" type="net.summerfarm.manage.infrastructure.model.coupon.Coupon">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="code" property="code" jdbcType="VARCHAR"/>
		<result column="money" property="money" jdbcType="DOUBLE"/>
		<result column="threshold" property="threshold" jdbcType="DOUBLE"/>
		<result column="type" property="type" jdbcType="TINYINT"/>
		<result column="vaild_date" property="vaildDate" jdbcType="TIMESTAMP"/>
		<result column="vaild_time" property="vaildTime" jdbcType="INTEGER"/>
		<result column="grouping" property="grouping" jdbcType="INTEGER"/>
		<result column="new_hand" property="newHand" jdbcType="INTEGER"/>
		<result column="category_id" property="categoryId" jdbcType="LONGVARCHAR"/>
		<result column="sku" property="sku" jdbcType="LONGVARCHAR"/>
		<result column="reamrk" property="reamrk" jdbcType="VARCHAR"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="agio_type" property="agioType" jdbcType="INTEGER"/>
		<result column="start_date" property="startDate" jdbcType="TIMESTAMP"/>
		<result column="start_time" property="startTime" jdbcType="INTEGER"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="limit_flag" property="limitFlag" jdbcType="INTEGER"/>
		<result column="activity_scope" property="activityScope" jdbcType="INTEGER"/>
		<result column="task_tag" property="taskTag" jdbcType="TINYINT"/>
		<result column="delete_tag" property="deleteTag" jdbcType="TINYINT"/>
		<result column="auto_created" property="autoCreated" jdbcType="TINYINT"/>
		<result column="quantity_claimed" property="quantityClaimed" jdbcType="INTEGER"/>
		<result column="grant_amount" property="grantAmount" jdbcType="INTEGER"/>
		<result column="grant_limit" property="grantLimit" jdbcType="INTEGER"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="couponColumns">
          t.id,
          t.name,
          t.code,
          t.money,
          t.threshold,
          t.type,
          t.vaild_date,
          t.vaild_time,
          t.grouping,
          t.new_hand,
          t.category_id,
          t.sku,
          t.reamrk,
          t.add_time,
          t.status,
          t.agio_type,
          t.start_date,
          t.start_time,
          t.update_time,
          t.limit_flag,
          t.activity_scope,
          t.task_tag,
          t.delete_tag,
          t.auto_created,
          t.quantity_claimed,
          t.grant_amount,
          t.grant_limit,
          t.creator
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="name != null and name !=''">
                AND t.name = #{name}
            </if>
			<if test="code != null and code !=''">
                AND t.code = #{code}
            </if>
			<if test="money != null">
                AND t.money = #{money}
            </if>
			<if test="threshold != null">
                AND t.threshold = #{threshold}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="vaildDate != null">
                AND t.vaild_date = #{vaildDate}
            </if>
			<if test="vaildTime != null">
                AND t.vaild_time = #{vaildTime}
            </if>
			<if test="grouping != null">
                AND t.grouping = #{grouping}
            </if>
			<if test="newHand != null">
                AND t.new_hand = #{newHand}
            </if>
			<if test="categoryId != null and categoryId !=''">
                AND t.category_id = #{categoryId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="reamrk != null and reamrk !=''">
                AND t.reamrk = #{reamrk}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="agioType != null">
                AND t.agio_type = #{agioType}
            </if>
			<if test="startDate != null">
                AND t.start_date = #{startDate}
            </if>
			<if test="startTime != null">
                AND t.start_time = #{startTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="limitFlag != null">
                AND t.limit_flag = #{limitFlag}
            </if>
			<if test="activityScope != null">
                AND t.activity_scope = #{activityScope}
            </if>
			<if test="taskTag != null">
                AND t.task_tag = #{taskTag}
            </if>
			<if test="deleteTag != null">
                AND t.delete_tag = #{deleteTag}
            </if>
			<if test="autoCreated != null">
                AND t.auto_created = #{autoCreated}
            </if>
			<if test="quantityClaimed != null">
                AND t.quantity_claimed = #{quantityClaimed}
            </if>
			<if test="grantAmount != null">
                AND t.grant_amount = #{grantAmount}
            </if>
			<if test="grantLimit != null">
                AND t.grant_limit = #{grantLimit}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="skuScopeDesc != null and skuScopeDesc !=''">
                AND t.sku_scope_desc = #{skuScopeDesc}
            </if>
			<if test="skuScope != null">
                AND t.sku_scope = #{skuScope}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="name != null">
                    t.name = #{name},
                </if>
                <if test="code != null">
                    t.code = #{code},
                </if>
                <if test="money != null">
                    t.money = #{money},
                </if>
                <if test="threshold != null">
                    t.threshold = #{threshold},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="vaildDate != null">
                    t.vaild_date = #{vaildDate},
                </if>
                <if test="vaildTime != null">
                    t.vaild_time = #{vaildTime},
                </if>
                <if test="grouping != null">
                    t.grouping = #{grouping},
                </if>
                <if test="newHand != null">
                    t.new_hand = #{newHand},
                </if>
                <if test="categoryId != null">
                    t.category_id = #{categoryId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="reamrk != null">
                    t.reamrk = #{reamrk},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="agioType != null">
                    t.agio_type = #{agioType},
                </if>
                <if test="startDate != null">
                    t.start_date = #{startDate},
                </if>
                <if test="startTime != null">
                    t.start_time = #{startTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="limitFlag != null">
                    t.limit_flag = #{limitFlag},
                </if>
                <if test="activityScope != null">
                    t.activity_scope = #{activityScope},
                </if>
                <if test="taskTag != null">
                    t.task_tag = #{taskTag},
                </if>
                <if test="deleteTag != null">
                    t.delete_tag = #{deleteTag},
                </if>
                <if test="autoCreated != null">
                    t.auto_created = #{autoCreated},
                </if>
                <if test="quantityClaimed != null">
                    t.quantity_claimed = #{quantityClaimed},
                </if>
                <if test="grantAmount != null">
                    t.grant_amount = #{grantAmount},
                </if>
                <if test="grantLimit != null">
                    t.grant_limit = #{grantLimit},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="skuScopeDesc != null">
                    t.sku_scope_desc = #{skuScopeDesc},
                </if>
                <if test="skuScope != null">
                    t.sku_scope = #{skuScope},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="couponResultMap" >
        SELECT <include refid="couponColumns" />
        FROM coupon t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.coupon.param.query.CouponQueryParam"  resultType="net.summerfarm.manage.domain.coupon.entity.CouponEntity" >
        SELECT
            t.id id,
            t.name name,
            t.code code,
            t.money money,
            t.threshold threshold,
            t.type type,
            t.vaild_date vaildDate,
            t.vaild_time vaildTime,
            t.grouping grouping,
            t.new_hand newHand,
            t.category_id categoryId,
            t.sku sku,
            t.reamrk reamrk,
            t.add_time addTime,
            t.status status,
            t.agio_type agioType,
            t.start_date startDate,
            t.start_time startTime,
            t.update_time updateTime,
            t.limit_flag limitFlag,
            t.activity_scope activityScope,
            t.task_tag taskTag,
            t.delete_tag deleteTag,
            t.auto_created autoCreated,
            t.quantity_claimed quantityClaimed,
            t.grant_amount grantAmount,
            t.grant_limit grantLimit,
            t.creator creator,
            t.sku_scope_desc skuScopeDesc,
            t.sku_scope skuScope
        FROM coupon t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.coupon.param.query.CouponQueryParam" resultMap="couponResultMap" >
        SELECT <include refid="couponColumns" />
        FROM coupon t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.coupon.Coupon" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO coupon
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="name != null">
				  name,
              </if>
              <if test="code != null">
				  code,
              </if>
              <if test="money != null">
				  money,
              </if>
              <if test="threshold != null">
				  threshold,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="vaildDate != null">
				  vaild_date,
              </if>
              <if test="vaildTime != null">
				  vaild_time,
              </if>
              <if test="grouping != null">
				  grouping,
              </if>
              <if test="newHand != null">
				  new_hand,
              </if>
              <if test="categoryId != null">
				  category_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="reamrk != null">
				  reamrk,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="agioType != null">
				  agio_type,
              </if>
              <if test="startDate != null">
				  start_date,
              </if>
              <if test="startTime != null">
				  start_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="limitFlag != null">
				  limit_flag,
              </if>
              <if test="activityScope != null">
				  activity_scope,
              </if>
              <if test="taskTag != null">
				  task_tag,
              </if>
              <if test="deleteTag != null">
				  delete_tag,
              </if>
              <if test="autoCreated != null">
				  auto_created,
              </if>
              <if test="quantityClaimed != null">
				  quantity_claimed,
              </if>
              <if test="grantAmount != null">
				  grant_amount,
              </if>
              <if test="grantLimit != null">
				  grant_limit,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="skuScopeDesc != null">
				  sku_scope_desc,
              </if>
              <if test="skuScope != null">
				  sku_scope,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="name != null">
				#{name,jdbcType=VARCHAR},
              </if>
              <if test="code != null">
				#{code,jdbcType=VARCHAR},
              </if>
              <if test="money != null">
				#{money,jdbcType=DOUBLE},
              </if>
              <if test="threshold != null">
				#{threshold,jdbcType=DOUBLE},
              </if>
              <if test="type != null">
				#{type,jdbcType=TINYINT},
              </if>
              <if test="vaildDate != null">
				#{vaildDate,jdbcType=TIMESTAMP},
              </if>
              <if test="vaildTime != null">
				#{vaildTime,jdbcType=INTEGER},
              </if>
              <if test="grouping != null">
				#{grouping,jdbcType=INTEGER},
              </if>
              <if test="newHand != null">
				#{newHand,jdbcType=INTEGER},
              </if>
              <if test="categoryId != null">
				#{categoryId,jdbcType=LONGVARCHAR},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=LONGVARCHAR},
              </if>
              <if test="reamrk != null">
				#{reamrk,jdbcType=VARCHAR},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="agioType != null">
				#{agioType,jdbcType=INTEGER},
              </if>
              <if test="startDate != null">
				#{startDate,jdbcType=TIMESTAMP},
              </if>
              <if test="startTime != null">
				#{startTime,jdbcType=INTEGER},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="limitFlag != null">
				#{limitFlag,jdbcType=INTEGER},
              </if>
              <if test="activityScope != null">
				#{activityScope,jdbcType=INTEGER},
              </if>
              <if test="taskTag != null">
				#{taskTag,jdbcType=TINYINT},
              </if>
              <if test="deleteTag != null">
				#{deleteTag,jdbcType=TINYINT},
              </if>
              <if test="autoCreated != null">
				#{autoCreated,jdbcType=TINYINT},
              </if>
              <if test="quantityClaimed != null">
				#{quantityClaimed,jdbcType=INTEGER},
              </if>
              <if test="grantAmount != null">
				#{grantAmount,jdbcType=INTEGER},
              </if>
              <if test="grantLimit != null">
				#{grantLimit,jdbcType=INTEGER},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="skuScopeDesc != null">
				#{skuScopeDesc,jdbcType=VARCHAR},
              </if>
              <if test="skuScope != null">
				#{skuScope,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.coupon.Coupon" >
        UPDATE coupon t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.coupon.Coupon" >
        DELETE FROM coupon
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>


    <select id="selectByIds"  resultMap="couponResultMap" >
        SELECT <include refid="couponColumns" />
        FROM coupon t
        WHERE t.id in
        <foreach item="item" collection="ids" index="index" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>