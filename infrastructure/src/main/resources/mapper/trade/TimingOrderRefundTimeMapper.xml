<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.trade.TimingOrderRefundTimeMapper">
    <!-- 结果集映射 -->
    <resultMap id="timingOrderRefundTimeResultMap" type="net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="refund_time" property="refundTime" jdbcType="DATE"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="timingOrderRefundTimeColumns">
          t.id,
          t.order_no,
          t.refund_time,
          t.m_id,
          t.create_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="refundTime != null">
                AND t.refund_time = #{refundTime}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="refundTime != null">
                    t.refund_time = #{refundTime},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="timingOrderRefundTimeResultMap" >
        SELECT <include refid="timingOrderRefundTimeColumns" />
        FROM timing_order_refund_time t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.trade.param.query.TimingOrderRefundTimeQueryParam"  resultType="net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity" >
        SELECT
            t.id id,
            t.order_no orderNo,
            t.refund_time refundTime,
            t.m_id mId,
            t.create_time createTime,
            t.update_time updateTime
        FROM timing_order_refund_time t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.trade.param.query.TimingOrderRefundTimeQueryParam" resultMap="timingOrderRefundTimeResultMap" >
        SELECT <include refid="timingOrderRefundTimeColumns" />
        FROM timing_order_refund_time t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO timing_order_refund_time
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="refundTime != null">
				  refund_time,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="refundTime != null">
				#{refundTime,jdbcType=DATE},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime" >
        UPDATE timing_order_refund_time t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime" >
        DELETE FROM timing_order_refund_time t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>

    <delete id="deleteTimeOrderRefund">
        DELETE FROM timing_order_refund_time
        WHERE order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>


</mapper>