<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.AfterSaleProofMapper">
    <!-- 结果集映射 -->
    <resultMap id="afterSaleProofResultMap" type="net.summerfarm.manage.infrastructure.model.order.AfterSaleProof">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="after_sale_order_no" property="afterSaleOrderNo" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="handle_num" property="handleNum" jdbcType="DOUBLE"/>
		<result column="proof_pic" property="proofPic" jdbcType="VARCHAR"/>
		<result column="after_sale_type" property="afterSaleType" jdbcType="VARCHAR"/>
		<result column="refund_type" property="refundType" jdbcType="VARCHAR"/>
		<result column="handle_type" property="handleType" jdbcType="INTEGER"/>
		<result column="handler" property="handler" jdbcType="VARCHAR"/>
		<result column="handle_remark" property="handleRemark" jdbcType="VARCHAR"/>
		<result column="auditer" property="auditer" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="apply_remark" property="applyRemark" jdbcType="VARCHAR"/>
		<result column="updatetime" property="updatetime" jdbcType="TIMESTAMP"/>
		<result column="applyer" property="applyer" jdbcType="VARCHAR"/>
		<result column="audite_remark" property="auditeRemark" jdbcType="VARCHAR"/>
		<result column="extra_remark" property="extraRemark" jdbcType="VARCHAR"/>
		<result column="auditetime" property="auditetime" jdbcType="TIMESTAMP"/>
		<result column="handletime" property="handletime" jdbcType="TIMESTAMP"/>
		<result column="recovery_num" property="recoveryNum" jdbcType="DOUBLE"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="handle_secondary_remark" property="handleSecondaryRemark" jdbcType="VARCHAR"/>
		<result column="apply_secondary_remark" property="applySecondaryRemark" jdbcType="VARCHAR"/>
		<result column="proof_video" property="proofVideo" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="afterSaleProofColumns">
          t.id,
          t.after_sale_order_no,
          t.quantity,
          t.handle_num,
          t.proof_pic,
          t.after_sale_type,
          t.refund_type,
          t.handle_type,
          t.handler,
          t.handle_remark,
          t.auditer,
          t.status,
          t.apply_remark,
          t.updatetime,
          t.applyer,
          t.audite_remark,
          t.extra_remark,
          t.auditetime,
          t.handletime,
          t.recovery_num,
          t.create_time,
          t.handle_secondary_remark,
          t.apply_secondary_remark,
          t.proof_video
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="afterSaleOrderNo != null and afterSaleOrderNo !=''">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
			<if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
			<if test="handleNum != null">
                AND t.handle_num = #{handleNum}
            </if>
			<if test="proofPic != null and proofPic !=''">
                AND t.proof_pic = #{proofPic}
            </if>
			<if test="afterSaleType != null and afterSaleType !=''">
                AND t.after_sale_type = #{afterSaleType}
            </if>
			<if test="refundType != null and refundType !=''">
                AND t.refund_type = #{refundType}
            </if>
			<if test="handleType != null">
                AND t.handle_type = #{handleType}
            </if>
			<if test="handler != null and handler !=''">
                AND t.handler = #{handler}
            </if>
			<if test="handleRemark != null and handleRemark !=''">
                AND t.handle_remark = #{handleRemark}
            </if>
			<if test="auditer != null and auditer !=''">
                AND t.auditer = #{auditer}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="applyRemark != null and applyRemark !=''">
                AND t.apply_remark = #{applyRemark}
            </if>
			<if test="updatetime != null">
                AND t.updatetime = #{updatetime}
            </if>
			<if test="applyer != null and applyer !=''">
                AND t.applyer = #{applyer}
            </if>
			<if test="auditeRemark != null and auditeRemark !=''">
                AND t.audite_remark = #{auditeRemark}
            </if>
			<if test="extraRemark != null and extraRemark !=''">
                AND t.extra_remark = #{extraRemark}
            </if>
			<if test="auditetime != null">
                AND t.auditetime = #{auditetime}
            </if>
			<if test="handletime != null">
                AND t.handletime = #{handletime}
            </if>
			<if test="recoveryNum != null">
                AND t.recovery_num = #{recoveryNum}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="handleSecondaryRemark != null and handleSecondaryRemark !=''">
                AND t.handle_secondary_remark = #{handleSecondaryRemark}
            </if>
			<if test="applySecondaryRemark != null and applySecondaryRemark !=''">
                AND t.apply_secondary_remark = #{applySecondaryRemark}
            </if>
			<if test="proofVideo != null and proofVideo !=''">
                AND t.proof_video = #{proofVideo}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="afterSaleOrderNo != null">
                    t.after_sale_order_no = #{afterSaleOrderNo},
                </if>
                <if test="quantity != null">
                    t.quantity = #{quantity},
                </if>
                <if test="handleNum != null">
                    t.handle_num = #{handleNum},
                </if>
                <if test="proofPic != null">
                    t.proof_pic = #{proofPic},
                </if>
                <if test="afterSaleType != null">
                    t.after_sale_type = #{afterSaleType},
                </if>
                <if test="refundType != null">
                    t.refund_type = #{refundType},
                </if>
                <if test="handleType != null">
                    t.handle_type = #{handleType},
                </if>
                <if test="handler != null">
                    t.handler = #{handler},
                </if>
                <if test="handleRemark != null">
                    t.handle_remark = #{handleRemark},
                </if>
                <if test="auditer != null">
                    t.auditer = #{auditer},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="applyRemark != null">
                    t.apply_remark = #{applyRemark},
                </if>
                <if test="updatetime != null">
                    t.updatetime = #{updatetime},
                </if>
                <if test="applyer != null">
                    t.applyer = #{applyer},
                </if>
                <if test="auditeRemark != null">
                    t.audite_remark = #{auditeRemark},
                </if>
                <if test="extraRemark != null">
                    t.extra_remark = #{extraRemark},
                </if>
                <if test="auditetime != null">
                    t.auditetime = #{auditetime},
                </if>
                <if test="handletime != null">
                    t.handletime = #{handletime},
                </if>
                <if test="recoveryNum != null">
                    t.recovery_num = #{recoveryNum},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="handleSecondaryRemark != null">
                    t.handle_secondary_remark = #{handleSecondaryRemark},
                </if>
                <if test="applySecondaryRemark != null">
                    t.apply_secondary_remark = #{applySecondaryRemark},
                </if>
                <if test="proofVideo != null">
                    t.proof_video = #{proofVideo},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="afterSaleProofResultMap" >
        SELECT <include refid="afterSaleProofColumns" />
        FROM after_sale_proof t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.AfterSaleProofQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity" >
        SELECT
            t.id id,
            t.after_sale_order_no afterSaleOrderNo,
            t.quantity quantity,
            t.handle_num handleNum,
            t.proof_pic proofPic,
            t.after_sale_type afterSaleType,
            t.refund_type refundType,
            t.handle_type handleType,
            t.handler handler,
            t.handle_remark handleRemark,
            t.auditer auditer,
            t.status status,
            t.apply_remark applyRemark,
            t.updatetime updatetime,
            t.applyer applyer,
            t.audite_remark auditeRemark,
            t.extra_remark extraRemark,
            t.auditetime auditetime,
            t.handletime handletime,
            t.recovery_num recoveryNum,
            t.create_time createTime,
            t.handle_secondary_remark handleSecondaryRemark,
            t.apply_secondary_remark applySecondaryRemark,
            t.proof_video proofVideo
        FROM after_sale_proof t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.AfterSaleProofQueryParam" resultMap="afterSaleProofResultMap" >
        SELECT <include refid="afterSaleProofColumns" />
        FROM after_sale_proof t
        <include refid="whereColumnBySelect"></include>
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.AfterSaleProof" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO after_sale_proof
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="afterSaleOrderNo != null">
				  after_sale_order_no,
              </if>
              <if test="quantity != null">
				  quantity,
              </if>
              <if test="handleNum != null">
				  handle_num,
              </if>
              <if test="proofPic != null">
				  proof_pic,
              </if>
              <if test="afterSaleType != null">
				  after_sale_type,
              </if>
              <if test="refundType != null">
				  refund_type,
              </if>
              <if test="handleType != null">
				  handle_type,
              </if>
              <if test="handler != null">
				  handler,
              </if>
              <if test="handleRemark != null">
				  handle_remark,
              </if>
              <if test="auditer != null">
				  auditer,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="applyRemark != null">
				  apply_remark,
              </if>
              <if test="updatetime != null">
				  updatetime,
              </if>
              <if test="applyer != null">
				  applyer,
              </if>
              <if test="auditeRemark != null">
				  audite_remark,
              </if>
              <if test="extraRemark != null">
				  extra_remark,
              </if>
              <if test="auditetime != null">
				  auditetime,
              </if>
              <if test="handletime != null">
				  handletime,
              </if>
              <if test="recoveryNum != null">
				  recovery_num,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="handleSecondaryRemark != null">
				  handle_secondary_remark,
              </if>
              <if test="applySecondaryRemark != null">
				  apply_secondary_remark,
              </if>
              <if test="proofVideo != null">
				  proof_video,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="afterSaleOrderNo != null">
				#{afterSaleOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
              </if>
              <if test="handleNum != null">
				#{handleNum,jdbcType=DOUBLE},
              </if>
              <if test="proofPic != null">
				#{proofPic,jdbcType=VARCHAR},
              </if>
              <if test="afterSaleType != null">
				#{afterSaleType,jdbcType=VARCHAR},
              </if>
              <if test="refundType != null">
				#{refundType,jdbcType=VARCHAR},
              </if>
              <if test="handleType != null">
				#{handleType,jdbcType=INTEGER},
              </if>
              <if test="handler != null">
				#{handler,jdbcType=VARCHAR},
              </if>
              <if test="handleRemark != null">
				#{handleRemark,jdbcType=VARCHAR},
              </if>
              <if test="auditer != null">
				#{auditer,jdbcType=VARCHAR},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="applyRemark != null">
				#{applyRemark,jdbcType=VARCHAR},
              </if>
              <if test="updatetime != null">
				#{updatetime,jdbcType=TIMESTAMP},
              </if>
              <if test="applyer != null">
				#{applyer,jdbcType=VARCHAR},
              </if>
              <if test="auditeRemark != null">
				#{auditeRemark,jdbcType=VARCHAR},
              </if>
              <if test="extraRemark != null">
				#{extraRemark,jdbcType=VARCHAR},
              </if>
              <if test="auditetime != null">
				#{auditetime,jdbcType=TIMESTAMP},
              </if>
              <if test="handletime != null">
				#{handletime,jdbcType=TIMESTAMP},
              </if>
              <if test="recoveryNum != null">
				#{recoveryNum,jdbcType=DOUBLE},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="handleSecondaryRemark != null">
				#{handleSecondaryRemark,jdbcType=VARCHAR},
              </if>
              <if test="applySecondaryRemark != null">
				#{applySecondaryRemark,jdbcType=VARCHAR},
              </if>
              <if test="proofVideo != null">
				#{proofVideo,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.AfterSaleProof" >
        UPDATE after_sale_proof t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.AfterSaleProof" >
        DELETE FROM after_sale_proof t
		WHERE t.id = #{id,jdbcType=INTEGER}
    </delete>



</mapper>