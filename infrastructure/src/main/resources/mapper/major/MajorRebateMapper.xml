<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.major.MajorRebateMapper">
    <!-- 结果集映射 -->
    <resultMap id="majorRebateResultMap" type="net.summerfarm.manage.infrastructure.model.major.MajorRebate">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="number" property="number" jdbcType="DOUBLE"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="cate" property="cate" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <resultMap id="majorRebateEntityResultMap" type="net.summerfarm.manage.domain.major.entity.MajorRebateEntity">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="number" property="number" jdbcType="DOUBLE"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="cate" property="cate" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="majorRebateColumns">
          t.id,
          t.sku,
          t.name,
          t.weight,
          t.type,
          t.number,
          t.admin_id,
          t.area_no,
          t.area_name,
          t.cate,
          t.status,
          t.add_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="name != null and name !=''">
                AND t.name = #{name}
            </if>
			<if test="weight != null and weight !=''">
                AND t.weight = #{weight}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="number != null">
                AND t.number = #{number}
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
			<if test="areaName != null and areaName !=''">
                AND t.area_name = #{areaName}
            </if>
			<if test="cate != null">
                AND t.cate = #{cate}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="name != null">
                    t.name = #{name},
                </if>
                <if test="weight != null">
                    t.weight = #{weight},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="number != null">
                    t.number = #{number},
                </if>
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="areaName != null">
                    t.area_name = #{areaName},
                </if>
                <if test="cate != null">
                    t.cate = #{cate},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="majorRebateResultMap" >
        SELECT <include refid="majorRebateColumns" />
        FROM major_rebate t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.major.param.query.MajorRebateQueryParam"  resultType="net.summerfarm.manage.domain.major.entity.MajorRebateEntity" >
        SELECT
            t.id id,
            t.sku sku,
            t.name name,
            t.weight weight,
            t.type type,
            t.number number,
            t.admin_id adminId,
            t.area_no areaNo,
            t.area_name areaName,
            t.cate cate,
            t.status status,
            t.add_time addTime,
            t.update_time updateTime
        FROM major_rebate t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.major.param.query.MajorRebateQueryParam" resultMap="majorRebateResultMap" >
        SELECT <include refid="majorRebateColumns" />
        FROM major_rebate t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorRebate" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO major_rebate
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="name != null">
				  name,
              </if>
              <if test="weight != null">
				  weight,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="number != null">
				  number,
              </if>
              <if test="adminId != null">
				  admin_id,
              </if>
              <if test="areaNo != null">
				  area_no,
              </if>
              <if test="areaName != null">
				  area_name,
              </if>
              <if test="cate != null">
				  cate,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=INTEGER},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="name != null">
				#{name,jdbcType=VARCHAR},
              </if>
              <if test="weight != null">
				#{weight,jdbcType=VARCHAR},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="number != null">
				#{number,jdbcType=DOUBLE},
              </if>
              <if test="adminId != null">
				#{adminId,jdbcType=INTEGER},
              </if>
              <if test="areaNo != null">
				#{areaNo,jdbcType=INTEGER},
              </if>
              <if test="areaName != null">
				#{areaName,jdbcType=VARCHAR},
              </if>
              <if test="cate != null">
				#{cate,jdbcType=INTEGER},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorRebate" >
        UPDATE major_rebate t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.major.MajorRebate" >
        DELETE FROM major_rebate
		WHERE id = #{id,jdbcType=INTEGER}
    </delete>


    <select id="selectList" resultType="net.summerfarm.manage.domain.major.entity.MajorRebateEntity">
        SELECT id, sku, name, weight, type, number,admin_id adminId, area_no areaNo,area_name areaName , cate
        FROM major_rebate b
        where b.status=1
        <if test="adminId != null">
            AND b.admin_id =#{adminId}
        </if>
        <if test="cate != null">
            AND b.cate = #{cate}
        </if>
        <if test="sku != null">
            AND b.sku = #{sku}
        </if>
        <if test="areaNos != null and areaNos.size() > 0">
            AND b.area_no IN
            <foreach collection="areaNos" item="areaNo" open="(" separator="," close=")">
                #{areaNo}
            </foreach>
        </if>
    </select>



</mapper>