<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.customization.CustomizationRequestMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequest">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
        <result column="color_count" property="colorCount" jdbcType="INTEGER"/>
        <result column="design_submit_time" property="designSubmitTime" jdbcType="TIMESTAMP"/>
        <result column="refuse_time" property="refuseTime" jdbcType="TIMESTAMP"/>
        <result column="close_time" property="closeTime" jdbcType="TIMESTAMP"/>
        <result column="agree_time" property="agreeTime" jdbcType="TIMESTAMP"/>
        <result column="reference_image" property="referenceImage" jdbcType="VARCHAR"/>
        <result column="logo_image" property="logoImage" jdbcType="VARCHAR"/>
        <result column="logo_size" property="logoSize" jdbcType="VARCHAR"/>
        <result column="sample_image" property="sampleImage" jdbcType="VARCHAR"/>
        <result column="design_image" property="designImage" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="refuse_reason" property="refuseReason" jdbcType="VARCHAR"/>
        <result column="store_remark" property="storeRemark" jdbcType="VARCHAR"/>
        <result column="designer_remark" property="designerRemark" jdbcType="VARCHAR"/>
        <result column="communication_notes" property="communicationNotes" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, store_name, account_name, m_id, master_order_no, color_count, design_submit_time,
        refuse_time, close_time, agree_time, reference_image, logo_image, logo_size,
        sample_image, design_image, status, refuse_reason, store_remark, designer_remark,
        communication_notes, create_time, update_time
    </sql>

    <!-- 新增 -->
    <insert id="insert" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequest" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customization_request
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeName != null and storeName != ''">store_name,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="mId != null">m_id,</if>
            <if test="masterOrderNo != null and masterOrderNo != ''">master_order_no,</if>
            <if test="colorCount != null">color_count,</if>
            <if test="designSubmitTime != null">design_submit_time,</if>
            <if test="refuseTime != null">refuse_time,</if>
            <if test="closeTime != null">close_time,</if>
            <if test="agreeTime != null">agree_time,</if>
            <if test="referenceImage != null">reference_image,</if>
            <if test="logoImage != null">logo_image,</if>
            <if test="logoSize != null">logo_size,</if>
            <if test="sampleImage != null">sample_image,</if>
            <if test="designImage != null">design_image,</if>
            <if test="status != null">status,</if>
            <if test="refuseReason != null">refuse_reason,</if>
            <if test="storeRemark != null">store_remark,</if>
            <if test="designerRemark != null">designer_remark,</if>
            <if test="communicationNotes != null">communication_notes,</if>
            create_time, update_time
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="storeName != null and storeName != ''">#{storeName},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="mId != null">#{mId},</if>
            <if test="masterOrderNo != null and masterOrderNo != ''">#{masterOrderNo},</if>
            <if test="colorCount != null">#{colorCount},</if>
            <if test="designSubmitTime != null">#{designSubmitTime},</if>
            <if test="refuseTime != null">#{refuseTime},</if>
            <if test="closeTime != null">#{closeTime},</if>
            <if test="agreeTime != null">#{agreeTime},</if>
            <if test="referenceImage != null">#{referenceImage},</if>
            <if test="logoImage != null">#{logoImage},</if>
            <if test="logoSize != null">#{logoSize},</if>
            <if test="sampleImage != null">#{sampleImage},</if>
            <if test="designImage != null">#{designImage},</if>
            <if test="status != null">#{status},</if>
            <if test="refuseReason != null">#{refuseReason},</if>
            <if test="storeRemark != null">#{storeRemark},</if>
            <if test="designerRemark != null">#{designerRemark},</if>
            <if test="communicationNotes != null">#{communicationNotes},</if>
            NOW(), NOW()
        </trim>
    </insert>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM customization_request WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM customization_request WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新 -->
    <update id="updateById" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequest">
        UPDATE customization_request
        <set>
            <if test="storeName != null and storeName != ''">store_name = #{storeName},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="mId != null">m_id = #{mId},</if>
            <if test="masterOrderNo != null and masterOrderNo != ''">master_order_no = #{masterOrderNo},</if>
            <if test="colorCount != null">color_count = #{colorCount},</if>
            <if test="designSubmitTime != null">design_submit_time = #{designSubmitTime},</if>
            <if test="refuseTime != null">refuse_time = #{refuseTime},</if>
            <if test="closeTime != null">close_time = #{closeTime},</if>
            <if test="agreeTime != null">agree_time = #{agreeTime},</if>
            <if test="referenceImage != null">reference_image = #{referenceImage},</if>
            <if test="logoImage != null">logo_image = #{logoImage},</if>
            <if test="logoSize != null">logo_size = #{logoSize},</if>
            <if test="sampleImage != null">sample_image = #{sampleImage},</if>
            <if test="designImage != null">design_image = #{designImage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="refuseReason != null">refuse_reason = #{refuseReason},</if>
            <if test="storeRemark != null">store_remark = #{storeRemark},</if>
            <if test="designerRemark != null">designer_remark = #{designerRemark},</if>
            <if test="communicationNotes != null">communication_notes = #{communicationNotes},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>
    <update id="updateStatusByMasterOrderNo">
        UPDATE customization_request
        SET status = #{status}
        WHERE master_order_no = #{masterOrderNo} and status != #{status}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request
        WHERE id = #{id}
    </select>

    <!-- 根据主订单编号和客户ID查询 -->
    <select id="selectByOrderNoAndMId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request
        WHERE master_order_no = #{masterOrderNo} AND m_id = #{mId}
    </select>

    <!-- 根据客户ID查询列表 -->
    <select id="selectByMId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request
        WHERE m_id = #{mId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询列表 -->
    <select id="selectByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request
        WHERE status = #{status}
        ORDER BY id DESC
    </select>

    <!-- 分页查询列表 -->
    <select id="selectList" parameterType="net.summerfarm.manage.domain.customization.param.CustomizationRequestQueryParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request
        <where>
            master_order_no != '' and master_order_no is not null
            <if test="storeName != null and storeName != ''">
                AND store_name LIKE CONCAT('%', #{storeName}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="masterOrderNo != null and masterOrderNo != ''">
                AND master_order_no = #{masterOrderNo}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 查询总数 -->
    <select id="selectCount" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequest" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM customization_request
        <where>
            <if test="storeName != null and storeName != ''">
                AND store_name LIKE CONCAT('%', #{storeName}, '%')
            </if>
            <if test="accountName != null and accountName != ''">
                AND account_name LIKE CONCAT('%', #{accountName}, '%')
            </if>
            <if test="mId != null">
                AND m_id = #{mId}
            </if>
            <if test="masterOrderNo != null and masterOrderNo != ''">
                AND master_order_no = #{masterOrderNo}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>
    <select id="queryByMasterOrderNo"
            resultType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequest">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request
        WHERE master_order_no = #{masterOrderNo}
    </select>

</mapper>
