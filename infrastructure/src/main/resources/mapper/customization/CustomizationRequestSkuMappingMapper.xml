<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.customization.CustomizationRequestSkuMappingMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="customization_request_id" property="customizationRequestId" jdbcType="BIGINT"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="source_sku" property="sourceSku" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, customization_request_id, sku, source_sku, create_time, update_time
    </sql>

    <!-- 新增 -->
    <insert id="insert" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customization_request_sku_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customizationRequestId != null">customization_request_id,</if>
            <if test="sku != null and sku != ''">sku,</if>
            <if test="sourceSku != null and sourceSku != ''">source_sku,</if>
            create_time, update_time
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="customizationRequestId != null">#{customizationRequestId},</if>
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="sourceSku != null and sourceSku != ''">#{sourceSku},</if>
            NOW(), NOW()
        </trim>
    </insert>

    <!-- 批量新增 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customization_request_sku_mapping
        (customization_request_id, sku, source_sku, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customizationRequestId},  #{item.sku}, #{item.sourceSku}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM customization_request_sku_mapping WHERE id = #{id}
    </delete>

    <!-- 根据定制需求ID删除 -->
    <delete id="deleteByCustomizationRequestId" parameterType="java.lang.Long">
        DELETE FROM customization_request_sku_mapping WHERE customization_request_id = #{customizationRequestId}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM customization_request_sku_mapping WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新 -->
    <update id="updateById" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping">
        UPDATE customization_request_sku_mapping
        <set>
            <if test="customizationRequestId != null">customization_request_id = #{customizationRequestId},</if>
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="sourceSku != null and sourceSku != ''">source_sku = #{sourceSku},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request_sku_mapping
        WHERE id = #{id}
    </select>

    <!-- 根据定制需求ID查询 -->
    <select id="selectByCustomizationRequestId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request_sku_mapping
        WHERE customization_request_id = #{customizationRequestId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据sku查询 -->
    <select id="selectBySku" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request_sku_mapping
        WHERE sku = #{sku}
        ORDER BY create_time DESC
    </select>

    <!-- 根据模版sku查询 -->
    <select id="selectBySourceSku" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request_sku_mapping
        WHERE source_sku = #{sourceSku}
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询列表 -->
    <select id="selectList" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request_sku_mapping
        <where>
            <if test="customizationRequestId != null">
                AND customization_request_id = #{customizationRequestId}
            </if>
            <if test="sku != null and sku != ''">
                AND sku = #{sku}
            </if>
            <if test="sourceSku != null and sourceSku != ''">
                AND source_sku = #{sourceSku}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 查询总数 -->
    <select id="selectCount" parameterType="net.summerfarm.manage.infrastructure.model.customization.CustomizationRequestSkuMapping" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM customization_request_sku_mapping
        <where>
            <if test="customizationRequestId != null">
                AND customization_request_id = #{customizationRequestId}
            </if>
            <if test="sku != null and sku != ''">
                AND sku = #{sku}
            </if>
            <if test="sourceSku != null and sourceSku != ''">
                AND source_sku = #{sourceSku}
            </if>
        </where>
    </select>

    <!-- 查询创建时间超过指定时间的记录 -->
    <select id="selectByCreateTimeBefore" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM customization_request_sku_mapping
        WHERE create_time &lt; #{beforeTime}
        ORDER BY create_time ASC
    </select>

</mapper>
