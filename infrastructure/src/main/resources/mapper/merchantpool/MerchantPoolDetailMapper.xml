<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchantPool.mapper.MerchantPoolDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.merchantpool.MerchantPoolDetail">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="pool_info_id" jdbcType="BIGINT" property="poolInfoId"/>
    <result column="m_id" jdbcType="BIGINT" property="mId"/>
    <result column="size" jdbcType="VARCHAR" property="size"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="version" jdbcType="INTEGER" property="version"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `pool_info_id`, `m_id`, `size`, `area_no`, `version`, `create_time`, `update_time`
  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="poolInfoId != null">
        AND t.pool_info_id = #{poolInfoId}
      </if>
      <if test="mId != null">
        AND t.m_id = #{mId}
      </if>
      <if test="size != null and size !=''">
        AND t.size = #{size}
      </if>
      <if test="areaNo != null">
        AND t.area_no = #{areaNo}
      </if>
      <if test="version != null">
        AND t.version = #{version}
      </if>
      <if test="createTime != null">
        AND t.create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND t.update_time = #{updateTime}
      </if>
    </trim>
  </sql>

  <!-- 根据条件查询对象 -->
  <select id="selectByPoolInfoIds" resultMap="BaseResultMap">
    SELECT t.m_id, t.pool_info_id
    FROM merchant_pool_detail t
    where t.pool_info_id IN
    <foreach collection="poolInfoIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
    </foreach>
    group by t.pool_info_id, t.m_id
    order by t.version desc
  </select>
</mapper>