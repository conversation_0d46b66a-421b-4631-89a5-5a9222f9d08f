<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.MerchantPoolInfoMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.merchantpool.MerchantPoolInfo">
        <!--@mbg.generated-->
        <!--@Table merchant_pool_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_way" jdbcType="TINYINT" property="createWay"/>
        <result column="update_way" jdbcType="TINYINT" property="updateWay"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_source" jdbcType="TINYINT" property="dataSource"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, create_way, update_way, `status`, remark, version, creator, updater,
        create_time, update_time, data_source
    </sql>

    <!--auto generated by MybatisCodeHelper on 2024-12-23-->
    <select id="selectByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from merchant_pool_info
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>