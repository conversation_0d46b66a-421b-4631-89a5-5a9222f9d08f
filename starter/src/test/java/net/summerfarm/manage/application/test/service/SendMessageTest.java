package net.summerfarm.manage.application.test.service;

import net.summerfarm.manage.application.service.merchant.MerchantFrequentlyBuyingSkuNotificationConfigQueryService;
import net.summerfarm.manage.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/5/23 13:53
 * @PackageName:net.summerfarm.manage.application.test.service
 * @ClassName: SendMessageTest
 * @Description: TODO
 * @Version 1.0
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class SendMessageTest {

    @Resource
    private MerchantFrequentlyBuyingSkuNotificationConfigQueryService notificationConfigQueryService;

    @Test
    public void notificationToMerchant() {
        notificationConfigQueryService.notificationToMerchant(9822L);
    }
}
