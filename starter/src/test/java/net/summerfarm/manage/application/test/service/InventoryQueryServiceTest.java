package net.summerfarm.manage.application.test.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.product.mall.InventoryQueryService;
import net.summerfarm.manage.facade.wms.CabinetInventoryFacade;
import net.summerfarm.manage.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description
 * @Date 2025/5/6 16:57
 * @<AUTHOR>
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class InventoryQueryServiceTest {

    @Resource
    private CabinetInventoryFacade cabinetInventoryFacade;

    @Test
    public void test() {
        Map<String, String> result = cabinetInventoryFacade.querySkuCabinetCodeMap(2, Lists.newArrayList("299355034442"));
        log.info("result:{}", JSON.toJSONString(result));
    }

}
