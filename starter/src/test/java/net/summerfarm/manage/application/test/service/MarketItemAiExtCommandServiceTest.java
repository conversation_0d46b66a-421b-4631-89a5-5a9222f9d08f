package net.summerfarm.manage.application.test.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class MarketItemAiExtCommandServiceTest {

    @Resource
    private MarketItemAiExtCommandService marketItemAiExtCommandService;

    @Test
    public void testbatchInitMarketItemQuestions() {
        BatchInitResult result = marketItemAiExtCommandService.batchInitMarketItemQuestions(
                Arrays.asList("5488620832", "5470608464", "5471274211"));
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    public void testrefreshMarketItemQuestions() {
        BatchInitResult result = marketItemAiExtCommandService.refreshMarketItemQuestions(
                Arrays.asList("5488620832", "5470608464", "5471274211"));
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    public void testtestProductInfoText() {
        Map<String, String> result = marketItemAiExtCommandService.getProductInfoTextBySkus(
                Arrays.asList("18650638620"));
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    public void testtestSearchKeywordsBySku() {
        Map<String, String> result = marketItemAiExtCommandService.getSearchKeywordsBySkus(
                Arrays.asList("18650638620"));
        log.info("result:{}", JSON.toJSONString(result));
    }
}
