package net.summerfarm.manage.application.test.service;

import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: xiaowk
 * @time: 2024/7/29 下午4:58
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class AreaSkuCommandServiceTest {

    @Autowired
    private AreaSkuCommandService areaSkuCommandService;

    @Autowired
    private OrderCommandService orderCommandService;


    @Test
    public void updateAreaSkuOnSale() {
//        Integer cnt = areaSkuCommandService.updateAreaSkuOnSale("2188586238354", 479, true);
        Integer cnt = areaSkuCommandService.updateAreaSkuOnSale("4831436333", 479, true);
        System.err.println(cnt);
    }

    @Test
    public void autoCompleteReceipt() {
        orderCommandService.autoCompleteReceipt("022471ZBVO1127163886");
    }
}
