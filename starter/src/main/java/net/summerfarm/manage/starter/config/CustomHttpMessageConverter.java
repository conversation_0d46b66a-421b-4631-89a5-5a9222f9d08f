package net.summerfarm.manage.starter.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/9/13  10:16
 */
@Configuration
public class CustomHttpMessageConverter implements WebMvcConfigurer {

    @Bean
    public FastJsonHttpMessageConverter fastJsonHttpMessageConverter() {
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();

        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(
               /* //输出key时是否使用双引号,默认为true
                SerializerFeature.QuoteFieldNames,*/
              /*  //List null-> []
                SerializerFeature.WriteNullListAsEmpty,*/
                //日期格式化
                SerializerFeature.WriteDateUseDateFormat,
              /*  //String null -> ""
                SerializerFeature.WriteNullStringAsEmpty*/
                SerializerFeature.DisableCircularReferenceDetect
        );

        //这里的字段返回格式，具体要参考各自目前系统中的返回格式再做对应的配置，避免升级后，
        //相同的字段，返回给前端展示的格式不相同，导致前端处理有问题


        List<MediaType> mediaTypeList = new ArrayList<>();
        mediaTypeList.add(MediaType.APPLICATION_JSON);
        fastJsonHttpMessageConverter.setSupportedMediaTypes(mediaTypeList);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
        return fastJsonHttpMessageConverter;
    }

    /**
     * 在ResponseBody注解下，Spring处理返回值为String时会用到StringHttpMessageConverter，我们只需要在配置文件中设置好他的编译编码就ok了
     *
     * @return StringHttpMessageConverter
     */
    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        StringHttpMessageConverter httpMessageConverter = new StringHttpMessageConverter();
        httpMessageConverter.setDefaultCharset(Charset.defaultCharset());
        return httpMessageConverter;
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.clear();
        StringHttpMessageConverter converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        converters.add(converter);
        converters.add(fastJsonHttpMessageConverter());
    }
}
