package net.summerfarm.manage.starter.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.io.InputStreamSource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.common.aspect
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/8/17
 */

@Aspect
@Component
@Slf4j
public class OperateAspect {

    @Resource
    private AdminDataPermissionQueryRepository adminDataPermissionQueryRepository;
    @Resource
    private RedisTemplate redisTemplate;

    @Before(value = "net.summerfarm.manage.starter.aspect.OperateAspect.PointCut.systemLogPointCut()")
    public void before(JoinPoint joinPoint) {
        //获取方法名
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        if ("login".equalsIgnoreCase(methodName)) {
            log.info("管理员{}登录了", args[0]);
            return;
        }

        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user != null && user.getBizUserId() != null) {
            String dataPermissionKey = String.format("auth-data-permission:%s", user.getBizUserId());

            // Set<Integer> dataPermission = adminDataPermissionService.selectDataPermissionIds(user.getBizUserId().intValue());
            // redisTemplate.opsForValue().set(dataPermissionKey, JSONObject.toJSONString(dataPermission), 240L, TimeUnit.MINUTES);
            AdminDataPermissionQueryParam dataPermissionParam = new AdminDataPermissionQueryParam();
            dataPermissionParam.setAdminId(user.getBizUserId().intValue());
            List<AdminDataPermissionEntity> dataPermission = adminDataPermissionQueryRepository.selectByCondition(dataPermissionParam);
            Set<Integer> dataPermissionSet = dataPermission.stream().map(AdminDataPermissionEntity::getId).collect(Collectors.toSet());
            redisTemplate.opsForValue().set(dataPermissionKey, JSONObject.toJSONString(dataPermissionSet), 240L, TimeUnit.MINUTES);
        }
    }

    @AfterReturning(value = "within(net.summerfarm.manage.application.inbound.controller.*))  && !@annotation(net.summerfarm.manage.starter.aspect.UnCollectByAspect)", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            HttpServletRequest request = RequestHolder.getRequest();
            String tab = request.getHeader("tab");
            String part = request.getHeader("part");
            Long adminId = null;
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            if (user != null) {
                adminId = user.getBizUserId();
            }

            String paraStr = "";
            if (joinPoint.getArgs().length != 0) {
                List<Object> logArgs = Arrays.stream(joinPoint.getArgs())
                        .filter(arg -> !(arg instanceof HttpServletRequest))
                        .filter(arg -> !(arg instanceof HttpServletResponse))
                        .filter(arg -> !(arg instanceof BeanPropertyBindingResult))
                        .map(arg -> {
                            if (arg instanceof InputStreamSource) {
                                return "文件流";
                            }
                            return arg;
                        })
                        .collect(Collectors.toList());
                paraStr = JSON.toJSONString(logArgs);
            } else if (request.getParameterMap().size() != 0) {
                paraStr = JSON.toJSONString(request.getParameterMap());
            }

            String resultStr = JSON.toJSONString(result);
            DataBuryPoint.logIntoDBP(tab, part, request.getRequestURI(), request.getMethod(), adminId, paraStr, resultStr);

        } catch (Exception e) {
            log.warn("数据埋点日志处理异常", e);
            e.printStackTrace();
        }
    }

    /**
     * 使用Java反射来获取被拦截方法(insert、update)的参数值，
     * 将参数值拼接为操作内容
     *
     * @param args
     * @param mName
     * @return
     */
    public String optionContent(Object[] args, String mName) {
        if (args == null) {
            return null;
        }
        StringBuffer rs = new StringBuffer();
        rs.append(mName);
        String className = null;
        int index = 1;
        //遍历参数对象
        for (Object info : args) {
            //获取对象类型
            className = info.getClass().getName();
            className = className.substring(className.lastIndexOf(".") + 1);
            rs.append("[参数" + index + "，类型:" + className + "，值:");
            //获取对象的所有方法
            Method[] methods = info.getClass().getDeclaredMethods();
            // 遍历方法，判断get方法
            for (Method method : methods) {
                String methodName = method.getName();
                // 判断是不是get方法
                if (methodName.indexOf("get") == -1) {//不是get方法
                    continue;//不处理
                }
                Object rsValue = null;
                try {
                    // 调用get方法，获取返回值
                    rsValue = method.invoke(info);
                } catch (Exception e) {
                    continue;
                }
                //将值加入内容中
                rs.append("(" + methodName + ":" + rsValue + ")");
            }
            rs.append("]");
            index++;
        }
        return rs.toString();
    }



    class PointCut {

        @Pointcut(value = "within(net.summerfarm.manage.application.inbound.controller.*)")
        public void systemLogPointCut() {

        }
    }
}
