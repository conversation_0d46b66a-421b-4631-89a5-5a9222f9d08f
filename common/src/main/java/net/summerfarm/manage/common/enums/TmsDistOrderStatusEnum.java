package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TmsDistOrderStatusEnum {


    WAIT_CABLE(10, "待排线"),
    CABLE_ING(11, "排线中"),
    WAIT_PICK(20, "待拣货"),
    ON_THE_WAY(30, "配送中"),
    CABLE_INTERCEPT(39, "完成排线后订单拦截"),
    DELIVERY_FINISH(40, "配送完成"),
    ;

    private Integer value;

    private String content;

    /**
     * 返回对应配送状态
     * @param value 类型
     * @return 配送状态
     */
    public static Integer getDistOrderStatus(Integer value){
        if (value.equals(TmsDistOrderStatusEnum.WAIT_PICK.getValue())){
            return PathStatusEnum.toBePicked.getCode();
        }
        if (value.equals(TmsDistOrderStatusEnum.ON_THE_WAY.getValue()) ||
                value.equals(TmsDistOrderStatusEnum.CABLE_INTERCEPT.getValue())){
            return PathStatusEnum.pickedComplete.getCode();
        }
        if (value.equals(TmsDistOrderStatusEnum.DELIVERY_FINISH.getValue())){
            return PathStatusEnum.sendComplete.getCode();
        }
        if (value.equals(TmsDistOrderStatusEnum.WAIT_CABLE.getValue()) ||
                value.equals(TmsDistOrderStatusEnum.CABLE_ING.getValue())){
            return DeliveryStatusEnum.TO_BE_WIRED.getStatus();
        }
        return null;
    }

}
