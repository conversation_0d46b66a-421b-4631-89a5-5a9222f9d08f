package net.summerfarm.manage.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.constants.Conf;
import net.summerfarm.manage.common.constants.Global;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * @ClassName WeChatUtils
 * @Description
 * <AUTHOR>
 * @Date 13:48 2024/2/20
 * @Version 1.0
 **/
@Slf4j
public class WeChatUtils {

    public static String getWeChatCode(String url) {
        return getWeChatCode(url, null);
    }

    /**
     *  获取授权code
     *  switch (scope)
     *  {
     *  case snsapi_base : 静默授权; break;
     *  case snsapi_userinfo : 主动授权; break;
     *  }
     *  强制主动授权
     * @param url
     * @param scope
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getWeChatCode(String url, String scope) {
        String code = null;
        url = url.contains(Global.DOMAIN_NAME) ? url : Global.DOMAIN_NAME + url;
        try {
            code = "https://open.weixin.qq.com/connect/oauth2/authorize?"
                    + "appid="+ URLEncoder.encode(Conf.APP_Id,"UTF-8")
                    +"&redirect_uri="+URLEncoder.encode(url,"UTF-8")
                    + "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            log.error("获取微信授权码失败", e);
        }
        return code;
    }
}
