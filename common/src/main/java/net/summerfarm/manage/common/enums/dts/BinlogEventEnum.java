package net.summerfarm.manage.common.enums.dts;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BinlogEventEnum {
    /**
     * 新增
     */
    INSERT("INSERT", "新增"),
    /**
     * 修改
     */
    UPDATE("UPDATE", "修改"),
    /**
     * 删除
     */
    DELETE("DELETE", "删除");

    /**
     * 事件
     */
    private String event;
    /**
     * 事件描述
     */
    private String eventDesc;
}
