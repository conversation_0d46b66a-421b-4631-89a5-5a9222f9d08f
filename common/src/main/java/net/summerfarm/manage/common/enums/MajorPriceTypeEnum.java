package net.summerfarm.manage.common.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/4/8 16:40
 */
public enum MajorPriceTypeEnum {

    /**
     * 商城价
     */
    MALL_PRICE(0, "商城价"),

    /**
     * 合同价（指定价）
     */
    CONTRACT_PRICE_SPECIFIED(1, "合同价（指定价）"),

    /**
     * 合同价（毛利率）
     */
    CONTRACT_PRICE_MARGIN(2, "合同价（毛利率）"),

    MALL_PRICE_ADD_RATE(3, "商城价上浮"),

    MALL_PRICE_SUB_RATE(4, "商城价下浮"),

    MALL_PRICE_ADD_PRICE(5, "商城价加价"),

    MALL_PRICE_SUB_PRICE(6, "商城价减价"),
    ;

    public static final List<Integer> MALL_RELATED = new ArrayList<>(Arrays.asList(
            MajorPriceTypeEnum.MALL_PRICE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_ADD_RATE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_SUB_RATE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_ADD_PRICE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_SUB_PRICE.getCode()
    ));
    // 枚举值
    private final Integer code;

    // 枚举描述
    private final String value;

    // 构造函数，用于初始化枚举值和描述
    MajorPriceTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    // 获取枚举值的方法
    public Integer getCode() {
        return code;
    }

    // 获取枚举描述的方法
    public String getValue() {
        return value;
    }

    // 静态方法，根据code获取对应的枚举实例
    public static MajorPriceTypeEnum fromCode(Integer code) {
        for (MajorPriceTypeEnum type : MajorPriceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
    public static MajorPriceTypeEnum fromValue(String value) {
        for (MajorPriceTypeEnum type : MajorPriceTypeEnum.values()) {
            if (type.getValue ().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
