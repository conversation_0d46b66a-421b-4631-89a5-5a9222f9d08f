package net.summerfarm.manage.common.input.order;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName AutoOrderSendCouponDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 18:41 2024/1/23
 * @Version 1.0
 **/
@Data
public class AutoOrderSendCouponDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 发放卡券的商户id 必传
     */
    private Long mId;

    /**
     * 微信openid
     */
    private String openId;
}
