package net.summerfarm.manage.common.input.wx;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/5/22 18:00
 * @PackageName:net.summerfarm.manage.common.input.wx
 * @ClassName: ActivityNotice
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivityNotice {

    /**
     * 商户openid
     */
    private String openId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动sku
     */
    private String sku;

    /**
     * 商品规格
     */
    private String weight;

    /**
     * 活动价格
     */
    private BigDecimal  activityPrice;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime  activityEndTime;
}
