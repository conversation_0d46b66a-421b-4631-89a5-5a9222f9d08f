package net.summerfarm.manage.common.enums.products;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-08-05
 * @description
 */
public interface ProductsEnum {
    /**
     * SPU生命周期
     */
    @Getter
    @AllArgsConstructor
    enum Outdated {
        /**
         * 1、上新中
         */
        CREATING(-1, "上新中"),
        /**
         * 0、有效
         */
        VALID(0, "有效"),
        /**
         * 1、已删除
         */
        IN_VALID(1, "已删除");

        private final Integer code;

        private final String desc;
    }

    /**
     * 上新审核状态
     */
    enum AuditStatus {
        /**
         * 0、待审核
         */
        WAIT_AUDIT,
        /**
         * 1、审核通过
         */
        SUCCESS,
        /**
         * 2、审核失败
         */
        FAIL
    }

    /**
     * 上新类型
     */
    enum CreateType {
        /**
         * 0、平台
         */
        PLATFORM,
        /**
         * 1、大客户
         */
        KA,
        /**
         * 2、帆台代仓
         */
        FAN_TAI_AGENT
    }

    /**
     * 温区(0:未分类,1:冷冻,2:冷藏,3:常温,4:顶汇大流通)
     */
    @Getter
    @AllArgsConstructor
    enum StorageLocation implements Enum2Args {
        DEFAULT(0, "未分类"),
        FREEZE(1, "冷冻"),
        COLD(2, "冷藏"),
        NORMAL(3, "常温"),
        DING_HUI(4, "顶汇大流通"),
        ;

        private Integer value;
        private String content;

    }
}
