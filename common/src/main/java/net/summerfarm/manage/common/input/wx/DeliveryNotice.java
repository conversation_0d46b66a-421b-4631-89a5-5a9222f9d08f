package net.summerfarm.manage.common.input.wx;

import lombok.Data;

import java.time.LocalDate;

/**
 * @ClassName DeliveryNotice
 * @Description
 * <AUTHOR>
 * @Date 14:14 2024/2/20
 * @Version 1.0
 **/
@Data
public class DeliveryNotice {

    /**
     * 商户openid
     */
    private String openId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 详细地址
     */
    private String contact;

    /**
     * 收货人手机号
     */
    private String phone;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 配送数量
     */
    private Integer quantity;
}
