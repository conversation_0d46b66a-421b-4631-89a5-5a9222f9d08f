package net.summerfarm.manage.common.enums.download;


import java.util.Date;

/**
 * 描述: Excel枚举类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
public enum ExcelTypeEnum {

    /**
     * 大客户报价单
     */
    MAJOR_PRICE_EXCEL("major_price_excel.xlsx", "大客户报价单"),
    ;

    private String name;
    private String desc;

    ExcelTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return this.name;
    }

    public String getDesc() {
        return this.desc;
    }

}
