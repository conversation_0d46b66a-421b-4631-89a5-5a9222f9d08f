package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
@Getter
@AllArgsConstructor
public enum CloseSaleTypeEnum {
    TURN_OFF(0,"下架"),
    SALE_OUT_TURN_OFF(1,"售罄下架"),
    TURN_OFF_TIMER(2,"定时下架"),
    ;
    private final Integer type;
    private final String operator;

    public static CloseSaleTypeEnum ofByOperator(String operator) {
        for (CloseSaleTypeEnum e: values()) {
            if (e.getOperator ().equals(operator)) {
                return e;
            }
        }
        return null;
    }

}
