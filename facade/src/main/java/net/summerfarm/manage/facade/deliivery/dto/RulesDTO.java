package net.summerfarm.manage.facade.deliivery.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/9/27 15:28:42
 */
@Data
public class RulesDTO implements Serializable {

    private static final long serialVersionUID = -8988169351859791437L;

    /**
     * 城市编号-针对品牌管理类型   0代表全部城市
     */
    private Integer areaNo;

    /**
     * 履约时效  0：T+1    1：T+N
     */
    private Integer ageing;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 快递费
     */
    private BigDecimal expressFee;

    /**
     * 条件
     */
    private List<ConditionsDTO> conditionsDTOS;
}
