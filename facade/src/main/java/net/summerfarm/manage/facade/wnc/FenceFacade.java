package net.summerfarm.manage.facade.wnc;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.constants.AppConsts;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.fence.PopFenceQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.StoreQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.StoreQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/19 15:31
 */
@Slf4j
@Component
public class FenceFacade {
    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;


    public Integer getXmAreaByAddress(String city, String area) {
        AreaQueryReq areaQueryReq = new AreaQueryReq();
        areaQueryReq.setArea(area);
        areaQueryReq.setCity(city);
        DubboResponse<AreaQueryResp> dubboResponse = deliveryFenceQueryProvider.queryAreaByAddress(areaQueryReq);
        if (null == dubboResponse || !dubboResponse.isSuccess()){
            log.warn("xm查询wnc获取区域信息失败：city:{}, area:{}, dubboResponse:{}", city, area, JSON.toJSONString(dubboResponse));
            throw new BizException("地址不在配送范围");
        }
        return dubboResponse.getData() == null ? null : dubboResponse.getData().getAreaNo();
    }


    /**
     * 获取城配仓编号
     * @return
     */
    public Integer queryStoreByAddress(String city, String area, String poi){
        if(StringUtils.isEmpty(city)){
            throw new BizException("城市不能为空");
        }
        StoreQueryReq req = new StoreQueryReq();
        req.setCity(city);
        req.setArea(area);
        req.setPoi(poi);
        req.setTenantId(AppConsts.XIANMU_TENANT_ID);
        DubboResponse<StoreQueryResp> response = deliveryFenceQueryProvider.queryStoreByAddress(req);
        if(response == null){
            log.error("DeliveryFenceQueryFacade[]queryStoreByAddress[]response is null,input:{}", JSON.toJSONString(req));
            throw new BizException("根据省市区查询城配仓异常");
        }
        if (!DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())) {
            log.error("DeliveryFenceQueryFacade[]queryStoreByAddress[]error,input:{}", JSON.toJSONString(req));
            throw new ProviderException(response.getMsg());
        }
        StoreQueryResp storeQueryResp = response.getData();
        if(storeQueryResp == null || storeQueryResp.getStoreNo() == null){
            log.error("DeliveryFenceQueryFacade[]queryStoreByAddress[]storeQueryResp is null,input:{}", JSON.toJSONString(req));
            throw new BizException("此地区不在鲜沐围栏配送范围内");
        }
        return storeQueryResp.getStoreNo();
    }
}
