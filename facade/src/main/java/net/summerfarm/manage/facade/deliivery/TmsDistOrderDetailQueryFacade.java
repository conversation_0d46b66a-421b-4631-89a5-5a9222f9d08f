package net.summerfarm.manage.facade.deliivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.deliivery.converter.TmsDistOrderDetailConverter;
import net.summerfarm.manage.facade.deliivery.dto.DistOrderDTO;
import net.summerfarm.manage.facade.deliivery.input.DistOrderDetailInput;
import net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @ClassName TmsDistOrderDetailFacade
 * @Description TODO
 * <AUTHOR>
 * @Date 18:30 2024/1/17
 * @Version 1.0
 **/
@Component
@Slf4j
public class TmsDistOrderDetailQueryFacade {

    @DubboReference
    private TmsDistOrderQueryStandardProvider tmsDistOrderQueryStandardProvider;

    /***
     * @author: lzh
     * @description: 获取委托单详情接口
     * @date: 2024/1/18 13:41
     * @param: [input]
     * @return: net.summerfarm.manage.facade.deliivery.dto.DistOrderDTO
     **/
    public DistOrderDTO queryDistOrderDetail(DistOrderDetailInput input) {
        DistOrderQueryStandardReq distOrderQueryStandardReq = TmsDistOrderDetailConverter.convertTmsDistOrderReq(input);
        log.info("TmsDistOrderDetailFacade[]queryDistOrderDetail[]distOrderQueryStandardReq{}", JSON.toJSONString(distOrderQueryStandardReq));
        DubboResponse<DistOrderStandardResp> dubboResponse = tmsDistOrderQueryStandardProvider.queryDistOrderDetail(distOrderQueryStandardReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())
                || null == dubboResponse.getData()) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        log.info("TmsDistOrderDetailFacade[]queryDistOrderDetail[]distOrderStandardResp{}", JSON.toJSONString(dubboResponse.getData()));
        return TmsDistOrderDetailConverter.convertTmsDistOrderResp2DTO(dubboResponse.getData());
    }
}
