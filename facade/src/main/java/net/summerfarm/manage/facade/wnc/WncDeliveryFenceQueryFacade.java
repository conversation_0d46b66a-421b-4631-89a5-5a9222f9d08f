package net.summerfarm.manage.facade.wnc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.wnc.convert.WncConverter;
import net.summerfarm.manage.facade.wnc.dto.AreaWarehouseNoSkuDTO;
import net.summerfarm.manage.facade.wnc.input.SkuWarehouseNoQueryAreaInput;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.fence.AreaQueryWarehouseNoSkuReq;
import net.summerfarm.wnc.client.resp.fence.AreaWarehouseNoSkuResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/16 14:56
 * @PackageName:net.summerfarm.manage.facade.wnc
 * @ClassName: WncDeliveryFenceQueryFacade
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@Component
public class WncDeliveryFenceQueryFacade {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    /***
     * @author: lzh
     * @description: 根据sku+warehouse查询区域编码
     * @date: 2024/4/9 15:22
     * @param: [input]
     * @return: net.xianmu.marketing.center.facade.wnc.dto.AreaWarehouseNoSkuDTO
     **/
    public List<AreaWarehouseNoSkuDTO> queryAreaByListWarehouseAndSku(List<SkuWarehouseNoQueryAreaInput> inputs) {
        if (CollectionUtils.isEmpty(inputs)) {
            return Collections.emptyList();
        }
        AreaQueryWarehouseNoSkuReq req = WncConverter.toAreaQueryWarehouseNoSkuReq(inputs);
        DubboResponse<List<AreaWarehouseNoSkuResp>> dubboResponse = deliveryFenceQueryProvider.queryAreaByListWarehouseAndSku(req);
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
            log.error("WncDeliveryFenceQueryFacade[]queryAreaByListWarehouseAndSku[]error!");
            return Collections.emptyList();
        }
        return WncConverter.toAreaWarehouseNoSkuDTOList(dubboResponse.getData());
    }
}
