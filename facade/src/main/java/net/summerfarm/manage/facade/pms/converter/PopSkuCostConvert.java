package net.summerfarm.manage.facade.pms.converter;

import net.summerfarm.manage.facade.pms.dto.PopSkuCostFacadeDTO;
import net.summerfarm.pms.client.resp.dto.PopSkuCostDTO;

import java.util.Objects;

/**
 * @Description
 * @Date 2024/12/12 10:57
 * @<AUTHOR>
 */
public class PopSkuCostConvert {

    public static PopSkuCostFacadeDTO convert(PopSkuCostDTO popSkuCostDTO) {
        if (Objects.isNull(popSkuCostDTO)) {
            return null;
        }
        return PopSkuCostFacadeDTO.builder()
                .sku(popSkuCostDTO.getSku())
                .supplierWeightPrice(popSkuCostDTO.getSupplierWeightPrice())
                .supplierCost(popSkuCostDTO.getSupplierCost())
                .build();
    }

}
