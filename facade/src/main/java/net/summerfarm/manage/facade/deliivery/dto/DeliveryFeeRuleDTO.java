package net.summerfarm.manage.facade.deliivery.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支持阶梯价的运费规则对象
 *
 * <AUTHOR>
 */
@Data
public class DeliveryFeeRuleDTO {

    /**
     * 履约时效，0：日配  1：非日配
     */
    private Integer ageing;

    /**
     * 起送金额
     */
    private BigDecimal startDeliveryAmount;

    /**
     * 运费规则详情
     */
    private List<DeliveryFeeRuleDetailDTO> deliveryFeeRuleDetailDTOList;

    @Data
    public static class DeliveryFeeRuleDetailDTO {

        /**
         * 货品类目  1：全部 2：乳制品 3：非乳制品
         */
        private Integer categoryType;

        /**
         * 计算运费的方式 1：金额 2：件数
         */
        private Integer feeMode;

        /**
         * 阶梯值
         */
        private String stepValue;

        /**
         * 配送费
         */
        private BigDecimal deliveryFee;

        /**
         * 快递费
         */
        private BigDecimal expressFee;
    }
}
