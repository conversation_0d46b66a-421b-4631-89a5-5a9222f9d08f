package net.summerfarm.manage.facade.inventory.convert;

import net.summerfarm.manage.facade.inventory.dto.AreaStoreQueryRes;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 11:21
 * @Version 1.0
 */
public class SaleInventoryConverter {
    public static List<AreaStoreQueryRes> convertToAreaStoreQueryResList(List<WarehouseSkuInventoryDetailResDTO> detailResDTOList) {
        List<AreaStoreQueryRes> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(detailResDTOList)) {
            return resultList;
        }
        detailResDTOList.forEach(e -> {
            AreaStoreQueryRes areaStoreQueryRes = new AreaStoreQueryRes();
            areaStoreQueryRes.setSkuCode(e.getSkuCode());
            areaStoreQueryRes.setAvailableQuantity(e.getAvailableQuantity());
            areaStoreQueryRes.setQuantity(e.getQuantity());
            areaStoreQueryRes.setOnlineQuantity(e.getOnlineQuantity());
            areaStoreQueryRes.setDeliveryTime(e.getDeliveryTime());
            areaStoreQueryRes.setCloseTime(e.getCloseTime());
            areaStoreQueryRes.setDeliveryCloseTime(e.getDeliveryCloseTime());
            areaStoreQueryRes.setIsEveryDayFlag(e.getIsEveryDayFlag());
            areaStoreQueryRes.setSkuSubType(e.getSkuSubType());
            areaStoreQueryRes.setReserveUseQuantity(e.getReserveUseQuantity());
            areaStoreQueryRes.setReserveMaxQuantity(e.getReserveMaxQuantity());
            areaStoreQueryRes.setReserveMinQuantity(e.getReserveMinQuantity());
            areaStoreQueryRes.setCostPrice(e.getCostPrice());
            areaStoreQueryRes.setWarehouseNo(e.getWarehouseNo());
            resultList.add(areaStoreQueryRes);
        });
        return  resultList;
    }
}
