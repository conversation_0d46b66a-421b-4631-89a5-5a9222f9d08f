package net.summerfarm.manage.facade.merchant.converter;

import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MerchantQueryConverter {


    private MerchantQueryConverter() {
        // 无需实现
    }

    public static List<MerchantStoreQueryReq> toMerchantStoreQueryReqList(List<MerchantQueryInput> merchantQueryInputList) {
        if (merchantQueryInputList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreQueryReq> merchantStoreQueryReqList = new ArrayList<>();
        for (MerchantQueryInput merchantQueryInput : merchantQueryInputList) {
            merchantStoreQueryReqList.add(toMerchantStoreQueryReq(merchantQueryInput));
        }
        return merchantStoreQueryReqList;
    }

    public static MerchantStoreQueryReq toMerchantStoreQueryReq(MerchantQueryInput merchantQueryInput) {
        if (merchantQueryInput == null) {
            return null;
        }
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setStatus(merchantQueryInput.getStatus());
        merchantStoreQueryReq.setType(merchantQueryInput.getType());
        merchantStoreQueryReq.setPhone(merchantQueryInput.getPhone());
        merchantStoreQueryReq.setMId(merchantQueryInput.getMId());
        merchantStoreQueryReq.setAreaNo(merchantQueryInput.getAreaNo());
        merchantStoreQueryReq.setSize(merchantQueryInput.getSize());
        merchantStoreQueryReq.setDirect(merchantQueryInput.getDirect());
        merchantStoreQueryReq.setMIds(merchantQueryInput.getMIds());
        merchantStoreQueryReq.setAreaNos(merchantQueryInput.getAreaNos());
// Not mapped TO fields:
// storeId
// storeIdList
// tenantId
// storeName
// storeNo
// Not mapped FROM fields:
// roleId
// mname
// mcontact
// openid
// rankId
// registerTime
// loginTime
// invitecode
// channelCode
// inviterChannelCode
// auditTime
// auditUser
// businessLicense
// province
// city
// area
// address
// poiNote
// remark
// shopSign
// otherProof
// lastOrderTime
// tradeArea
// tradeGroup
// unionid
// mpOpenid
// adminId
// server
// popView
// memberIntegral
// grade
// skuShow
// rechargeAmount
// cashAmount
// cashUpdateTime
// showPrice
// mergeAdmin
// mergeTime
// firstLoginPop
// changePop
// pullBlackRemark
// pullBlackOperator
// houseNumber
// companyBrand
// cluePool
// merchantType
// enterpriseScale
// updateTime
// examineType
// displayButton
// operateStatus
// updater
// doorPic
// preRegisterFlag
// merchantLabel
// merchantLabelList
// recordId
// pageIndex
// pageSize
// sortList
        return merchantStoreQueryReq;
    }

    public static List<MerchantQueryInput> toMerchantQueryInputList(List<MerchantStoreQueryReq> merchantStoreQueryReqList) {
        if (merchantStoreQueryReqList == null) {
            return Collections.emptyList();
        }
        List<MerchantQueryInput> merchantQueryInputList = new ArrayList<>();
        for (MerchantStoreQueryReq merchantStoreQueryReq : merchantStoreQueryReqList) {
            merchantQueryInputList.add(toMerchantQueryInput(merchantStoreQueryReq));
        }
        return merchantQueryInputList;
    }

    public static MerchantQueryInput toMerchantQueryInput(MerchantStoreQueryReq merchantStoreQueryReq) {
        if (merchantStoreQueryReq == null) {
            return null;
        }
        MerchantQueryInput merchantQueryInput = new MerchantQueryInput();
        merchantQueryInput.setMId(merchantStoreQueryReq.getMId());
        merchantQueryInput.setPhone(merchantStoreQueryReq.getPhone());
        merchantQueryInput.setStatus(merchantStoreQueryReq.getStatus());
        merchantQueryInput.setAreaNo(merchantStoreQueryReq.getAreaNo());
        merchantQueryInput.setSize(merchantStoreQueryReq.getSize());
        merchantQueryInput.setType(merchantStoreQueryReq.getType());
        merchantQueryInput.setDirect(merchantStoreQueryReq.getDirect());
        merchantQueryInput.setMIds(merchantStoreQueryReq.getMIds());
        merchantQueryInput.setAreaNos(merchantStoreQueryReq.getAreaNos());
// Not mapped TO fields:
// roleId
// mname
// mcontact
// openid
// rankId
// registerTime
// loginTime
// invitecode
// channelCode
// inviterChannelCode
// auditTime
// auditUser
// businessLicense
// province
// city
// area
// address
// poiNote
// remark
// shopSign
// otherProof
// lastOrderTime
// tradeArea
// tradeGroup
// unionid
// mpOpenid
// adminId
// server
// popView
// memberIntegral
// grade
// skuShow
// rechargeAmount
// cashAmount
// cashUpdateTime
// showPrice
// mergeAdmin
// mergeTime
// firstLoginPop
// changePop
// pullBlackRemark
// pullBlackOperator
// houseNumber
// companyBrand
// cluePool
// merchantType
// enterpriseScale
// updateTime
// examineType
// displayButton
// operateStatus
// updater
// doorPic
// preRegisterFlag
// merchantLabel
// merchantLabelList
// recordId
// pageIndex
// pageSize
// sortList
// Not mapped FROM fields:
// storeId
// storeIdList
// tenantId
// storeName
// storeNo
        return merchantQueryInput;
    }
}
