<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mall-manage</artifactId>
        <groupId>net.summerfarm</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mall-manage-facade</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    
    <dependencies>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>mall-manage-common</artifactId>
        </dependency>


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>marketing-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>mall-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>tms-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xianmu-common</artifactId>
                    <groupId>net.xianmu.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tms-common</artifactId>
                    <groupId>net.summerfarm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xianmu-common</artifactId>
                    <groupId>net.xianmu.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tms-common</artifactId>
                    <groupId>net.summerfarm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>message-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-robot-util</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
            <version>${inventory-client.verison}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>scp-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-pms-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm.wms</groupId>
            <artifactId>summerfarm-wms-client</artifactId>
        </dependency>


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-inventory-sdk</artifactId>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
    </dependencies>
</project>
