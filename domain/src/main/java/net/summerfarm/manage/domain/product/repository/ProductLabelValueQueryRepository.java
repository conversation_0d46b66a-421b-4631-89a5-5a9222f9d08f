package net.summerfarm.manage.domain.product.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.query.ProductLabelValueQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:46
* @version 1.0
*
*/
public interface ProductLabelValueQueryRepository {

    PageInfo<ProductLabelValueEntity> getPage(ProductLabelValueQueryParam param);

    ProductLabelValueEntity selectById(Long id);

    List<ProductLabelValueEntity> selectByCondition(ProductLabelValueQueryParam param);

    List<ProductLabelValueEntity> selectBySku(String sku);
}