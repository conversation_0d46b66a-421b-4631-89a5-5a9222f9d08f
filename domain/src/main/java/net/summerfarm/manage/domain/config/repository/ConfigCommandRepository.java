package net.summerfarm.manage.domain.config.repository;



import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.param.command.ConfigCommandParam;




/**
*
* <AUTHOR>
* @date 2024-06-18 23:48:54
* @version 1.0
*
*/
public interface ConfigCommandRepository {

    ConfigEntity insertSelective(ConfigCommandParam param);

    int updateSelectiveById(ConfigCommandParam param);

    int remove(Long id);

}