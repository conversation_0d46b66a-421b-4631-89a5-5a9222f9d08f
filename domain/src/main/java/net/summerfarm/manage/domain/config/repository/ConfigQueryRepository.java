package net.summerfarm.manage.domain.config.repository;


import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.param.query.ConfigQueryParam;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-18 23:48:54
 */
public interface ConfigQueryRepository {

    PageInfo<ConfigEntity> getPage(ConfigQueryParam param);

    ConfigEntity selectById(Long id);

    ConfigEntity selectByKey(String key);

    List<ConfigEntity> selectByCondition(ConfigQueryParam param);

}