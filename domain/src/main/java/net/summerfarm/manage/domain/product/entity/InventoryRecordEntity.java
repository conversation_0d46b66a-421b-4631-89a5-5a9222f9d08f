package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-06 15:10:53
 * @version 1.0
 *
 */
@Data
public class InventoryRecordEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private String sku;

	/**
	 * 变化字段
	 */
	private String changeField;

	/**
	 * 变化前的值
	 */
	private String oldValue;

	/**
	 * 变化后的值
	 */
	private String newValue;

	/**
	 * 记录人
	 */
	private String recorder;

	/**
	 * 
	 */
	private LocalDateTime addtime;

	

	
}