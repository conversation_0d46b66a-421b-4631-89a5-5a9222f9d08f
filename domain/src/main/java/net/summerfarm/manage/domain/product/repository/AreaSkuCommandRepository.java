package net.summerfarm.manage.domain.product.repository;

import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.command.AreaSkuCommandParam;
import net.summerfarm.manage.domain.product.param.command.AreaSkuOnSaleParam;
import net.summerfarm.manage.domain.product.param.command.AreaSkuPriceCommandParam;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface AreaSkuCommandRepository {

    /**
     * 插入区域SKU
     */
    AreaSkuEntity insertSelective(AreaSkuCommandParam param);

    Integer updateAreaSkuPrice(List<AreaSkuPriceCommandParam> areaSkuPriceS);

    Integer updateAreaSkuOnSale(AreaSkuOnSaleParam areaSkuOnSaleParam);
    void updateAreaSkuOnSaleBatch(List<AreaSkuOnSaleParam> areaSkuOnSaleParams);
    void updateAreaSkuOnSaleBatchBySku(Boolean onSale, Collection<String> skus);

    void offSaleByIds(List<Integer> ids);

    void offSaleBySkus(List<String> skus);
}
