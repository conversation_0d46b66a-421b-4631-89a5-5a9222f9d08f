package net.summerfarm.manage.domain.account.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023-10-26 16:20:19
 * @version 1.0
 *
 */
@Data
public class AccountChangeEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private Long mId;

	/**
	 * 
	 */
	private Long accountId;

	/**
	 * 
	 */
	private String oldPhone;

	/**
	 * 
	 */
	private String oldContact;

	/**
	 * 
	 */
	private String newPhone;

	/**
	 * 
	 */
	private String newContact;

	/**
	 * 
	 */
	private String mname;

	/**
	 * 
	 */
	private String remark;

	/**
	 * 1待审核 2审核通过 3审核失败
	 */
	private Integer status;

	/**
	 * 
	 */
	private String openid;

	/**
	 * 
	 */
	private String unionid;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}