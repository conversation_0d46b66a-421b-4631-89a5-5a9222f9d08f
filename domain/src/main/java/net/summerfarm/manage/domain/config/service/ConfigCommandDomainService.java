package net.summerfarm.manage.domain.config.service;


import net.summerfarm.manage.domain.config.repository.ConfigQueryRepository;
import net.summerfarm.manage.domain.config.repository.ConfigCommandRepository;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.param.command.ConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 商城配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-06-18 23:48:54
 * @version 1.0
 *
 */
@Service
public class ConfigCommandDomainService {


    @Autowired
    private ConfigCommandRepository configCommandRepository;
    @Autowired
    private ConfigQueryRepository configQueryRepository;



    public ConfigEntity insert(ConfigCommandParam param) {
        return configCommandRepository.insertSelective(param);
    }


    public int update(ConfigCommandParam param) {
        return configCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return configCommandRepository.remove(id);
    }
}
