package net.summerfarm.manage.domain.offline.repository;

import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.offline.entity.DataSynchronizationInformationEntity;

/**
*
* <AUTHOR>
* @date 2023-09-19 16:21:24
* @version 1.0
*
*/
public interface DataSynchronizationInformationRepository {

    /**
     * 根据表名获取数据更新信息
     * @param tableName 表名
     * @return 数据更新信息
     */
    DataSynchronizationInformationEntity selectByTableName(String tableName);
}