package net.summerfarm.manage.domain.merchant.service;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.util.PoiSearchUtil;
import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.repository.ContactRepository;
import net.summerfarm.manage.facade.wnc.FenceFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;



/**
 *
 * @Title: 联系人业务逻辑实现类
 * @Description:
 * <AUTHOR>
 * @date 2023-09-19 16:21:24
 * @version 1.0
 *
 */
@Service
@Slf4j
public class ContactDomainService {


    @Autowired
    private ContactRepository contactRepository;

    @Autowired
    private FenceFacade fenceFacade;
    
    public Long addContact(MerchantEntity merchant){
        log.info("【单个创建门店】开始初始化门店的地址。merchant：{}", JSON.toJSONString(merchant));
        ContactEntity contact = new ContactEntity();
        contact.setContact(merchant.getMcontact());
        contact.setMId(merchant.getMId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        contact.setAddress(merchant.getAddress());
        contact.setStatus(1);
        contact.setIsDefault(1);
        contact.setPoiNote(merchant.getPoiNote());
        contact = contactRepository.insertSelective(contact);
        return contact.getContactId();
    }


    public Long addContactForMerchantBatchCreate(MerchantEntity merchant){
        if(!merchant.isInitContact()) {
            log.info("无需初始化地址");
            return null;
        }
        log.info("【批量创建门店】开始初始化门店的地址。merchant：{}", JSON.toJSONString(merchant));
        ContactEntity contact = new ContactEntity();
        contact.setContact(merchant.getMcontact());
        contact.setMId(merchant.getMId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        contact.setAddress(merchant.getAddress());
        contact.setIsDefault(0);

        //补充poi，并模拟自动审核 （这里应产品要求，获取不到poi时，不抛出异常，回滚事务。让业务走地址审核的逻辑补充poi）
        String poiNote = merchant.getPoiNote();
        if(StringUtils.isBlank(poiNote)) {
            contact.setStatus(3);
            merchant.setErrorMsg("门店注册成功，但未获取到poi，地址自动审核失败");
            log.error("未获取到poi!  entity:{}", JSON.toJSONString(merchant));
        } else {
            Integer storeNo = fenceFacade.queryStoreByAddress(contact.getCity(), contact.getArea(), poiNote);
            contact.setStoreNo(storeNo);
            contact.setPoiNote(poiNote);
            contact.setStatus(1);
        }

        contact = contactRepository.insertSelective(contact);
        return contact.getContactId();
    }

    /**
     * 补充城配仓、poi
     */
    private void warpContactEntity(MerchantEntity merchant, ContactEntity contact) {
        if(contact == null) {
            return;
        }
        String address = StringUtils.defaultString(contact.getProvince()) +
                        StringUtils.defaultString(contact.getCity()) +
                        StringUtils.defaultString(contact.getArea()) +
                        StringUtils.defaultString(contact.getAddress());
        String poiNote = PoiSearchUtil.getPoiByAddress(address);
        if(StringUtils.isBlank(poiNote)) {
            log.error("未获取到poi!  contact:{}", JSON.toJSONString(contact));
            merchant.setErrorMsg("门店注册成功，但未获取到poi，地址自动审核失败");
            return;
        }
        Integer storeNo = fenceFacade.queryStoreByAddress(contact.getCity(), contact.getArea(), poiNote);
        ContactEntity updateEntity = new ContactEntity();
        updateEntity.setContactId(contact.getContactId());
        updateEntity.setStoreNo(storeNo);
        updateEntity.setPoiNote(poiNote);
        updateEntity.setStatus(1);
        contactRepository.updateByPrimaryKeySelective(updateEntity);
    }


    /**
     * 添加免审地址
     * @param merchant
     * @param storeNo
     * @return
     */
    public ContactEntity addContactWithoutAudit(MerchantEntity merchant, Integer storeNo){
        ContactEntity contact = new ContactEntity();
        // 默认杭州总部
        String defaultPoi = "120.058591,30.279943";
        contact.setContact(merchant.getMcontact());
        contact.setMId(merchant.getMId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        contact.setAddress(merchant.getAddress());
        contact.setStatus(1);
        contact.setIsDefault(0);
        contact.setPoiNote(StringUtils.isBlank(merchant.getPoiNote()) ? defaultPoi : merchant.getPoiNote());
        contact.setStoreNo(storeNo);
        contact.setRemark("人工工单操作");
        contact = contactRepository.insertSelective(contact);
        log.info("免审地址添加完成:contact：{}", JSON.toJSONString(contact));
        return contact;
    }


}
