package net.summerfarm.manage.domain.activity.service;


import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.manage.common.enums.activity.ActivityTypeEnum;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuPriceCommandParam;
import net.summerfarm.manage.domain.activity.param.query.ActivitySkuPriceQueryParam;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuDetailCommandParam;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceCommandRepository;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceQueryRepository;
import net.summerfarm.manage.domain.activity.valueObject.ActivityItemScopeValueObject;
import net.summerfarm.manage.domain.customization.entity.CustomizationRequestSkuMappingEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 *
 * @Title: 活动sku配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-09 14:55:30
 * @version 1.0
 *
 */
@Service
public class ActivitySkuDetailCommandDomainService {


    @Autowired
    private ActivitySkuDetailCommandRepository activitySkuDetailCommandRepository;
    @Autowired
    private ActivitySkuDetailQueryRepository activitySkuDetailQueryRepository;
    @Autowired
    private ActivityBasicInfoQueryDomainService basicInfoQueryDomainService;
    @Autowired
    private ActivitySkuPriceCommandRepository activitySkuPriceCommandRepository;
    @Autowired
    private ActivitySkuPriceQueryRepository activitySkuPriceQueryRepository;

    public ActivitySkuDetailEntity insert(ActivitySkuDetailCommandParam param) {
        return activitySkuDetailCommandRepository.insertSelective(param);
    }


    public int update(ActivitySkuDetailCommandParam param) {
        return activitySkuDetailCommandRepository.updateSelectiveById(param);
    }


    /**
     * 根据sku 跟 活动类型 查询对应的sku配置 并复制
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void selectAndCopyByAreaAndSku(Map<String,String> map, Integer areaNo) {
        List<ActivityItemScopeValueObject> activityItemScopeValueObjects = basicInfoQueryDomainService.selectActivityByAreaNoAndType (areaNo, ActivityTypeEnum.SPECIAL_PRICE.getCode ());
        List<Long> itemConfigIds = activityItemScopeValueObjects.stream ().map (ActivityItemScopeValueObject::getItemConfigId).collect (Collectors.toList ());

        if(CollectionUtil.isEmpty (itemConfigIds)){
            return;
        }

        List<ActivitySkuDetailEntity> activitySkuDetailEntities = activitySkuDetailQueryRepository.listByItemConfigsAndSkus (itemConfigIds, map.keySet ());
        if(CollectionUtil.isEmpty (activitySkuDetailEntities)){
            return;
        }
        ActivitySkuPriceQueryParam queryParam = new ActivitySkuPriceQueryParam ();
        queryParam.setSkus (map.keySet ());
        queryParam.setAreaNo (areaNo);
        queryParam.setSkuDetailIds (activitySkuDetailEntities.stream ().map (ActivitySkuDetailEntity::getId).collect (Collectors.toList ()));
        List<ActivitySkuPriceEntity> skuPrice = activitySkuPriceQueryRepository.selectByCondition (queryParam);
        if(CollectionUtil.isEmpty (skuPrice)){
            return;
        }

        Map<Long, List<ActivitySkuPriceEntity>> priceMap = skuPrice.stream().collect(Collectors.groupingBy(ActivitySkuPriceEntity::getSkuDetailId));

        Map<String, List<ActivitySkuDetailEntity>> resultMap = activitySkuDetailEntities.stream().collect(Collectors.groupingBy(ActivitySkuDetailEntity::getSku));

        resultMap.forEach ((srcsku, activitySkuDetails) ->{
            String newSku = map.get (srcsku);
            if (newSku != null) {
                for (ActivitySkuDetailEntity activitySkuDetail : activitySkuDetails) {
                    List<ActivitySkuPriceEntity> activitySkuPriceEntities = priceMap.get (activitySkuDetail.getId ());
                    if (!CollectionUtil.isEmpty (activitySkuPriceEntities)) {
                        activitySkuPriceEntities = activitySkuPriceEntities.stream ().filter (activitySkuPriceEntity -> activitySkuPriceEntity.getSku ().equals (srcsku) && activitySkuPriceEntity.getAreaNo ().equals (areaNo)).collect(Collectors.toList());
                        if (!CollectionUtil.isEmpty (activitySkuPriceEntities)) {
                            ActivitySkuDetailCommandParam param = saveActivitySkuDetailCommandParam (activitySkuDetail, newSku);
                            for(ActivitySkuPriceEntity activitySkuPriceEntity : activitySkuPriceEntities){
                                activitySkuPriceCommandRepository.insertSelective (saveActivitySkuPriceCommandParam (activitySkuPriceEntity, newSku,param));
                            }
                        }
                    }
                }
            }
        });
    }

    private ActivitySkuPriceCommandParam saveActivitySkuPriceCommandParam(ActivitySkuPriceEntity activitySkuPriceEntity, String newSku, ActivitySkuDetailCommandParam param) {
        ActivitySkuPriceCommandParam activitySkuPriceCommandParam = new ActivitySkuPriceCommandParam ();
        activitySkuPriceCommandParam.setSkuDetailId(param.getId ());
        activitySkuPriceCommandParam.setSku(newSku);
        activitySkuPriceCommandParam.setAreaNo(activitySkuPriceEntity.getAreaNo ());
        activitySkuPriceCommandParam.setSalePrice(activitySkuPriceEntity.getSalePrice ());
        activitySkuPriceCommandParam.setLadderPrice(activitySkuPriceEntity.getLadderPrice ());
        activitySkuPriceCommandParam.setActivityPrice(activitySkuPriceEntity.getActivityPrice ());
        activitySkuPriceCommandParam.setUpdaterId(activitySkuPriceEntity.getUpdaterId ());
        activitySkuPriceCommandParam.setBasicInfoId(activitySkuPriceEntity.getBasicInfoId ());
        return activitySkuPriceCommandParam;
    }

    private ActivitySkuDetailCommandParam saveActivitySkuDetailCommandParam(ActivitySkuDetailEntity activitySkuDetail, String newSku) {
        ActivitySkuDetailCommandParam param = new ActivitySkuDetailCommandParam ();
        // 复制所有字段，但不复制id（让数据库自动生成新的ID）
        param.setItemConfigId(activitySkuDetail.getItemConfigId());
        param.setSku(newSku); // 使用新的SKU
        param.setRoundingMode(activitySkuDetail.getRoundingMode());
        param.setAdjustType(activitySkuDetail.getAdjustType());
        param.setAmount(activitySkuDetail.getAmount());
        param.setSort(activitySkuDetail.getSort());
        param.setPlanQuantity(activitySkuDetail.getPlanQuantity());
        param.setActualQuantity(activitySkuDetail.getActualQuantity());
        param.setLockQuantity(activitySkuDetail.getLockQuantity());
        param.setAccountLimit(activitySkuDetail.getAccountLimit());
        param.setLimitQuantity(activitySkuDetail.getLimitQuantity());
        param.setMinSaleNum(activitySkuDetail.getMinSaleNum());
        param.setSingleDeposit(activitySkuDetail.getSingleDeposit());
        param.setExpansionRatio(activitySkuDetail.getExpansionRatio());
        param.setHidePrice(activitySkuDetail.getHidePrice());
        param.setTimingConfig(activitySkuDetail.getTimingConfig());
        param.setDelFlag(0); // 新记录设置为未删除
        param.setIsSupportTiming(activitySkuDetail.getIsSupportTiming());
        param.setAutoPrice(activitySkuDetail.getAutoPrice());
        param.setLadderConfig(activitySkuDetail.getLadderConfig());
        param.setDiscountLabel(activitySkuDetail.getDiscountLabel());
        ActivitySkuDetailEntity activitySkuDetailEntity = activitySkuDetailCommandRepository.insertSelective (param);
        param.setId (activitySkuDetailEntity.getId ());
        return param;
    }


    public int delete(Long id) {
        return activitySkuDetailCommandRepository.remove(id);
    }
}
