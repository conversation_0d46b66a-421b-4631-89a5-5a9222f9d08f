package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * cycle_inventory_cost
 * <AUTHOR>
@Data
public class CycleInventoryCostEntity implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku

     */
    private String sku;

    /**
     * c1周期成本
     */
    private BigDecimal firstCycleCost;

    private LocalDateTime firstCycleCostTime;

    /**
     * C2周期成本
     */
    private BigDecimal endCycleCost;

    private LocalDateTime endCycleCostTime;

    /**
     * 创建人
     */
    private Integer creater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 当前日周期成本更新时间
     */
    private LocalDateTime currentCostUpdateTime;

    private static final long serialVersionUID = 1L;
}