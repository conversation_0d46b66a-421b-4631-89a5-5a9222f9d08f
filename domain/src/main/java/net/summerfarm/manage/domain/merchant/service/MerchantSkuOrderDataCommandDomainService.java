package net.summerfarm.manage.domain.merchant.service;


import net.summerfarm.manage.domain.merchant.repository.MerchantSkuOrderDataQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantSkuOrderDataCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantSkuOrderDataEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantSkuOrderDataCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 采购助手-门店常购清单订单行为数据领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Service
public class MerchantSkuOrderDataCommandDomainService {


    @Autowired
    private MerchantSkuOrderDataCommandRepository merchantSkuOrderDataCommandRepository;
    @Autowired
    private MerchantSkuOrderDataQueryRepository merchantSkuOrderDataQueryRepository;



    public MerchantSkuOrderDataEntity insert(MerchantSkuOrderDataCommandParam param) {
        return merchantSkuOrderDataCommandRepository.insertSelective(param);
    }


    public int update(MerchantSkuOrderDataCommandParam param) {
        return merchantSkuOrderDataCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return merchantSkuOrderDataCommandRepository.remove(id);
    }
}
