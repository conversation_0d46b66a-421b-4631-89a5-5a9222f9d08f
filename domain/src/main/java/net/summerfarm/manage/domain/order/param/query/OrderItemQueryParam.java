package net.summerfarm.manage.domain.order.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-01-18 15:49:07
 * @version 1.0
 *
 */
@Data
public class OrderItemQueryParam extends BasePageInput {
	/**
	 * 
	 */
	private Long id;

	/**
	 * 商品名称（快照）
	 */
	private String pdName;

	/**
	 * 产品编号
	 */
	private String sku;

	/**
	 * 重量/规格
	 */
	private String weight;

	/**
	 * 生熟度
	 */
	private String maturity;

	/**
	 * 订单编号
	 */
	private String orderNo;

	/**
	 * 
	 */
	private Integer categoryId;

	/**
	 * 购买数量
	 */
	private Integer amount;

	/**
	 * 购买时的商品价格
	 */
	private BigDecimal price;

	/**
	 * 
	 */
	private BigDecimal originalPrice;

	/**
	 * 商品图片
	 */
	private String picturePath;

	/**
	 * 购买时间
	 */
	private LocalDateTime addTime;

	/**
	 * 仓储区域
	 */
	private Integer storageLocation;

	/**
	 * 
	 */
	private Integer suitId;

	/**
	 * 
	 */
	private String suitName;

	/**
	 * 
	 */
	private Integer suitAmount;

	/**
	 * 订单项状态
	 */
	private Integer status;

	/**
	 * 
	 */
	private Integer rebateType;

	/**
	 * 
	 */
	private Double rebateNumber;

	/**
	 * 
	 */
	private Double mPrice;

	/**
	 * 体积
	 */
	private String volume;

	/**
	 * 重量kg
	 */
	private BigDecimal weightNum;

	/**
	 * 是否用券
	 */
	private Integer useCoupon;

	/**
	 * 大单值
	 */
	private Integer maxThreshold;

	/**
	 * 直发采购下单时的sku的总价
	 */
	private BigDecimal totalPrice;

	/**
	 * 商品类型：0、普通商品 1、赠品
	 */
	private Integer productType;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 订单项实付总价
	 */
	private BigDecimal actualTotalPrice;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * sku名称
	 */
	private String skuName;

	/**
	 * sku下单时有效期
	 */
	private String info;

	/**
	 * 多个订单号集合
	 */
	private List<String> orderNos;

	
}