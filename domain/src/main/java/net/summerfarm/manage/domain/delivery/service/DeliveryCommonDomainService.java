package net.summerfarm.manage.domain.delivery.service;

import net.summerfarm.manage.domain.delivery.param.command.DeliveryPlanCommandParam;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanCommandRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName OrderCommonDomainService
 * @Description 订单领域
 * <AUTHOR>
 * @Date 15:41 2024/1/18
 * @Version 1.0
 **/
@Service
public class DeliveryCommonDomainService {

    @Resource
    private DeliveryPlanCommandRepository deliveryPlanCommandRepository;

    /***
     * @author: lzh
     * @description: 更加配送ID更新配送信息
     * @date: 2024/1/18 17:50
     * @param: [id]
     * @return: java.lang.Boolean
     **/
    public Boolean updateInfoById(DeliveryPlanCommandParam commandParam) {
        return deliveryPlanCommandRepository.updateInfoById(commandParam);
    }
}
