package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Data
public class AreaSkuEntity implements Serializable {

    /**
     * 自增id
     */
    private Integer id;
    /**
     * inverntory.Id
     */
    private Long skuId;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 可售库存
     */
    private Integer quantity;

    /**
     * 锁定库存
     */
    private Integer lockQuantity;

    /**
     * 安全库存
     */
    private Integer safeQuantity;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 销售价
     */
    private BigDecimal price;

    /**
     * 阶梯价
     */
    private String ladderPrice;

    /**
     * 1使用虚拟库存0不
     */
    private Boolean share;

    /**
     * 0下架 1上架
     */
    private Boolean onSale;

    /**
     * 越小排序越靠前
     */
    private Integer priority;

    /**
     * 排序
     */
    private Integer pdPriority;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 添加时间
     */
    private Date addTime;

    private Integer level;

    /**
     * 限购数量
     */
    private Integer limitedQuantity;

    /**
     * 销售模式；0普通，1团购，2日限购，3永久限购，4新手限购，5订单限购
     */
    private Integer salesMode;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * storeQuantity
     */
    private Integer storeQuantity;

    private Integer shareQuantity;

    /**
     * 是否展示在首页
     */
    private Boolean show;

    /**
     * 介绍
     */
    private String info;

    /**
     * 是否是大客户0 不是 1是
     */
    private Integer mType;

    /**
     * 是否展示预告信息
     */
    private Boolean showAdvance;

    /**
     * 预告信息
     */
    private String advance;

    private BigDecimal oldPrice;

    /**
     * 角标状态 0、关闭 1、开启
     */
    private Integer cornerStatus;

    /**
     * 上架操作: 0上架、1有库存时上架、2定时上架
     */
    private Integer openSale;

    /**
     * 定时上架时间
     */
    private LocalDateTime openSaleTime;

    /**
     * 下架操作: 0下架、1售罄下架、2定时下架
     */
    private Integer closeSale;

    /**
     * 定时下架时间
     */
    private LocalDateTime closeSaleTime;

    /**
     * 排序固定标识 0、不固定 1、固定
     */
    private Integer fixFlag;

    /**
     * 固定排序值
     */
    private Integer fixNum;

    /**
     * 毛利率
     */
    private BigDecimal interestRateNew;

    private BigDecimal interestRateOld;

    /**
     * 是否自动调价
     */
    private Integer autoFlagNew;

    private Integer autoFlagOld;

    /**
     * 更新人
     */
    private String updater;

    private Integer parentNo;

    /**
     * 库存仓-上架时指定
     */
    private Integer warehouseNo;

}
