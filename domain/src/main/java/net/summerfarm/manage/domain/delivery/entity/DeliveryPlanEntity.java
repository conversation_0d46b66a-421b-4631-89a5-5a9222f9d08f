package net.summerfarm.manage.domain.delivery.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024-01-18 14:33:12
 * @version 1.0
 *
 */
@Data
public class DeliveryPlanEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 配送状态
	 */
	private Integer status;

	/**
	 * 配送时间
	 */
	private Date deliveryTime;

	
	/**
	 * 配送数量
	 */
	private Integer quantity;

	/**
	 * 随单配送时对应的普通订单订单号
	 */
	private String masterOrderNo;

	/**
	 * 
	 */
	private LocalDateTime updateTime;

	/**
	 * 联系人
	 */
	private Integer contactId;

	/**
	 * 配送方式
	 */
	private Integer deliverytype;

	/**
	 * 配送时间区间
	 */
	private String timeFrame;

	/**
	 * 子帐号id
	 */
	private Long accountId;

	/**
	 * 管理员id
	 */
	private Integer adminId;

	/**
	 * 下单仓编号
	 */
	private Integer orderStoreNo;

	/**
	 * 省心送推迟订单时间
	 */
	private Date putOffTime;

	
	/**
	 * 
	 */
	private LocalDateTime addTime;

	/**
	 * 旧配送时间
	 */
	private Date ordDeliveryTime;

	
	/**
	 * 旧配送时间
	 */
	private Date oldDeliveryTime;

	
	/**
	 * 拦截状态 0 正常 1被拦截
	 */
	private Integer interceptFlag;

	/**
	 * 拦截时间
	 */
	private LocalDateTime interceptTime;

	/**
	 * 完成排线-展示标识 0 展示 1不展示
	 */
	private Integer showFlag;

	/**
	 * 0:未评价，1:已评价
	 */
	private Integer deliveryEvaluationStatus;



}