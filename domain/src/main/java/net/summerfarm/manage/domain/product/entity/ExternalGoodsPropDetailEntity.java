package net.summerfarm.manage.domain.product.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2024/12/18 11:24
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalGoodsPropDetailEntity {

    /**
     * 级别
     */
    private String level;

    /**
     * 产地
     */
    private String origin;

    /**
     * 果规-单果大小
     */
    private String fruitSize;


}
