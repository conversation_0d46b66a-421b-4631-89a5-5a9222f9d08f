package net.summerfarm.manage.domain.trade.repository;



import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import net.summerfarm.manage.domain.trade.param.command.TimingOrderRefundTimeCommandParam;




/**
*
* <AUTHOR>
* @date 2024-01-22 14:45:54
* @version 1.0
*
*/
public interface TimingOrderRefundTimeCommandRepository {

    TimingOrderRefundTimeEntity insertSelective(TimingOrderRefundTimeCommandParam param);

    int updateSelectiveById(TimingOrderRefundTimeCommandParam param);

    int remove(Long id);

    /***
     * @author: lzh
     * @description: 根据订单号删除省心送退款记录
     * @date: 2024/1/22 14:58
     * @param: [orderNo]
     * @return: java.lang.Boolean
     **/
    Boolean deleteTimeOrderRefund(String orderNo);
}