package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-07 14:12:46
 * @version 1.0
 *
 */
@Data
public class ProductLabelValueQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 标签字段ID
	 */
	private String labelId;

	/**
	 * 标签值：0 关闭 ， 1 开启
	 */
	private Integer labelValue;

	/**
	 * 创建时间
	 */
	private LocalDateTime creatTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	

	
}