package net.summerfarm.manage.domain.invoice.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * invoice_config
 * <AUTHOR>
@ApiModel(description = "财务税票抬头配置实体类")
@Data
public class InvoiceConfig implements Serializable {
    private static final long serialVersionUID = 2046898834683652781L;

    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 取自merchant表中m_id，表示单店自由的抬头配置
     */
    @ApiModelProperty(value = "取自merchant表中m_id，表示单店自由的抬头配置")
    private Long merchantId;

    /**
     * 取自admin表中为大客户的id, 表示所属的大客户
     */
    @ApiModelProperty(value = "取自admin表中为大客户的id, 表示所属的大客户")
    private Integer adminId;

    /**
     * 0:门店自有抬头；1:大客户门店下的抬头
     */
    @ApiModelProperty(value = "0:门店自有抬头；1:大客户门店下的抬头")
    private Integer type;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /**
     * 开户账号
     */
    @ApiModelProperty(value = "开户账号")
    private String openAccount;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String openBank;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司电话
     */
    @ApiModelProperty(value = "公司电话")
    private String companyPhone;

    /**
     * 邮寄地址
     */
    @ApiModelProperty(value = "邮寄地址")
    private String mailAddress;

    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人")
    private String companyReceiver;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String companyEmail;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String linkMethod;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "营业执照地址")
    private String businessLicenseAddress;

    @ApiModelProperty(value = "修改人")
    private String updater;

    /**
     * 0:生效中（默认), 1:(失效)
     */
    @ApiModelProperty(value = "0:生效中（默认), 1:(失效)")
    private Integer validStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建抬头提交时间
     */
    @ApiModelProperty(value = "创建抬头提交时间")
    private LocalDateTime createTime;

    /**
     * 默认抬头标志
     */
    private Integer defaultFlag;
}