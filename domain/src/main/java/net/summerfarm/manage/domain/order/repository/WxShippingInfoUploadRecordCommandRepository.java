package net.summerfarm.manage.domain.order.repository;



import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import net.summerfarm.manage.domain.order.param.command.WxShippingInfoUploadRecordCommandParam;




/**
*
* <AUTHOR>
* @date 2024-10-15 17:49:41
* @version 1.0
*
*/
public interface WxShippingInfoUploadRecordCommandRepository {

    WxShippingInfoUploadRecordEntity insertSelective(WxShippingInfoUploadRecordCommandParam param);

    int updateSelectiveById(WxShippingInfoUploadRecordCommandParam param);

    int remove(Long id);

}