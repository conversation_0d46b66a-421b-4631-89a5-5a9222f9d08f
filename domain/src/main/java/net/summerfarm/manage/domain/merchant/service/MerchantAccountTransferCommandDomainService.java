package net.summerfarm.manage.domain.merchant.service;


import net.summerfarm.manage.domain.merchant.repository.MerchantAccountTransferQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantAccountTransferCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantAccountTransferCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 门店迁移领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
@Service
public class MerchantAccountTransferCommandDomainService {


    @Autowired
    private MerchantAccountTransferCommandRepository merchantAccountTransferCommandRepository;


    public MerchantAccountTransferEntity insert(MerchantAccountTransferCommandParam param) {
        return merchantAccountTransferCommandRepository.insertSelective(param);
    }

}
