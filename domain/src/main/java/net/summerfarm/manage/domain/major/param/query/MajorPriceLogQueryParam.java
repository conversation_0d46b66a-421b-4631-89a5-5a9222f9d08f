package net.summerfarm.manage.domain.major.param.query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MajorPriceLogQueryParam extends BasePageInput {

	/**
	 * sku
	 */
	private String sku;

	/**
	 * areano
	 */
	private Integer areaNo;

	/**
	 * 1 账期 2  现结
	 */
	private Integer direct;

	/**
	 * 大客户id
	 */
	private Long adminId;


}