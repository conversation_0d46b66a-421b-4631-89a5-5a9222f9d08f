package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-06 13:48:40
 * @version 1.0
 *
 */
@Data
public class InventoryBindQueryParam extends BasePageInput {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * spuid(products.pd_id)
	 */
	private Long pdId;

	/**
	 * 商品编号
	 */
	private String sku;

	/**
	 * 绑定sku编号
	 */
	private String bindSku;

	/**
	 * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
	 */
	private Integer extType;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;
}