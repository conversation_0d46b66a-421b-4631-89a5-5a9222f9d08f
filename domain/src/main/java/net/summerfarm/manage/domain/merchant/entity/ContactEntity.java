package net.summerfarm.manage.domain.merchant.entity;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-09-19 16:21:24
 * @version 1.0
 *
 */
@Data
public class ContactEntity {
	/**
	 * 
	 */
	private Long contactId;

	/**
	 *  商户id
	 */
	private Long mId;

	/**
	 * 联系人
	 */
	private String contact;

	/**
	 * 职位
	 */
	private String position;

	/**
	 * 性别
	 */
	private Integer gender;

	/**
	 * 
	 */
	private String phone;

	/**
	 * 
	 */
	private String email;

	/**
	 * 微信号
	 */
	private String weixincode;

	/**
	 * 
	 */
	private String province;

	/**
	 * 
	 */
	private String city;

	/**
	 * 
	 */
	private String area;

	/**
	 * 
	 */
	private String address;

	/**
	 * 配送车
	 */
	private String deliveryCar;

	/**
	 * 状态(1正常或审核通过、2删除、3待审核、4审核不通过)
	 */
	private Integer status;

	/**
	 * 
	 */
	private String remark;

	/**
	 * 1默认地址 与merchat中一致
	 */
	private Integer isDefault;

	/**
	 * 高德地图poi坐标
	 */
	private String poiNote;

	/**
	 * 与仓库的距离
	 */
	private BigDecimal distance;

	/**
	 * 路线
	 */
	private String path;

	/**
	 * 门牌号
	 */
	private String houseNumber;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 配送仓编号
	 */
	private Integer storeNo;

	/**
	 * 归属区域id adCodeMsg表
	 */
	private Integer acmId;

	/**
	 * 配送仓编号 备注
	 */
	private Integer backStoreNo;

	/**
	 * 配送周期
	 */
	private String deliveryFrequent;

	/**
	 * 运费规则
	 */
	private String deliveryRule;

	/**
	 * 运费
	 */
	private BigDecimal deliveryFee;

	/**
	 * 地址备注
	 */
	private String addressRemark;

	

	
}