package net.summerfarm.manage.domain.product.param.query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-04-08 15:19:21
 * @version 1.0
 *
 */
@Data
public class MajorPriceQueryParam extends BasePageInput {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private String sku;

	/**
	 * 
	 */
	private String pdName;

	/**
	 * 
	 */
	private String weight;

	/**
	 * 
	 */
	private Integer areaNo;

	/**
	 * 
	 */
	private Integer adminId;

	/**
	 * 
	 */
	private BigDecimal price;

	/**
	 * 
	 */
	private String areaName;

	/**
	 * 
	 */
	private Integer direct;

	/**
	 * 当为代仓商品时的支付方式：0：无需支付 1：下单时支付
	 */
	private Integer payMethod;

	/**
	 * 报价单生效时间
	 */
	private LocalDateTime validTime;

	/**
	 * 报价单失效时间
	 */
	private LocalDateTime invalidTime;

	/**
	 * 用户是否展示
	 */
	private Integer mallShow;

	/**
	 * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）
	 */
	private Integer priceType;

	/**
	 * 毛利率的成本价
	 */
	private BigDecimal cost;

	/**
	 * 毛利率
	 */
	private BigDecimal interestRate;

	/**
	 * 毛利率的固定价
	 */
	private BigDecimal fixedPrice;

	/**
	 * 原售价
	 */
	private BigDecimal originalPrice;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 通过低价监控更新价格的时间
	 */
	private LocalDateTime lowPriceUpdateTime;
	

	
}