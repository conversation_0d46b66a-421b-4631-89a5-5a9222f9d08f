package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity;
import net.summerfarm.manage.domain.product.param.command.GoodsLocationDetailCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-07 14:12:49
* @version 1.0
*
*/
public interface GoodsLocationDetailCommandRepository {

    GoodsLocationDetailEntity insertSelective(GoodsLocationDetailCommandParam param);

    int updateSelectiveById(GoodsLocationDetailCommandParam param);

    int remove(Long id);

}