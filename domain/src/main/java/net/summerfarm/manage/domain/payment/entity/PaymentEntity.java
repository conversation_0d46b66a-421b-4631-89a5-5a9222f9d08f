package net.summerfarm.manage.domain.payment.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-12-23 17:01:31
 * @version 1.0
 *
 */
@Data
public class PaymentEntity {
	/**
	 * 
	 */
	private Long paymentId;

	/**
	 * 支付类型
	 */
	private String payType;

	/**
	 * 订单ID
	 */
	private String orderNo;

	/**
	 * 交易号
	 */
	private String transactionNumber;

	/**
	 * 支付金额
	 */
	private BigDecimal money;

	/**
	 * 支付完成时间
	 */
	private LocalDateTime endTime;

	/**
	 * 支付名称
	 */
	private String tradeType;

	/**
	 * 支付方式
	 */
	private String bankType;

	/**
	 * 支付状态
	 */
	private Integer status;

	/**
	 * 支付错误代码
	 */
	private String errCode;

	/**
	 * 支付错误描述
	 */
	private String errCodeDes;

	/**
	 * 支付账户id（废弃）
	 */
	private Integer companyAccountId;

	/**
	 * Boc支付二维码
	 */
	private String scanCode;

	/**
	 * 支付账号信息
	 */
	private String accountInfo;

	/**
	 * 
	 */
	private String bocPayType;

	/**
	 * 线上支付完成时间
	 */
	private LocalDateTime onlinePayEndTime;

	/**
	 * 支付渠道id
	 */
	private Long paymentChannelId;

	

	
}