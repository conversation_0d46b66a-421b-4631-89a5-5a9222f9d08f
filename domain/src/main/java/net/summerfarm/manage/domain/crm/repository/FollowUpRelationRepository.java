package net.summerfarm.manage.domain.crm.repository;

import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-20 15:02:32
* @version 1.0
*
*/
public interface FollowUpRelationRepository {

    FollowUpRelationEntity selectByPrimaryKey(Long id);

    List<FollowUpRelationEntity> selectByCondition(FollowUpRelationEntity entity);

    FollowUpRelationEntity selectOne(FollowUpRelationEntity record);

    FollowUpRelationEntity insertSelective(FollowUpRelationEntity entity);

    int updateById(FollowUpRelationEntity entity);

    int remove(Long id);


    List<FollowUpRelationEntity> batchQueryByMids(List<Long> mIds);
}