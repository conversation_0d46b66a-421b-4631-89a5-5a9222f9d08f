package net.summerfarm.manage.domain.job.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-12-18 15:57:19
 * @version 1.0
 *
 */
@Data
public class CrmJobRewardRecordCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * 任务Id FK crm_job
	 */
	private Long jobId;

	/**
	 * 奖励类型：0-红包，1-优惠券
	 */
	private Integer rewardType;

	/**
	 * 卡券id
	 */
	private String rewardValue;

	/**
	 * 达成奖励发放的订单号列表
	 */
	private String orderNo;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	

	
}