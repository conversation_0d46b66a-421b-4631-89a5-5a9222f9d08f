package net.summerfarm.manage.domain.trade.service;


import net.summerfarm.manage.domain.trade.repository.TimingOrderRefundTimeQueryRepository;
import net.summerfarm.manage.domain.trade.repository.TimingOrderRefundTimeCommandRepository;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import net.summerfarm.manage.domain.trade.param.command.TimingOrderRefundTimeCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 省心送订单退款时间表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-22 14:45:54
 * @version 1.0
 *
 */
@Service
public class TimingOrderRefundTimeCommandDomainService {


    @Autowired
    private TimingOrderRefundTimeCommandRepository timingOrderRefundTimeCommandRepository;
    @Autowired
    private TimingOrderRefundTimeQueryRepository timingOrderRefundTimeQueryRepository;



    public TimingOrderRefundTimeEntity insert(TimingOrderRefundTimeCommandParam param) {
        return timingOrderRefundTimeCommandRepository.insertSelective(param);
    }


    public int update(TimingOrderRefundTimeCommandParam param) {
        return timingOrderRefundTimeCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return timingOrderRefundTimeCommandRepository.remove(id);
    }

    public Boolean deleteTimeOrderRefund(String orderNo) {
        return timingOrderRefundTimeCommandRepository.deleteTimeOrderRefund(orderNo);
    }
}
