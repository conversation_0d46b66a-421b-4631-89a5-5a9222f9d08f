package net.summerfarm.manage.domain.product.param.command;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-06 16:02:28
 * @version 1.0
 *
 */
@Data
public class ProductsPropertyValueCommandParam {
	/**
	 * 主键、自增
	 */
	private Integer id;

	/**
	 * pd_id
	 */
	private Long pdId;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 属性id
	 */
	private Integer productsPropertyId;

	/**
	 * 属性值
	 */
	private String productsPropertyValue;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	

	
}