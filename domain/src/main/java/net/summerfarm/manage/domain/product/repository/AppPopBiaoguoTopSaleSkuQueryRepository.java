package net.summerfarm.manage.domain.product.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;

import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoCategoryEntity;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoTopSaleSkuQueryParam;



/**
*
* <AUTHOR>
* @date 2024-11-18 15:55:40
* @version 1.0
*
*/
public interface AppPopBiaoguoTopSaleSkuQueryRepository {

    PageInfo<AppPopBiaoguoTopSaleSkuEntity> getPage(AppPopBiaoguoTopSaleSkuQueryParam param);

    AppPopBiaoguoTopSaleSkuEntity selectById(Long id);

    List<AppPopBiaoguoTopSaleSkuEntity> selectByCondition(AppPopBiaoguoTopSaleSkuQueryParam param);

    List<AppPopBiaoguoCategoryEntity> selectCategory2List(AppPopBiaoguoTopSaleSkuQueryParam queryParam);

}