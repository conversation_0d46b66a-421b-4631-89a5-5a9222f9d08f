package net.summerfarm.manage.domain.order.param.command;

import java.time.LocalDateTime;
import java.math.BigDecimal;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-01-23 16:02:29
 * @version 1.0
 *
 */
@Data
public class OrderRelationCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 主订单号
	 */
	private String masterOrderNo;

	/**
	 * 子订单号
	 */
	private String orderNo;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 精准送费用
	 */
	private BigDecimal precisionDeliveryFee;

	

	
}