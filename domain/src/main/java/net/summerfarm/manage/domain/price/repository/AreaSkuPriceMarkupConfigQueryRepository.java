package net.summerfarm.manage.domain.price.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;



/**
*
* <AUTHOR>
* @date 2025-03-26 13:59:07
* @version 1.0
*
*/
public interface AreaSkuPriceMarkupConfigQueryRepository {

    PageInfo<AreaSkuPriceMarkupConfigEntity> getPage(AreaSkuPriceMarkupConfigQueryParam param);

    AreaSkuPriceMarkupConfigEntity selectById(Long id);

    List<AreaSkuPriceMarkupConfigEntity> selectByCondition(AreaSkuPriceMarkupConfigQueryParam param);

}