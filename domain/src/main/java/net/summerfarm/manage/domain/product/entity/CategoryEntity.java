package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

/**
 * @ClassName CategoryEntity
 * @Description
 * <AUTHOR>
 * @Date 18:39 2024/5/6
 * @Version 1.0
 **/
@Data
public class CategoryEntity {
    /**
     * 类目id
     */
    private Integer id;

    /**
     * 父类id
     */
    private Integer parentId;

    /**
     * 类目名称
     */
    private String category;

    /**
     * 标记位-过时的品类  1代表过时，商品被删除
     */
    private Integer outdated;

    /**
     * 图标
     */
    private String icon;

    /**
     * 1 全部,2乳制品,3非乳制品,4水果
     */
    private Integer type;
}
