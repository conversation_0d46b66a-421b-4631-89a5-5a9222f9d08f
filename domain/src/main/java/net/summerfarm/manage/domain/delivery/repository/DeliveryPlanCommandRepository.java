package net.summerfarm.manage.domain.delivery.repository;

import net.summerfarm.manage.domain.delivery.param.command.DeliveryPlanCommandParam;

/**
 * @ClassName DeliveryPlanCommandRepository
 * @Description TODO
 * <AUTHOR>
 * @Date 17:44 2024/1/18
 * @Version 1.0
 **/
public interface DeliveryPlanCommandRepository {

    /***
     * @author: lzh
     * @description:
     * @date: 2024/1/18 17:50
     * @param: [id]
     * @return: java.lang.Boolean
     **/
    Boolean updateInfoById(DeliveryPlanCommandParam commandParam);
}
