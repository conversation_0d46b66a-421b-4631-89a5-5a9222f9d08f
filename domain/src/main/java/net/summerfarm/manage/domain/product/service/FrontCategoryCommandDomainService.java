package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.FrontCategoryQueryRepository;
import net.summerfarm.manage.domain.product.repository.FrontCategoryCommandRepository;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.domain.product.param.command.FrontCategoryCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 前台类目领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 *
 */
@Service
public class FrontCategoryCommandDomainService {


    @Autowired
    private FrontCategoryCommandRepository frontCategoryCommandRepository;
    @Autowired
    private FrontCategoryQueryRepository frontCategoryQueryRepository;



    public FrontCategoryEntity insert(FrontCategoryCommandParam param) {
        return frontCategoryCommandRepository.insertSelective(param);
    }


    public int update(FrontCategoryCommandParam param) {
        return frontCategoryCommandRepository.updateSelectiveById(param);
    }


    public int delete(Integer id) {
        return frontCategoryCommandRepository.remove(id);
    }
}
