package net.summerfarm.manage.domain.coupon.repository;



import net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.MerchantCouponCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-31 15:37:46
* @version 1.0
*
*/
public interface MerchantCouponCommandRepository {

    MerchantCouponEntity insertSelective(MerchantCouponCommandParam param);

    int updateSelectiveById(MerchantCouponCommandParam param);

    int remove(Long id);

    int deleteMerchantCoupon(MerchantCouponCommandParam merchantCouponCommandParam);
}