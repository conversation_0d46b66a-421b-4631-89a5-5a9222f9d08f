package net.summerfarm.manage.domain.activity.repository;



import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.command.MarketRuleHistoryCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-31 17:39:38
* @version 1.0
*
*/
public interface MarketRuleHistoryCommandRepository {

    MarketRuleHistoryEntity insertSelective(MarketRuleHistoryCommandParam param);

    int updateSelectiveById(MarketRuleHistoryCommandParam param);

    int remove(Long id);

}