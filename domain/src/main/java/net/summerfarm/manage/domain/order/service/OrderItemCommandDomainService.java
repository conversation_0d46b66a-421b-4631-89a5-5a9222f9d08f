package net.summerfarm.manage.domain.order.service;


import net.summerfarm.manage.domain.order.repository.OrderItemQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrderItemCommandRepository;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import net.summerfarm.manage.domain.order.param.command.OrderItemCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 购物车领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-18 15:49:07
 * @version 1.0
 *
 */
@Service
public class OrderItemCommandDomainService {


    @Autowired
    private OrderItemCommandRepository orderItemCommandRepository;
    @Autowired
    private OrderItemQueryRepository orderItemQueryRepository;



    public OrderItemEntity insert(OrderItemCommandParam param) {
        return orderItemCommandRepository.insertSelective(param);
    }


    public int update(OrderItemCommandParam param) {
        return orderItemCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return orderItemCommandRepository.remove(id);
    }
}
