package net.summerfarm.manage.domain.product.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description
 * @Date 2025/3/31 16:21
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductsPropertyMappingRelationInfo {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 映射类型：0、类目 1、spu
     */
    private Integer type;

    /**
     * 类目id/pd id
     */
    private Integer mappingId;

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
