package net.summerfarm.manage.domain.offline.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/10/20 15:00
 */
public interface CrmMerchantDayLabelRepository {

    /**
     * 根据标签查询商户信息
     * @param selectKeys 查询条件
     * @param dayTag 时间标记
     * @return 商户ids
     */
    List<Long> selectMidListByInput(@Param("selectKeys") MerchantQueryInput selectKeys, @Param("dayTag") Integer dayTag);

}
