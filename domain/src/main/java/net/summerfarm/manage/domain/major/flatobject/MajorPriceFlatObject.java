package net.summerfarm.manage.domain.major.flatobject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@Data
public class MajorPriceFlatObject {
	/**
	 * 运营大区
	 */
	private Integer largeAreaNo;

	/**
	 * 运营大区name
	 */
	private String largeAreaName;

	/**
	 * sku编号
	 */
	private String sku;

	/**
	 * 商品名称
	 */
	private String pdName;


	/**
	 * 规格
	 */
	private String specification;

	/**
	 * 规格单位
	 */
	private String specificationUnit;

	/**
	 * 城市报价
	 */
	private List<MajorPriceItemFlatObject> majorPrices;

}