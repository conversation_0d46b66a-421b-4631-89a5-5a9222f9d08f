package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;

import java.util.List;

/**
 * @ClassName ProductsPropertyValueQueryParam
 * @Description
 * <AUTHOR>
 * @Date 18:05 2024/4/30
 * @Version 1.0
 **/
@Data
public class ProductsPropertyValueQueryParam {

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

    /**
     * pd_id
     */
    private Long pdId;

    /**
     * sku
     */
    private List<String> skus;

    /**
     * 属性类型：0、关键属性 1、销售属性
     */
    private Integer type;

    /**
     * 属性名
     */
    private String name;
}
