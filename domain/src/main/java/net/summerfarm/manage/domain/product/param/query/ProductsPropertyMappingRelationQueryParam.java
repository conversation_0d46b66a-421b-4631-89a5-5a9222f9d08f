package net.summerfarm.manage.domain.product.param.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * @Date 2025/3/31 16:41
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductsPropertyMappingRelationQueryParam {

    /**
     * 自定义属性类型 0、关键属性
     */
    private Integer propertyType;

    /**
     * 映射类型：0、类目 1、spu
     */
    private Integer mappingType;

    /**
     * 类目id/pd id
     */
    private List<Integer> mappingIdList;

    /**
     * 属性id
     */
    private Integer productsPropertyId;
}
