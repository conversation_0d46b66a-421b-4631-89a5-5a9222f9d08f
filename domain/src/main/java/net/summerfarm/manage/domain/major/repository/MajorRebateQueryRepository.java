package net.summerfarm.manage.domain.major.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import net.summerfarm.manage.domain.major.param.query.MajorRebateQueryParam;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-02-27 15:22:25
 */
public interface MajorRebateQueryRepository {

    PageInfo<MajorRebateEntity> getPage(MajorRebateQueryParam param);

    MajorRebateEntity selectById(Long id);

    List<MajorRebateEntity> selectByCondition(MajorRebateQueryParam param);


    List<MajorRebateEntity> selectListByCate(Integer adminId, List<Integer> areaNos, Integer cate, String sku);

}