package net.summerfarm.manage.domain.product.repository;

import net.summerfarm.manage.domain.product.entity.CategoryAllPathEntity;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.manage.domain.product.entity.CategoryLevelEntity;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2024/1/30
 */
public interface CategoryQueryRepository {

    List<CategoryLevelEntity> listCategoryLevel(List<Long> categoryIds);

    CategoryEntity selectInfoByPdId(Long pdId);

    List<Integer> getSubLevelCategoryIds(List<Integer> categoryIds);

    /**
     * 查询类目全路径
     *
     * <AUTHOR>
     * @date 2024/10/18 11:21
     * @param categoryIdList 类目id列表
     */
    List<CategoryAllPathEntity> selectCategoryAllPath(List<Long> categoryIdList);


    List<Long> selectCategoryIdsByFrontId(Long frontCategoryId);

    CategoryEntity selectByCategoryId(Long categoryId);
}
