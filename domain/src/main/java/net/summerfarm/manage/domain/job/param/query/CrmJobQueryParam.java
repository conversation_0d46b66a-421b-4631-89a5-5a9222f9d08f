package net.summerfarm.manage.domain.job.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-12-18 15:57:19
 * @version 1.0
 *
 */
@Data
public class CrmJobQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 任务名称
	 */
	private String jobName;

	/**
	 * 任务描述
	 */
	private String description;

	/**
	 * 任务类型 0.发券 1.其它 2. 指定商品下单 3. 指定品类下单 4. 普通下单
	 */
	private Integer type;

	/**
	 * 任务开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 任务结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 任务命中的品列表
	 */
	private String productList;

	/**
	 * 创建人admin_id
	 */
	private Long creator;

	/**
	 * 修改人admin_id
	 */
	private Long updater;

	/**
	 * 状态 0.未开始 1.进行中 2. 已结束 3. 已取消
	 */
	private Integer status;

	/**
	 * 关联人群方式 0. 门店列表 1. 上传门店excel 2. 上传门店商品关联excel
	 */
	private Integer merchantSelectionType;

	/**
	 * 发券任务必须关联的优惠券id
	 */
	private Integer couponId;

	/**
	 * 任务命中的品类列表。是个二维数组
	 */
	private String categoryList;

	/**
	 * 任务领取方式：0-手动领取，1-自动领取
	 */
	private Integer claimingMethod;

	/**
	 * 任务备注（用于后台展示）
	 */
	private String remark;

	/**
	 * 业务类型：0-销售任务，1-门店任务
	 */
	private Integer businessType;

	/**
	 * 上传的excel地址
	 */
	private String excelUrl;

	/**
	 * 关联人群包id列表
	 */
	private String merchantSelectionList;

	/**
	 * 奖励类型：0-红包，1-优惠券
	 */
	private Integer rewardType;

	/**
	 * 任务具体的奖励：卡券id、积分等
	 */
	private String rewardValue;

	

	
}