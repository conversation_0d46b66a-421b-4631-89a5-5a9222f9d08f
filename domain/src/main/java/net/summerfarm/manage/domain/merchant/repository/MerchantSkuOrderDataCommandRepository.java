package net.summerfarm.manage.domain.merchant.repository;



import net.summerfarm.manage.domain.merchant.entity.MerchantSkuOrderDataEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantSkuOrderDataCommandParam;




/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
public interface MerchantSkuOrderDataCommandRepository {

    MerchantSkuOrderDataEntity insertSelective(MerchantSkuOrderDataCommandParam param);

    int updateSelectiveById(MerchantSkuOrderDataCommandParam param);

    int remove(Long id);

}