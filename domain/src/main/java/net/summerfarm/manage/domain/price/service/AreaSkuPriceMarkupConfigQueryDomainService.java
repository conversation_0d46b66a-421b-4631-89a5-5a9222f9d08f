package net.summerfarm.manage.domain.price.service;


import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigQueryRepository;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigCommandRepository;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: pop城市分销商商品加价配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
@Service
public class AreaSkuPriceMarkupConfigQueryDomainService {


}
