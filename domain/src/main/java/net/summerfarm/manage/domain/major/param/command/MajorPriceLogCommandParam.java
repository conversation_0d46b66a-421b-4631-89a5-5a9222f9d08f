package net.summerfarm.manage.domain.major.param.command;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-02-19 11:19:11
 * @version 1.0
 *
 */
@Data
public class MajorPriceLogCommandParam {
	/**
	 * id
	 */
	private Long id;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * pdname
	 */
	private String pdName;

	/**
	 * 规格
	 */
	private String weight;

	/**
	 * areano
	 */
	private Integer areaNo;

	/**
	 * 大客户id
	 */
	private Long adminId;

	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * 区域name
	 */
	private String areaName;

	/**
	 * 1 账期 2  现结
	 */
	private Integer direct;

	/**
	 * 运营大区
	 */
	private Integer largeAreaNo;

	/**
	 * 商城价浮动/加减 值
	 */
	private BigDecimal priceAdjustmentValue;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 成本价
	 */
	private BigDecimal costPrice;

	/**
	 * 商城价
	 */
	private BigDecimal mallPrice;

	/**
	 * 当为代仓商品时的支付方式：0：无需支付 1：下单时支付
	 */
	private Integer payMethod;

	/**
	 * 报价单生效时间
	 */
	private LocalDateTime validTime;

	/**
	 * 报价单失效时间
	 */
	private LocalDateTime invalidTime;

	/**
	 * 用户是否展示
	 */
	private Integer mallShow;

	/**
	 * 价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）3商城价上浮 4商城价下浮 5商城价加价 6商城价减价
	 */
	private Integer priceType;

	/**
	 * 毛利率的成本价
	 */
	private BigDecimal cost;

	/**
	 * 毛利率
	 */
	private BigDecimal interestRate;

	/**
	 * 毛利率的固定价
	 */
	private BigDecimal fixedPrice;

	/**
	 * 原售价
	 */
	private BigDecimal originalPrice;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 通过低价监控更新价格的时间
	 */
	private LocalDateTime lowPriceUpdateTime;

	

	
}