package net.summerfarm.manage.domain.product.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopBiaoguoProductsDfQueryParam;



/**
*
* <AUTHOR>
* @date 2024-12-12 11:19:19
* @version 1.0
*
*/
public interface AppPopBiaoguoProductsDfQueryRepository {

    PageInfo<AppPopBiaoguoProductsDfEntity> getPage(AppPopBiaoguoProductsDfQueryParam param);

    AppPopBiaoguoProductsDfEntity selectById(Long id);

    List<AppPopBiaoguoProductsDfEntity> selectByCondition(AppPopBiaoguoProductsDfQueryParam param);

    boolean exist(AppPopBiaoguoProductsDfQueryParam productsDfQueryParam);

    /**
     * 统计符合条件的数量
     *
     * <AUTHOR>
     * @date 2024/12/24 11:11
     * @param productsDfQueryParam 查询参数
     * @return int 数量
     */
    int count(AppPopBiaoguoProductsDfQueryParam productsDfQueryParam);
}