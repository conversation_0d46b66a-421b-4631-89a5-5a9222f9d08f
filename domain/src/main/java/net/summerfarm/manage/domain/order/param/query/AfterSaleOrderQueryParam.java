package net.summerfarm.manage.domain.order.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-01-18 16:06:21
 * @version 1.0
 *
 */
@Data
public class AfterSaleOrderQueryParam extends BasePageInput {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 售后编号
	 */
	private String afterSaleOrderNo;

	/**
	 * 售后用户
	 */
	private Long mId;

	/**
	 * 售后订单
	 */
	private String orderNo;

	/**
	 * 售后sku，为空则为整单退
	 */
	private String sku;

	/**
	 * 售后数量
	 */
	private Integer quantity;

	/**
	 * 售后原因
	 */
	private String afterSaleType;

	/**
	 * 照片凭证
	 */
	private String proofPic;

	/**
	 * 售后处理方式,0返券，1未知，2退款，3录入账单，4退货退款，5退货录入账单，6换货，7补发，8人工退款，9拒收退款，10拒收账单，11拦截退款，12拦截录入账单
	 */
	private Integer handleType;

	/**
	 * 补偿数量
	 */
	private BigDecimal handleNum;

	/**
	 * 处理人
	 */
	private String handler;

	/**
	 * 审核人
	 */
	private String auditer;

	/**
	 * 补充说明
	 */
	private String applyRemark;

	/**
	 * 售后状态：0、审核中
1、处理中
2、成功
3、失败
4、补充凭证
11、取消
12、退款中
	 */
	private Integer status;

	/**
	 * 处理结果
	 */
	private String handleRemark;

	/**
	 * 售后单位
	 */
	private String afterSaleUnit;

	/**
	 * 更新时间
	 */
	private LocalDateTime updatetime;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 0、普通售后 1、极速售后
	 */
	private Integer type;

	/**
	 * 
	 */
	private Integer grade;

	/**
	 * 0 未到货售后 1 已到货售后
	 */
	private Integer deliveryed;

	/**
	 * 
	 */
	private Integer times;

	/**
	 * 
	 */
	private Integer suitId;

	/**
	 * 
	 */
	private Integer view;

	/**
	 * 是否全额退款
	 */
	private Integer isFull;

	/**
	 * 售后发起子账号id
	 */
	private Long accountId;

	/**
	 * 配送计划id
	 */
	private Integer deliveryId;

	/**
	 * 是否需要回收费 0 不需要 1 需要
	 */
	private Integer recoveryType;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 售后类型
	 */
	private Integer afterSaleRemarkType;

	/**
	 * 售后类型备注
	 */
	private String afterSaleRemark;

	/**
	 * 售后单类型：0 普通售后单，1 拦截售后单
	 */
	private Integer afterSaleOrderStatus;

	/**
	 * 0为没退运费，1为退了运费
	 */
	private Integer refundFreight;

	/**
	 * 关闭售后单操作者
	 */
	private String closer;

	/**
	 * 关单时间
	 */
	private LocalDateTime closeTime;

	/**
	 * 商品类型：0、普通商品 1、赠品 2、换购商品
	 */
	private Integer productType;

	/**
	 * 补发是否带货回来：0没带，1带了
	 */
	private Integer carryingGoods;

	

	
}