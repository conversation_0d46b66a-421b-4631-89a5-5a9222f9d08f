package net.summerfarm.manage.domain.coupon.service;


import net.summerfarm.manage.domain.coupon.repository.MerchantCouponQueryRepository;
import net.summerfarm.manage.domain.coupon.repository.MerchantCouponCommandRepository;
import net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 用户-优惠券关联表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-31 15:37:46
 * @version 1.0
 *
 */
@Service
public class MerchantCouponQueryDomainService {


}
