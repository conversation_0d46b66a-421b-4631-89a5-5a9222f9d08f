package net.summerfarm.manage.domain.product.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 *
 */
@Data
public class FrontCategoryCommandParam {
	/**
	 * 主键、自增
	 */
	private Integer id;

	/**
	 * 父级id
	 */
	private Integer parentId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 有效标识 0、有效 1、失效
	 */
	private Integer outdated;

	/**
	 * 图标链接
	 */
	private String icon;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 类型，1 - xm商城;2 - pop商城
	 */
	private Integer fCategoryType;

	

	
}