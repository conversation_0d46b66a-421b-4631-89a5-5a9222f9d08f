package net.summerfarm.manage.domain.account.repository;



import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;




/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:20
* @version 1.0
*
*/
public interface ContactAdjustCommandRepository {

    ContactAdjustEntity insertSelective(ContactAdjustEntity entity);

    int updateByIdSelective(ContactAdjustEntity entity);

    int remove(Long id);

}