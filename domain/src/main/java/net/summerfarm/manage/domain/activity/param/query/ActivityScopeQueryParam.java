package net.summerfarm.manage.domain.activity.param.query;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: <EMAIL>
 * @create: 2022/12/9
 */
@Data
public class ActivityScopeQueryParam implements Serializable {

    public ActivityScopeQueryParam(Long scopeId, Integer scopeType) {
        this.scopeId = scopeId;
        this.scopeType = scopeType;
    }

    /**
     * 活动范围类型
     */
    private Integer scopeType;

    /**
     * 活动范围id
     */
    private Long scopeId;


}
