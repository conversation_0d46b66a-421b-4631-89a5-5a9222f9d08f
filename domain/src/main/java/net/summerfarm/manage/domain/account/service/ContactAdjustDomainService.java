package net.summerfarm.manage.domain.account.service;



import net.summerfarm.manage.domain.account.repository.ContactAdjustQueryRepository;
import net.summerfarm.manage.domain.account.repository.ContactAdjustCommandRepository;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 用户地址调整表领域层
 * @Description:
 * <AUTHOR>
 * @date 2023-10-26 16:20:20
 * @version 1.0
 *
 */
@Service
public class ContactAdjustDomainService {


    @Autowired
    private ContactAdjustCommandRepository contactAdjustCommandRepository;
    @Autowired
    private ContactAdjustQueryRepository contactAdjustQueryRepository;



    public ContactAdjustEntity insert(ContactAdjustEntity entity) {
        return contactAdjustCommandRepository.insertSelective(entity);
    }


    public Boolean update(ContactAdjustEntity entity) {
        contactAdjustCommandRepository.updateByIdSelective(entity);
        return true;
    }
}
