package net.summerfarm.manage.domain.activity.entity;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-31 14:13:04
 * @version 1.0
 *
 */
@Data
public class MarketRuleEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private String name;

	/**
	 * 活动类型
	 */
	private Integer type;

	/**
	 * 规则详情
	 */
	private String detail;

	/**
	 * 商城展示名称
	 */
	private String showName;

	/**
	 * 规则开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 规则结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 营销活动开放区域
	 */
	private String areaNo;

	/**
	 * 参与活动的品类
	 */
	private String categoryId;

	/**
	 * 参与活动的所有sku
	 */
	private String sku;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 
	 */
	private String ruleDetail;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 
	 */
	private Integer supportType;

	/**
	 * 返券规则  1-确认收货后（默认）  2-支付完成后
	 */
	private Integer couponRule;

	

	
}