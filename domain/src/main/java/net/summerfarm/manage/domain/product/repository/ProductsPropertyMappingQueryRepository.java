package net.summerfarm.manage.domain.product.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingRelationInfo;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyMappingQueryParam;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyMappingRelationQueryParam;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-05-07 16:18:00
* @version 1.0
*
*/
public interface ProductsPropertyMappingQueryRepository {

    PageInfo<ProductsPropertyMappingEntity> getPage(ProductsPropertyMappingQueryParam param);

    ProductsPropertyMappingEntity selectById(Long id);

    List<ProductsPropertyMappingEntity> selectByCondition(ProductsPropertyMappingQueryParam param);

    List<ProductsPropertyEntity> selectAnchoredProperty(int type, int mappingId);

    /**
     * 查询商品自定义属性映射关系列表
     *
     * <AUTHOR>
     * @date 2025/3/31 16:40
     */
    List<ProductsPropertyMappingRelationInfo> listProductsPropertyMapping(ProductsPropertyMappingRelationQueryParam queryParam);
}