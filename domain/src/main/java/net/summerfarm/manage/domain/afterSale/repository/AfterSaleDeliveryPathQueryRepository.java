package net.summerfarm.manage.domain.afterSale.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.afterSale.entity.AfterSaleDeliveryPathEntity;
import net.summerfarm.manage.domain.afterSale.flatObject.AfterSaleDeliveryPathFlatObject;
import net.summerfarm.manage.domain.afterSale.param.query.AfterSaleDeliveryPathQueryParam;



/**
*
* <AUTHOR>
* @date 2024-12-31 14:32:57
* @version 1.0
*
*/
public interface AfterSaleDeliveryPathQueryRepository {

    PageInfo<AfterSaleDeliveryPathEntity> getPage(AfterSaleDeliveryPathQueryParam param);

    AfterSaleDeliveryPathEntity selectById(Long id);

    List<AfterSaleDeliveryPathEntity> selectByCondition(AfterSaleDeliveryPathQueryParam param);

    /**
     * 查询售后配送详情
     * @param afterSaleNoList 售后单号集合
     */
    List<AfterSaleDeliveryPathFlatObject> queryValidAfterSaleDeliveryPathDetail(List<String> afterSaleNoList);
}