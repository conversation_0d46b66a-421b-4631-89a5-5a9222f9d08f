package net.summerfarm.manage.domain.order.service;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;
import net.summerfarm.manage.domain.order.repository.OrdersQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 *
 * @Title: 订单领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-18 15:49:06
 * @version 1.0
 *
 */
@Service
@Slf4j
public class OrdersQueryDomainService {
    @Resource
    private OrdersQueryRepository ordersQueryRepository;

    /**
     * 根据城配仓查询省心送代销不入仓订单购买SKU的总数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseSkuTotalNum(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuFlatObjects = ordersQueryRepository.queryTimingOrderProxySaleNoWarehouseSkuTotalNum(storeNo);
        log.info("queryTimingOrderProxySaleNoWarehouseSkuTotalNum req 城配仓编号:{},resp sku信息:{}",storeNo, JSON.toJSONString(skuFlatObjects));

        return skuFlatObjects;
    }

    /**
     * 根据城配仓查询省心送代销不入仓已设置的代销不如仓SKU的数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }

        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuHaveSetInfos = ordersQueryRepository.queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum(storeNo);
        log.info("queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum req 城配仓编号:{},resp sku信息:{}",storeNo, JSON.toJSONString(skuHaveSetInfos));

        return skuHaveSetInfos;
    }

    /**
     * 根据城配仓查询省心送代销不入仓售后的SKU的数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    public List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }

        List<TimingOrderProxySaleNoWarehouseSkuFlatObject> skuAfterInfos = ordersQueryRepository.queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum(storeNo);
        log.info("queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum req 城配仓编号:{},resp sku信息:{}",storeNo, JSON.toJSONString(skuAfterInfos));

        return skuAfterInfos;
    }
}
