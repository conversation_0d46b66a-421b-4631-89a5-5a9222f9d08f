package net.summerfarm.manage.domain.merchant.service;


import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 采购助手-常购清单表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Service
public class MerchantFrequentlyBuyingSkuQueryDomainService {


}
