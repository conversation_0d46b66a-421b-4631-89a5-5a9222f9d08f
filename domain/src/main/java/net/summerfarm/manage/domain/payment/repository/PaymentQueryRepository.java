package net.summerfarm.manage.domain.payment.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.payment.entity.PaymentEntity;
import net.summerfarm.manage.domain.payment.param.query.PaymentQueryParam;



/**
*
* <AUTHOR>
* @date 2024-12-23 17:01:31
* @version 1.0
*
*/
public interface PaymentQueryRepository {

    PageInfo<PaymentEntity> getPage(PaymentQueryParam param);

    PaymentEntity selectById(Long paymentId);

    List<PaymentEntity> selectByCondition(PaymentQueryParam param);

}