package net.summerfarm.manage.domain.activity.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-05-31 17:39:38
 * @version 1.0
 *
 */
@Data
public class MarketRuleHistoryQueryParam extends BasePageInput {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 营销详情表
	 */
	private String detail;

	/**
	 * 
	 */
	private String orderNo;

	/**
	 * 
	 */
	private Integer marketRuleId;

	/**
	 * 
	 */
	private Integer value;

	/**
	 * 
	 */
	private Integer ruleLevel;

	/**
	 * 0满返 1 满减
	 */
	private Integer type;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 满返发放状态  0：待发放 1：发放中  2：已发放  3：已撤回
	 */
	private Integer sendStatus;

	

	
}