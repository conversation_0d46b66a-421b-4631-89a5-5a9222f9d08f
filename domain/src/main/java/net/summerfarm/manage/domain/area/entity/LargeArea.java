package net.summerfarm.manage.domain.area.entity;

import io.swagger.annotations.ApiModel;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(description = "运营大区实体类")
@Data
@NoArgsConstructor
public class LargeArea {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 运营大区编号
     */
    private Integer largeAreaNo;

    /**
     * 名称
     */
    private String largeAreaName;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 开放状态，0：未开发，1：开放中
     */
    private Integer status;

    /**
     * 负责人ID，admin.admin_id
     */
    private Integer manageAdminId;

    /**
     * 运营大区负责人
     */
    private String adminName;
}

