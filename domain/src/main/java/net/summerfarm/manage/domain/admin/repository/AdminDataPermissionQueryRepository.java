package net.summerfarm.manage.domain.admin.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;



/**
*
* <AUTHOR>
* @date 2024-06-18 23:13:51
* @version 1.0
*
*/
public interface AdminDataPermissionQueryRepository {

    PageInfo<AdminDataPermissionEntity> getPage(AdminDataPermissionQueryParam param);

    AdminDataPermissionEntity selectById(Long id);

    List<AdminDataPermissionEntity> selectByCondition(AdminDataPermissionQueryParam param);

}