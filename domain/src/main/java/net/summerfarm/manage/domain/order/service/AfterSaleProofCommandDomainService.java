package net.summerfarm.manage.domain.order.service;


import net.summerfarm.manage.domain.order.repository.AfterSaleProofQueryRepository;
import net.summerfarm.manage.domain.order.repository.AfterSaleProofCommandRepository;
import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import net.summerfarm.manage.domain.order.param.command.AfterSaleProofCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-18 16:27:13
 * @version 1.0
 *
 */
@Service
public class AfterSaleProofCommandDomainService {


    @Autowired
    private AfterSaleProofCommandRepository afterSaleProofCommandRepository;
    @Autowired
    private AfterSaleProofQueryRepository afterSaleProofQueryRepository;



    public AfterSaleProofEntity insert(AfterSaleProofCommandParam param) {
        return afterSaleProofCommandRepository.insertSelective(param);
    }


    public int update(AfterSaleProofCommandParam param) {
        return afterSaleProofCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return afterSaleProofCommandRepository.remove(id);
    }
}
