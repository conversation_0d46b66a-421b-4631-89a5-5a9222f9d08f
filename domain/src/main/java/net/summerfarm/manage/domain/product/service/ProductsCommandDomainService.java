package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.ProductsQueryRepository;
import net.summerfarm.manage.domain.product.repository.ProductsCommandRepository;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 商品领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-07 11:31:28
 * @version 1.0
 *
 */
@Service
public class ProductsCommandDomainService {


    @Autowired
    private ProductsCommandRepository productsCommandRepository;
    @Autowired
    private ProductsQueryRepository productsQueryRepository;



    public ProductsEntity insert(ProductsCommandParam param) {
        return productsCommandRepository.insertSelective(param);
    }


    public int update(ProductsCommandParam param) {
        return productsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long pdId) {
        return productsCommandRepository.remove(pdId);
    }
}
