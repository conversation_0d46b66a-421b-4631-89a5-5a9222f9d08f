package net.summerfarm.manage.domain.order.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.flatObject.TimingOrderProxySaleNoWarehouseSkuFlatObject;
import net.summerfarm.manage.domain.order.param.query.OrdersQueryParam;

import java.math.BigDecimal;
import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-01-18 15:49:06
* @version 1.0
*
*/
public interface OrdersQueryRepository {

    PageInfo<OrdersEntity> getPage(OrdersQueryParam param);

    OrdersEntity selectById(Long orderId);

    List<OrdersEntity> selectByCondition(OrdersQueryParam param);

    OrdersEntity selectByOrderNo(String orderNo);

    /***
     * @author: lzh
     * @description: 获取用户在指定月份的消费总额
     * @date: 2024/1/23 16:36
     * @param: [ordersQueryParam]
     * @return: java.math.BigDecimal
     **/
    BigDecimal selectTotalPriceByMonth(OrdersQueryParam ordersQueryParam);

    /***
     * @author: lzh
     * @description: 批量获取订单号信息
     * @date: 2024/1/23 16:36
     * @param: [subOrderNos]
     * @return: java.util.List<net.summerfarm.manage.domain.order.entity.OrdersEntity>
     **/
    List<OrdersEntity> batchGetOrderNos(List<String> subOrderNos);

    /***
     * @author: lzh
     * @description: 获取用户下单数量
     * @date: 2024/1/23 17:09
     * @param: [mId]
     * @return: int
     **/
    int selectCountByMId(Long mId);

    /**
     * 根据城配仓查询省心送代销不入仓订单购买SKU的总数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseSkuTotalNum(Integer storeNo);

    /**
     * 根据城配仓查询省心送代销不入仓已设置的代销不如仓SKU的数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseHaveSetSkuNum(Integer storeNo);

    /**
     * 根据城配仓查询省心送代销不入仓售后的SKU的数量
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<TimingOrderProxySaleNoWarehouseSkuFlatObject> queryTimingOrderProxySaleNoWarehouseAfterSaleSkuNum(Integer storeNo);

    /**
     * 获取待收货的订单信息
     * @param
     * @return 结果
     */
    List<OrdersEntity> getPendingOrders(String orderNo, int pageStart, int pageSize);
}