package net.summerfarm.manage.domain.product.service;


import jodd.util.StringUtil;
import net.summerfarm.manage.common.util.VolumeUtil;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;
import net.summerfarm.manage.domain.product.repository.InventoryCommandRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 商品SKU表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-06 16:02:27
 * @version 1.0
 *
 */
@Service
public class InventoryCommandDomainService {


    @Autowired
    private InventoryCommandRepository inventoryCommandRepository;


    public InventoryEntity insert(InventoryCommandParam param) {
        return inventoryCommandRepository.insertSelective(param);
    }


    public int update(InventoryCommandParam param) {
        return inventoryCommandRepository.updateSelectiveBySku(param);
    }

    public int updateWithNull(InventoryCommandParam param) {
        String skuPic = StringUtils.isBlank (param.getSkuPic ())?null:param.getSkuPic ();
        String videoUrl = StringUtils.isBlank (param.getVideoUrl ())?null:param.getVideoUrl ();
        param.setSkuPic (skuPic);
        param.setVideoUrl (videoUrl);
        return inventoryCommandRepository.updateWithNull(param);
    }
}
