package net.summerfarm.manage.domain.activity.valueObject;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ActivitySkuDetailValueObject implements Serializable {

    private Long id;

    /**
     * 商品配置id
     */
    private Long itemConfigId;


    /**
     * 基本信息id
     */
    private Long basicInfoId;

    /**
     * 活动sku
     */
    private String sku;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 阶梯配置
     */
    private String ladderConfig;

    /**
     * 活动创建时间（basic表的创建时间）
     */
    private Date activityCreateTime;

    /**
     * 范围id （人群包：merchant_pool_info主键ID，运营城市：areaNo，运营大区：largeAreaNo）
     */
    private Long scopeId;

    /**
     * 活动范围类型，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;
}
