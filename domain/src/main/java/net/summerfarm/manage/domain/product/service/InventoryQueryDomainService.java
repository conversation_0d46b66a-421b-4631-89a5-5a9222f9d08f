package net.summerfarm.manage.domain.product.service;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.repository.InventoryCommandRepository;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.repository.other.dto.Inventory;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 *
 * @Title: 商品SKU表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-06 16:02:27
 * @version 1.0
 *
 */
@Service
@Slf4j
public class InventoryQueryDomainService {


    @Autowired
    private InventoryQueryRepository inventoryQueryRepository;

    public Integer selectMinSaleUnit(String sku) {
        if(StringUtils.isBlank(sku)) {
            log.error("sku:{}不存在!", sku);
            throw new BizException("sku不存在!");
        }
        InventoryQueryParam param = new InventoryQueryParam();
        param.setSku(sku);
        InventoryEntity entity = inventoryQueryRepository.querySelectOne(param);
        if(entity == null) {
            log.error("sku:{}不存在!", sku);
            throw new BizException("sku不存在!");
        }
        return entity.getBaseSaleUnit() * entity.getBaseSaleQuantity();
    }
}
