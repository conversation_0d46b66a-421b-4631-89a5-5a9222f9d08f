package net.summerfarm.manage.domain.account.repository;



import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import net.summerfarm.manage.common.input.account.ContactAdjustQueryInput;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:20
* @version 1.0
*
*/
public interface ContactAdjustQueryRepository {


    ContactAdjustEntity selectById(Long id);

    ContactAdjustEntity selectOneByEntity(ContactAdjustEntity entity);

    List<ContactAdjustEntity> selectByCondition(MerchantQueryInput input);

}