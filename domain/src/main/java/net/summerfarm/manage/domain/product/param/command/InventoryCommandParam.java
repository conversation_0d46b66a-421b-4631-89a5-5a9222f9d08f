package net.summerfarm.manage.domain.product.param.command;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024-05-06 16:02:27
 * @version 1.0
 *
 */
@Data
public class InventoryCommandParam {
	/**
	 * 
	 */
	private Long invId;

	/**
	 * 产品编号
	 */
	private String sku;

	/**
	 * 仓库(实际)库存
	 */
	private Integer storeQuantity;

	/**
	 * 限制购买数量
	 */
	private Integer limitedQuantity;

	/**
	 * 属性ID
	 */
	private Integer aitId;

	/**
	 * 
	 */
	private Long pdId;

	/**
	 * 产地
	 */
	private String origin;

	/**
	 * 单位
	 */
	private String unit;

	/**
	 * 生熟度
	 */
	private String maturity;

	/**
	 * 包数
	 */
	private String pack;

	/**
	 * 重量
	 */
	private String weight;

	/**
	 * 生产日期
	 */
	private Date productionDate;

	
	/**
	 * 贮存方式(作废)
	 */
	private String storageMethod;

	/**
	 * 广告语
	 */
	private String slogan;

	/**
	 * 销售量
	 */
	private Integer saleCount;

	/**
	 * 销售模式；0普通，1团购，2日限购，3永久限购，4新手限购，5订单限购，6定单价函数限购
	 */
	private Integer salesMode;

	/**
	 * 警戒库存
	 */
	private Integer alertInventory;

	/**
	 * 安全库存
	 */
	private Integer safeInventory;

	/**
	 * 销售价
	 */
	private BigDecimal salePrice;

	/**
	 * 促销价
	 */
	private BigDecimal promotionPrice;

	/**
	 * 商品介绍
	 */
	private String introduction;

	/**
	 * 售后最大数量
	 */
	private Integer afterSaleQuantity;

	/**
	 * SKU生命周期：-1、上新处理中 0、使用中 1、已删除
	 */
	private Integer outdated;

	/**
	 * 最小起售量
	 */
	private Integer baseSaleQuantity;

	/**
	 * 售卖规格
	 */
	private Integer baseSaleUnit;

	/**
	 * 体积(长*宽*高)
	 */
	private String volume;

	/**
	 * 重量kg
	 */
	private BigDecimal weightNum;

	/**
	 * 类型 0 自营 1 代仓
	 */
	private Integer type;

	/**
	 * 所属大客户ID
	 */
	private Integer adminId;

	/**
	 *  0 不加入 1加入
	 */
	private Integer samplePool;

	/**
	 * sku图
	 */
	private String skuPic;

	/**
	 * 售后单位
	 */
	private String afterSaleUnit;

	/**
	 * 添加时间
	 */
	private LocalDateTime addTime;

	/**
	 * 
	 */
	private LocalDateTime updateTime;

	/**
	 * 上新审核状态：0、待上新 1、上新成功 2、上新失败
	 */
	private Integer auditStatus;

	/**
	 * 上新审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 创建人adminId
	 */
	private Integer creator;

	/**
	 * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
	 */
	private Integer extType;

	/**
	 * 
	 */
	private String createRemark;

	/**
	 * 任务类型：0、SPU 1、SKU
	 */
	private Integer taskType;

	/**
	 * 上新类型：0、平台 1、大客户 2、saas自营&代仓 3、仅saas自营
	 */
	private Integer createType;

	/**
	 * 操作人adminId
	 */
	private Integer auditor;

	/**
	 * 规格备注
	 */
	private String weightNotes;

	/**
	 * 是否为国产，0：不是，1是
	 */
	private Integer isDomestic;

	/**
	 * 供应商是否可见：0不可见，1可见
	 */
	private Integer supplierVisible;

	/**
	 * 0 不展示平均价  1 展示平均价
	 */
	private Integer averagePriceFlag;

	/**
	 * sku名称
	 */
	private String skuName;

	/**
	 * 拒绝原因
	 */
	private String refuseReason;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
	 */
	private Integer subType;

	/**
	 * 是否展示
	 */
	private Boolean show;

	/**
	 * 工商名称
	 */
	private String realName;

	/**
	 * 买手ID
	 */
	private Long buyerId;

	/**
	 * 买手名称
	 */
	private String buyerName;

	/**
	 * 净重
	 */
	private BigDecimal netWeightNum;

	/**
	 * 净重
	 */
	private String netWeightUnit;

	/**
	 * 视频链接
	 */
	private String videoUrl;

	/**
	 * 售后规则详情
	 */
	private String afterSaleRuleDetail;


	/**
	 * 视频上传人
	 */
	private String videoUploadUser;
	/**
	 * 视频上传时间
	 */
	private LocalDateTime videoUploadTime;

	/**
	 * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
	 */
	private Integer quoteType;

	/**
	 * 自动补差售后量阈值
	 */
	private Integer minAutoAfterSaleThreshold;
	
}